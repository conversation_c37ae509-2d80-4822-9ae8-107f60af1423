<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Models\Merchant;
use App\Http\Resources\BaseCollection;

class ExamineController extends Controller
{
    /**
     * 考核列表
     */
    public function index(Request $request)
    {
        $model = Merchant::with([
            'store',
            'marketManager' => function ($query) {
                $query->select('id', 'name');
            },
            'province',
            'city',
            'area'
        ]);

        if ($request->has('search')) {
            $model = $this->addScope($model, $request->input('search', []));
        }

        if($request->has('sales_target_volume')) {
            $model = $model->salesTargetVolume($request->input('sales_target_volume'));
        }

        $data = $model->where('merchant.progress_status', 4)->where('merchant.daily_sales_target', '>', 0)->orderBy('merchant.created_at', 'desc')->paginate($request->input('pageSize', 10));

        $data->getCollection()->transform(function ($item) {
            // 获取上线天数
            $onlineDays = now()->diffInDays($item->online_at);
            $item->daily_avg_amount = $item->getDailyAvgAmount($onlineDays);
            return $item;
        });

        return new BaseCollection($data);
    }

    public function updateExamine(Request $request, Merchant $merchant)
    {
        if ($merchant->progress_status != 4) {
            return ['status' => false, 'message' => '该商户不是已上线状态'];
        }
        if ($merchant->store_id == 0) {
            return ['status' => false,'message' => '该商户未关联线上店铺'];
        }

        $merchant->daily_sales_target = $request->input('daily_sales_target', 0);
        $merchant->save();

        return ['status' => true, 'message' => '设置考核成功'];
    }
}
