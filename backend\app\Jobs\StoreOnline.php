<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Merchant;
use App\Models\Store;
use Illuminate\Support\Facades\DB;
use Log;

class StoreOnline implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $merchant;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Merchant $merchant)
    {
        $this->merchant = $merchant->load('settlement', 'oilPrices');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $storeData = [
            'stor_title' => $this->merchant->merchant_name,
            'stor_provin' => $this->merchant->province_code,
            'stor_city' => $this->merchant->city_code,
            'stor_area' => $this->merchant->area_code,
            'stor_address' => $this->merchant->address,
            'stor_lng' => $this->merchant->longitude,
            'stor_lat' => $this->merchant->latitude,
            'stor_paytype' => 2,
            'split_type' => 1,
            'settlement_type' => 1,
            'stor_pic' => $this->merchant->store_photos,
            'stor_statu' => 1
        ];

        DB::beginTransaction();

        try {
            $model = new Store();

            $store = $model->create($storeData);

            // 更新结算信息
            if($this->merchant->settlement) {
                $settlement = [
                    //'store_id' => $store->id
                    'type' => $this->merchant->settlement->settlement_type == 0 ? 1 : 0,
                    'card_number' => $this->merchant->settlement->bank_account,
                    'bank_name' => $this->merchant->settlement->bank_name,
                    'name' => $this->merchant->settlement->account_holder,
                    'mobile' => $this->merchant->settlement->bank_phone,
                ];

                $store->account()->create($settlement);
            }

            // 更新油价
            if($this->merchant->oil_prices && sizeof($this->merchant->oil_prices) > 0) {
                $priceData = [];
                foreach($this->merchant->oil_prices as $v) {
                    $priceData[] = [
                        'shop_type_id' => $v->oil_type == 0 ? 1 : ($v->oil_type == 1 ? 2 : 5),
                        'shop_price' => $v->price
                    ];
                }
                $store->shop()->create($priceData);
            }

            // 更新支付信息
            $payData = [
                'pinf_type' => 2,
                'config' => json_encode([
                    'mid' => ***************,
                    'rate' => 0,
                    'ysf_mid' => ***************,
                    'ysf_rate' => 0,
                    'ali_mid' => ***************,
                    'ali_rate' => 0,
                    'ali_discount' => null
                ])
            ];

            $store->payInfo()->create($priceData);

            DB::commit();

            Log::info('商户CRM同步成功:' . $this->merchant->id);

        } catch (\Exception $e) {
            DB::beginTransaction();
            Log::info('商户CRM同步失败:' . $e->getMessage());
        }
    }
}
