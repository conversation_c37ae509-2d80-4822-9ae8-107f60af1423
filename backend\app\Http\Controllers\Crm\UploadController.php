<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use OSS\OssClient;
use OSS\Core\OssException;
use Intervention\Image\Facades\Image;
use FFMpeg\FFMpeg;
use FFMpeg\Coordinate\TimeCode;
use App\Utils\SignatureStamp;
use Illuminate\Support\Facades\Log;

class UploadController extends Controller
{
    private $ossClient;
    private $bucket;
    private $endpoint;
    private $maxFileSize = 1024 * 1024 * 1024; // 200M
    private $allowedImageTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/jpg',  // 某些设备可能使用这种MIME类型
        'image/heic', // iOS HEIC格式
        'image/heif'  // iOS HEIF格式
    ];
    private $allowedVideoTypes = [
        'video/mp4',
        'video/quicktime',
        'video/x-quicktime', // 某些设备可能使用这种MIME类型
        'video/3gpp',       // 移动设备常见格式
        'video/x-msvideo'   // 某些设备可能使用这种MIME类型
    ];

    // 验证文件分辨率（图片最小800x600，视频最小640x360）
    private $minImageWidth = 800;
    private $minImageHeight = 600;
    private $minVideoWidth = 640;
    private $minVideoHeight = 360;

    public function __construct()
    {
        $this->bucket = config('filesystems.disks.oss.bucket');
        $this->endpoint = config('filesystems.disks.oss.endpoint');
        $this->ossClient = new OssClient(
            config('filesystems.disks.oss.key'),
            config('filesystems.disks.oss.secret'),
            $this->endpoint,
        );
    }

    /**
     * 根据文件类型获取扩展名
     */
    private function getFileExtension($mimeType)
    {
        $extensions = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'video/mp4' => 'mp4',
            'video/quicktime' => 'mov'
        ];

        return $extensions[$mimeType] ?? 'jpg';
    }

    /**
     * 验证文件类型
     */
    private function validateFileType($mimeType)
    {
        return in_array($mimeType, array_merge($this->allowedImageTypes, $this->allowedVideoTypes));
    }

    /**
     * 上传文件
     */
    public function upload(Request $request)
    {
        try {
            // 验证文件
            $validator = Validator::make($request->all(), [
                'file' => 'required|file',
            ]);

            if ($validator->fails()) {
                Log::info('上传失败: 参数错误');
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first()
                ], 422);
            }

            $file = $request->file('file');
            $mimeType = $file->getMimeType();

            // 验证文件类型
            if (!in_array($mimeType, array_merge($this->allowedImageTypes, $this->allowedVideoTypes))) {
                Log::info('上传失败: 不支持的文件类型');
                return response()->json([
                    'status' => false,
                    'message' => '不支持的文件类型'
                ], 422);
            }

            // 验证文件大小
            if ($file->getSize() > $this->maxFileSize) {
                Log::info('上传失败: 文件大小超过限制');
                return response()->json([
                    'status' => false,
                    'message' => '文件大小超过限制'
                ], 422);
            }

            // 验证分辨率
            if (!$this->validateResolution($file, $mimeType)) {
                Log::info('上传失败: 文件分辨率不符合要求');
                return response()->json([
                    'status' => false,
                    'message' => '文件分辨率不符合要求'
                ], 422);
            }

            // 生成文件名
            $extension = $file->getClientOriginalExtension();
            $fileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $path = 'uploads/' . date('Ym') . '/' . $fileName;

            // 上传到OSS
            $this->ossClient->putObject($this->bucket, $path, file_get_contents($file->getPathname()));

            // 获取文件URL（不带有效期）
            $fileUrl = 'https://' . $this->bucket . '.' . 'oss-cn-huhehaote.aliyuncs.com' . '/' . $path;

            $responseData = [
                'url' => $fileUrl,
                'path' => $path,
            ];

            // 如果是视频，获取视频时长和封面图
            if (in_array($mimeType, $this->allowedVideoTypes)) {
                $videoInfo = $this->getVideoInfo($file);
                $responseData['duration'] = $videoInfo['duration'];
                $responseData['thumbnail'] = $videoInfo['thumbnail'];
            }

            return response()->json([
                'status' => true,
                'message' => '上传成功',
                'data' => $responseData
            ]);

        } catch (OssException $e) {
            Log::info('上传失败:' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '上传失败：' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            Log::info('上传失败:' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => '上传失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 验证文件分辨率
     */
    private function validateResolution($file, $mimeType)
    {
        try {
            if (in_array($mimeType, $this->allowedImageTypes)) {
                $image = Image::make($file);
                Log::info('上传文件分辨率:' . $image->width() . '*' . $image->height());
                return $image->width() >= $this->minImageWidth && 
                       $image->height() >= $this->minImageHeight;
            }

            $ffmpegPath = storage_path('ffmpeg') . '/ffmpeg';
            $ffprobePath = storage_path('ffmpeg') . '/ffprobe';
            
            Log::info('FFmpeg配置:', [
                'ffmpeg_path' => $ffmpegPath,
                'ffprobe_path' => $ffprobePath,
                'ffmpeg_exists' => file_exists($ffmpegPath),
                'ffprobe_exists' => file_exists($ffprobePath),
                'ffmpeg_executable' => is_executable($ffmpegPath),
                'ffprobe_executable' => is_executable($ffprobePath),
                'php_user' => get_current_user(),
                'php_version' => PHP_VERSION
            ]);

            if (in_array($mimeType, $this->allowedVideoTypes)) {
                $ffmpeg = FFMpeg::create([
                    'ffmpeg.binaries' => $ffmpegPath,
                    'ffprobe.binaries' => $ffprobePath
                ]);
                $video = $ffmpeg->open($file->getPathname());
                $streams = $video->getStreams();
                
                // 获取视频流
                $videoStream = $streams->videos()->first();
                if (!$videoStream) {
                    return false;
                }

                return $videoStream->getDimensions()->getWidth() >= $this->minVideoWidth && 
                       $videoStream->getDimensions()->getHeight() >= $this->minVideoHeight;
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('Failed to validate resolution: ' . $e->getMessage());
            // 如果无法获取分辨率，返回true以允许上传
            return true;
        }
    }

    /**
     * 获取视频信息（时长和封面图）
     */
    private function getVideoInfo($file)
    {
        try {
            // 确保临时目录存在
            $tempDir = storage_path('app/temp');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0777, true);
            }

            $ffmpeg = FFMpeg::create([
                'ffmpeg.binaries' => storage_path('ffmpeg') . '/ffmpeg',
                'ffprobe.binaries' => storage_path('ffmpeg') . '/ffprobe'
            ]);
            
            $video = $ffmpeg->open($file->getPathname());
            
            // 获取视频时长（秒）
            $duration = 0;
            $streams = $video->getStreams();
            $videoStream = $streams->videos()->first();
            if ($videoStream) {
                $duration = $videoStream->get('duration');
            }

            // 生成封面图
            $thumbnailName = date('YmdHis') . '_' . uniqid() . '_thumb.jpg';
            $thumbnailPath = 'uploads/' . date('Ym') . '/' . $thumbnailName;
            
            // 从视频中截取第一帧作为封面
            $frame = $video->frame(TimeCode::fromSeconds(0));
            $tempPath = $tempDir . '/' . $thumbnailName;
            
            // 确保临时文件目录可写
            if (!is_writable($tempDir)) {
                chmod($tempDir, 0777);
            }
            
            // 保存封面图
            $frame->save($tempPath, true);

            // 检查封面图是否成功生成
            if (!file_exists($tempPath)) {
                throw new \Exception('Failed to generate thumbnail');
            }

            // 上传封面图到OSS
            $this->ossClient->putObject($this->bucket, $thumbnailPath, file_get_contents($tempPath));
            
            // 删除临时文件
            @unlink($tempPath);

            // 获取封面图URL
            $thumbnailUrl = 'https://' . $this->bucket . '.' . 'oss-cn-huhehaote.aliyuncs.com' . '/' . $thumbnailPath;

            return [
                'duration' => round($duration, 2), // 保留两位小数
                'thumbnail' => $thumbnailUrl
            ];
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('Failed to get video info: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'error' => $e->getMessage()
            ]);

            // 如果获取视频信息失败，尝试使用ffprobe直接获取时长
            try {
                $duration = shell_exec("ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 " . escapeshellarg($file->getPathname()));
                $duration = floatval(trim($duration));
            } catch (\Exception $e) {
                $duration = 0;
            }

            return [
                'duration' => round($duration, 2),
                'thumbnail' => ''
            ];
        }
    }

    public function uploadContract(Request $request)
    {
        if (!$request->hasFile('pdf_file')) {
            return ['status' => false, 'message' => '没有可上传文件'];
        }

        $file = $request->file('pdf_file');
        if (!$file->isValid() || $file->getMimeType() !== 'application/pdf') {
            return ['status' => false, 'message' => '上传文件必须pdf格式'];
        }

        $directory = storage_path('static/tmp');
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        $originalFileName = time() . '_' . $file->getClientOriginalName();
        $originalPath = $directory . '/' . $originalFileName;
        $file->move($directory, $originalFileName);

        try {
            $signatureStamp = new SignatureStamp();
            $stampedPdfPath = $signatureStamp->AddSignatureStamp($originalPath);

            $object = 'contracts/' . basename($stampedPdfPath);
            $this->ossClient->uploadFile($this->bucket, $object, $stampedPdfPath);

            $ossUrl = 'https://' . $this->bucket . '.' . $this->endpoint . '/' . $object;

            @unlink($originalPath);
            @unlink($stampedPdfPath);

            return ['status' => true, 'url' => $ossUrl];
        } catch (OssException $e) {
            return ['status' => false, 'message' => 'OSS上传错误: ' . $e->getMessage()];
        } catch (\Exception $e) {
            return ['status' => false, 'message' => '文件执行错误: ' . $e->getMessage()];
        }
    }

    /**
     * 分块上传接口
     */
    public function uploadChunk(Request $request)
    {
        ini_set('upload_max_filesize', '22M');
        ini_set('post_max_size', '22M');
        set_time_limit(0);

        $uploadId = $request->input('uploadId');
        $chunkIndex = $request->input('chunkIndex');
        $totalChunks = $request->input('totalchunks');
        $chunkData = $request->input('chunkData');


        if (empty($uploadId) || !is_numeric($chunkIndex) || !is_numeric($totalChunks) || empty($chunkData)) {
            return response()->json(['status' => false, 'message' => '缺少必要的参数']);
        }

        if (!base64_decode($chunkData, true)) {
            return response()->json(['status' => false, 'message' => '无效的分块数据']);
        }

        $chunkContent = base64_decode($chunkData);
        $chunkDir = storage_path('chunks/' . $uploadId);
        if (!file_exists($chunkDir)) {
            mkdir($chunkDir, 0777, true);
        }

        $chunkPath = $chunkDir . '/chunk_' . $chunkIndex;
        file_put_contents($chunkPath, $chunkContent);

        if ($this->checkAllChunksUploaded($uploadId, $totalChunks)) {
            return response()->json(['status' => true, 'message' => '分块上传完成，可以开始合并']);
        }

        return response()->json(['status' => true, 'message' => '分块上传成功']);
    }

    /**
     * 检查是否所有分块都已上传
     */
    private function checkAllChunksUploaded($uploadId, $totalChunks)
    {
        $chunkDir = storage_path('chunks/' . $uploadId);

        for ($i = 0; $i < $totalChunks; $i++) {
            $chunkPath = $chunkDir . '/chunk_' . $i;
            if (!file_exists($chunkPath)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 合并分块并上传到OSS
     */
    public function mergeChunks(Request $request)
    {
        // 临时增加内存限制
        ini_set('memory_limit', '1024M');
        $uploadId = $request->input('uploadId');
        $totalChunks = $request->input('totalChunks');
        $mimeType = $request->input('mimeType');
    
        if (empty($uploadId) || !is_numeric($totalChunks) || empty($mimeType)) {
            return response()->json(['status' => false, 'message' => '缺少必要的参数']);
        }
    
        // 验证是否为视频类型
        if (!in_array($mimeType, $this->allowedVideoTypes)) {
            return response()->json(['status' => false, 'message' => '分块合并仅支持视频文件']);
        }
    
        $chunkDir = storage_path('chunks/' . $uploadId);
        if (!file_exists($chunkDir)) {
            return response()->json(['status' => false, 'message' => '分块目录不存在']);
        }
    
        $chunkFiles = glob($chunkDir . '/chunk_*');
    
        if (count($chunkFiles) !== (int)$totalChunks) {
            return response()->json(['status' => false, 'message' => '分块文件数量不匹配']);
        }
    
        sort($chunkFiles);
    
        // 从 MIME 类型获取文件扩展名
        $extension = $this->getFileExtension($mimeType);
    
        // 合并分块
        $mergedPath = $chunkDir . '/merged_' . $uploadId . '.' . $extension;
        $mergedFile = fopen($mergedPath, 'wb');
    
        if (!$mergedFile) {
            return response()->json(['status' => false, 'message' => '无法打开合并文件']);
        }
    
        foreach ($chunkFiles as $chunkFile) {
            $chunkHandle = fopen($chunkFile, 'rb');
            if ($chunkHandle) {
                // 逐块读取并写入合并文件
                while (!feof($chunkHandle)) {
                    $buffer = fread($chunkHandle, 8192); // 每次读取 8KB
                    fwrite($mergedFile, $buffer);
                }
                fclose($chunkHandle);
                unlink($chunkFile);
            }
        }
    
        fclose($mergedFile);
    
        // 创建临时目录来存放合并后的文件
        $tempDir = storage_path('temp_merged_files');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0777, true);
        }
        $tempMergedPath = $tempDir . '/' . basename($mergedPath);
        rename($mergedPath, $tempMergedPath);
    
        // 使用 deleteDirectory 方法递归删除目录
        $this->deleteDirectory($chunkDir);
    
        // 检查合并后的文件是否存在
        if (!file_exists($tempMergedPath)) {
            return response()->json(['status' => false, 'message' => '合并后的文件不存在，无法验证文件大小']);
        }
    
        // 验证文件大小
        $fileSize = filesize($tempMergedPath);
        if ($fileSize > $this->maxFileSize) {
            unlink($tempMergedPath);
            return response()->json(['status' => false, 'message' => '文件大小超过限制']);
        }

        $videoInfo = [];

        // 如果是视频，获取视频时长和封面图
        if (in_array($mimeType, $this->allowedVideoTypes)) {
            try {
                // 确保临时目录存在
                $tempDir = storage_path('app/temp');
                if (!file_exists($tempDir)) {
                    mkdir($tempDir, 0777, true);
                }

                $ffmpeg = FFMpeg::create([
                    'ffmpeg.binaries' => storage_path('ffmpeg') . '/ffmpeg',
                    'ffprobe.binaries' => storage_path('ffmpeg') . '/ffprobe'
                ]);
                
                $video = $ffmpeg->open($tempMergedPath);
                
                // 获取视频时长（秒）
                $duration = 0;
                $streams = $video->getStreams();
                $videoStream = $streams->videos()->first();
                if ($videoStream) {
                    $duration = $videoStream->get('duration');
                }

                // 生成封面图
                $thumbnailName = date('YmdHis') . '_' . uniqid() . '_thumb.jpg';
                $thumbnailPath = 'uploads/' . date('Ym') . '/' . $thumbnailName;
                
                // 从视频中截取第一帧作为封面
                $frame = $video->frame(TimeCode::fromSeconds(0));
                $tempPath = $tempDir . '/' . $thumbnailName;
                
                // 确保临时文件目录可写
                if (!is_writable($tempDir)) {
                    chmod($tempDir, 0777);
                }
                
                // 保存封面图
                $frame->save($tempPath, true);

                // 检查封面图是否成功生成
                if (!file_exists($tempPath)) {
                    throw new \Exception('Failed to generate thumbnail');
                }

                // 上传封面图到OSS
                $this->ossClient->putObject($this->bucket, $thumbnailPath, file_get_contents($tempPath));
                
                // 删除临时文件
                @unlink($tempPath);

                // 获取封面图URL
                $thumbnailUrl = 'https://' . $this->bucket . '.' . 'oss-cn-huhehaote.aliyuncs.com' . '/' . $thumbnailPath;

                $videoInfo['duration'] = round($duration, 2);
                $videoInfo['thumbnail'] = $thumbnailUrl;
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error('Failed to get video info: ' . $e->getMessage(), [
                    'file' => basename($tempMergedPath),
                    'error' => $e->getMessage()
                ]);

                // 如果获取视频信息失败，尝试使用ffprobe直接获取时长
                try {
                    $duration = shell_exec("ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 " . escapeshellarg($tempMergedPath));
                    $duration = floatval(trim($duration));
                } catch (\Exception $e) {
                    $duration = 0;
                }

                $videoInfo['duration'] = round($duration, 2);
                $videoInfo['thumbnail'] = '';
            }
        }

        // 生成文件名
        $fileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
        $path = 'uploads/' . date('Ym') . '/' . $fileName;

        // 上传到OSS，改回原来的方式
        try {
            $this->ossClient->putObject($this->bucket, $path, file_get_contents($tempMergedPath));
        } catch (OssException $e) {
            Log::error('OSS 上传错误: ' . $e->getMessage());
            unlink($tempMergedPath);
            return response()->json(['status' => false, 'message' => 'OSS 上传错误: ' . $e->getMessage()]);
        }

        // 获取文件URL（不带有效期）
        $fileUrl = 'https://' . $this->bucket . '.' . 'oss-cn-huhehaote.aliyuncs.com' . '/' . $path;
    
        $responseData = [
            'url' => $fileUrl,
            'path' => $path,
        ];
    
        unlink($tempMergedPath);

        $data = array_merge($responseData, $videoInfo);
    
        return response()->json([
            'status' => true,
            'message' => '文件上传成功',
            'data' => $data
        ]);
    }

    /**
     * 删除目录及其所有内容
     */
    private function deleteDirectory($dir)
    {
        if (!file_exists($dir)) {
            return true;
        }

        if (!is_dir($dir)) {
            return unlink($dir);
        }

        foreach (scandir($dir) as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }

            if (!$this->deleteDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
                return false;
            }
        }

        return rmdir($dir);
    }

    public function uploadTestChunk(Request $request)
    {
        return view('crm.chunk');
    }
}