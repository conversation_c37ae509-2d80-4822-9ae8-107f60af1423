<?php

namespace App\Utils;

class Permission
{
    public static $permission = [
        "system.user.view" => '查看管理员',
        "system.user.add" => '添加管理员',
        "system.user.edit" => '编辑管理员',
        "system.user.delete" => '删除管理员',
        "system.role.view" => '查看角色',
        "system.role.add" => '添加角色',
        "system.role.edit" => '编辑角色',
        "system.role.delete" => '删除角色',
        "goods.view" => '查看采集库商品',
        "goods.export" => '导出采集库商品',
        "goods.listing.view" => '查看出价商品',
        "goods.listing.cancel" => '商品取消出价',
        "goods.listing.update" => '商品更新商品',
        "goods.new.view" => '查看新品',
        "goods.new.submit" => '提交新品',
        "goods.new.delete" => '删除新品',
        "goods.bidding" => '商品出价',
        "goods.apply" => '新品申请',
        "order.view" => '订单查看',
        "aftersale.view" => '查看售后',
        "finance.view" => '查看对账单',
    ];

    public static function getName($value)
    {
        return self::$permission[$value] ?? null;
    }
}
