<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * 测试用户登录
     */
    public function test_can_login()
    {
        $user = User::factory()->create([
            'username' => 'testuser',
            'password' => bcrypt('password123')
        ]);

        $response = $this->postJson('/api/login', [
            'username' => 'testuser',
            'password' => 'password123'
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'token',
                    'user' => [
                        'id',
                        'username',
                        'phone',
                        'status'
                    ]
                ]
            ]);
    }

    /**
     * 测试用户注册
     */
    public function test_can_register()
    {
        $response = $this->postJson('/api/register', [
            'username' => 'newuser',
            'password' => 'password123',
            'phone' => '13800138000'
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'username',
                    'phone',
                    'status'
                ]
            ]);

        $this->assertDatabaseHas('user', [
            'username' => 'newuser',
            'phone' => '13800138000'
        ]);
    }

    /**
     * 测试获取用户信息
     */
    public function test_can_get_user_info()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->getJson('/api/user');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'id',
                    'username',
                    'phone',
                    'status'
                ]
            ]);
    }

    /**
     * 测试更新用户信息
     */
    public function test_can_update_user_info()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->putJson('/api/user', [
                'phone' => '13900139000'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => '用户信息更新成功'
            ]);

        $this->assertDatabaseHas('user', [
            'id' => $user->id,
            'phone' => '13900139000'
        ]);
    }

    /**
     * 测试用户登出
     */
    public function test_can_logout()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->postJson('/api/logout');

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => '退出登录成功'
            ]);
    }
} 