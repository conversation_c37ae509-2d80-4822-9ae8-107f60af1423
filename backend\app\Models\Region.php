<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Region extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'region';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'parent_id',
        'name',
        'level',
        'sort_order',
        'status'
    ];

    /**
     * 获取父级地区
     */
    public function parent()
    {
        return $this->belongsTo(Region::class, 'parent_id');
    }

    /**
     * 获取子级地区
     */
    public function children()
    {
        return $this->hasMany(Region::class, 'parent_id');
    }

    /**
     * 获取所有子级地区
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 获取商户
     */
    public function merchants()
    {
        return $this->hasMany(Merchant::class);
    }
} 