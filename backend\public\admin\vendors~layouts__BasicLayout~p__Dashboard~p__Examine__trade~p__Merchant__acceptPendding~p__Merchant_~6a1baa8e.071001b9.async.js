(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[2],{"+i8S":function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n("q1tI"),o=function(e){var t=Object(r["useRef"])(null);return t.current=e,Object(r["useCallback"])((function(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(r))}),[])}},"/ezw":function(e,t,n){"use strict";var r=n("+y50"),o=n("jiTG"),a=n("rTrx"),i=n("TSYQ"),c=n.n(i),l=n("q1tI"),u=n("H84U"),s=n("bT9E"),d=function(e){var t,n,a=e.prefixCls,i=e.className,u=e.style,s=e.size,d=e.shape,f=c()((t={},Object(r["a"])(t,"".concat(a,"-lg"),"large"===s),Object(r["a"])(t,"".concat(a,"-sm"),"small"===s),t)),p=c()((n={},Object(r["a"])(n,"".concat(a,"-circle"),"circle"===d),Object(r["a"])(n,"".concat(a,"-square"),"square"===d),Object(r["a"])(n,"".concat(a,"-round"),"round"===d),n)),m=l["useMemo"]((function(){return"number"===typeof s?{width:s,height:s,lineHeight:"".concat(s,"px")}:{}}),[s]);return l["createElement"]("span",{className:c()(a,f,p,i),style:Object(o["a"])(Object(o["a"])({},m),u)})},f=d,p=function(e){var t=e.prefixCls,n=e.className,a=e.active,i=e.shape,d=void 0===i?"circle":i,p=e.size,m=void 0===p?"default":p,h=l["useContext"](u["b"]),v=h.getPrefixCls,b=v("skeleton",t),y=Object(s["a"])(e,["prefixCls","className"]),g=c()(b,"".concat(b,"-element"),Object(r["a"])({},"".concat(b,"-active"),a),n);return l["createElement"]("div",{className:g},l["createElement"](f,Object(o["a"])({prefixCls:"".concat(b,"-avatar"),shape:d,size:m},y)))},m=p,h=function(e){var t,n=e.prefixCls,a=e.className,i=e.active,d=e.block,p=void 0!==d&&d,m=e.size,h=void 0===m?"default":m,v=l["useContext"](u["b"]),b=v.getPrefixCls,y=b("skeleton",n),g=Object(s["a"])(e,["prefixCls"]),O=c()(y,"".concat(y,"-element"),(t={},Object(r["a"])(t,"".concat(y,"-active"),i),Object(r["a"])(t,"".concat(y,"-block"),p),t),a);return l["createElement"]("div",{className:O},l["createElement"](f,Object(o["a"])({prefixCls:"".concat(y,"-button"),size:h},g)))},v=h,b=n("2s+V"),y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM288 604a64 64 0 10128 0 64 64 0 10-128 0zm118-224a48 48 0 1096 0 48 48 0 10-96 0zm158 228a96 96 0 10192 0 96 96 0 10-192 0zm148-314a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"dot-chart",theme:"outlined"},g=y,O=n("/1Lp"),j=function(e,t){return l["createElement"](O["a"],Object(b["a"])(Object(b["a"])({},e),{},{ref:t,icon:g}))};j.displayName="DotChartOutlined";var x=l["forwardRef"](j),w=function(e){var t=e.prefixCls,n=e.className,o=e.style,a=e.active,i=e.children,s=l["useContext"](u["b"]),d=s.getPrefixCls,f=d("skeleton",t),p=c()(f,"".concat(f,"-element"),Object(r["a"])({},"".concat(f,"-active"),a),n),m=null!==i&&void 0!==i?i:l["createElement"](x,null);return l["createElement"]("div",{className:p},l["createElement"]("div",{className:c()("".concat(f,"-image"),n),style:o},m))},C=w,S="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",E=function(e){var t=e.prefixCls,n=e.className,o=e.style,a=e.active,i=l["useContext"](u["b"]),s=i.getPrefixCls,d=s("skeleton",t),f=c()(d,"".concat(d,"-element"),Object(r["a"])({},"".concat(d,"-active"),a),n);return l["createElement"]("div",{className:f},l["createElement"]("div",{className:c()("".concat(d,"-image"),n),style:o},l["createElement"]("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:"".concat(d,"-image-svg")},l["createElement"]("path",{d:S,className:"".concat(d,"-image-path")}))))},P=E,k=function(e){var t,n=e.prefixCls,a=e.className,i=e.active,d=e.block,p=e.size,m=void 0===p?"default":p,h=l["useContext"](u["b"]),v=h.getPrefixCls,b=v("skeleton",n),y=Object(s["a"])(e,["prefixCls"]),g=c()(b,"".concat(b,"-element"),(t={},Object(r["a"])(t,"".concat(b,"-active"),i),Object(r["a"])(t,"".concat(b,"-block"),d),t),a);return l["createElement"]("div",{className:g},l["createElement"](f,Object(o["a"])({prefixCls:"".concat(b,"-input"),size:m},y)))},T=k,M=n("xGeg"),N=function(e){var t=function(t){var n=e.width,r=e.rows,o=void 0===r?2:r;return Array.isArray(n)?n[t]:o-1===t?n:void 0},n=e.prefixCls,r=e.className,o=e.style,a=e.rows,i=Object(M["a"])(Array(a)).map((function(e,n){return l["createElement"]("li",{key:n,style:{width:t(n)}})}));return l["createElement"]("ul",{className:c()(n,r),style:o},i)},R=N,F=function(e){var t=e.prefixCls,n=e.className,r=e.width,a=e.style;return l["createElement"]("h3",{className:c()(t,n),style:Object(o["a"])({width:r},a)})},D=F;function L(e){return e&&"object"===Object(a["a"])(e)?e:{}}function I(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function A(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function z(e,t){var n={};return e&&t||(n.width="61%"),n.rows=!e&&t?3:2,n}var H=function(e){var t=e.prefixCls,n=e.loading,a=e.className,i=e.style,s=e.children,d=e.avatar,p=void 0!==d&&d,m=e.title,h=void 0===m||m,v=e.paragraph,b=void 0===v||v,y=e.active,g=e.round,O=l["useContext"](u["b"]),j=O.getPrefixCls,x=O.direction,w=j("skeleton",t);if(n||!("loading"in e)){var C,S,E,P=!!p,k=!!h,T=!!b;if(P){var M=Object(o["a"])(Object(o["a"])({prefixCls:"".concat(w,"-avatar")},I(k,T)),L(p));S=l["createElement"]("div",{className:"".concat(w,"-header")},l["createElement"](f,Object(o["a"])({},M)))}if(k||T){var N,F;if(k){var H=Object(o["a"])(Object(o["a"])({prefixCls:"".concat(w,"-title")},A(P,T)),L(h));N=l["createElement"](D,Object(o["a"])({},H))}if(T){var B=Object(o["a"])(Object(o["a"])({prefixCls:"".concat(w,"-paragraph")},z(P,k)),L(b));F=l["createElement"](R,Object(o["a"])({},B))}E=l["createElement"]("div",{className:"".concat(w,"-content")},N,F)}var K=c()(w,(C={},Object(r["a"])(C,"".concat(w,"-with-avatar"),P),Object(r["a"])(C,"".concat(w,"-active"),y),Object(r["a"])(C,"".concat(w,"-rtl"),"rtl"===x),Object(r["a"])(C,"".concat(w,"-round"),g),C),a);return l["createElement"]("div",{className:K,style:i},S,E)}return"undefined"!==typeof s?s:null};H.Button=v,H.Avatar=m,H.Input=T,H.Image=P,H.Node=C;var B=H;t["a"]=B},"/wGt":function(e,t,n){"use strict";var r=n("jiTG"),o=n("+y50"),a=n("Z97s"),i=n("064x"),c=n("TSYQ"),l=n.n(c),u=n("VTBJ"),s=n("ODXe"),d=n("q1tI"),f=n("bTyn"),p=n("TNol"),m=n("rePB"),h=n("wx14"),v=n("8XRh"),b=n("4IlW"),y=n("bX4T"),g=d["createContext"](null),O=g,j=function(e){var t=e.prefixCls,n=e.className,r=e.style,o=e.children,a=e.containerRef,i=e.id,c=e.onMouseEnter,s=e.onMouseOver,f=e.onMouseLeave,p=e.onClick,m=e.onKeyDown,v=e.onKeyUp,b={onMouseEnter:c,onMouseOver:s,onMouseLeave:f,onClick:p,onKeyDown:m,onKeyUp:v};return d["createElement"](d["Fragment"],null,d["createElement"]("div",Object(h["a"])({id:i,className:l()("".concat(t,"-content"),n),style:Object(u["a"])({},r),"aria-modal":"true",role:"dialog",ref:a},b),o))};var x=j,w=n("Kwbf");n("MNnm");function C(e){return"string"===typeof e&&String(Number(e))===e?(Object(w["a"])(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var S={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"};function E(e,t){var n,r,o,a,i=e.prefixCls,c=e.open,f=e.placement,p=e.inline,g=e.push,j=e.forceRender,w=e.autoFocus,E=e.keyboard,P=e.rootClassName,k=e.rootStyle,T=e.zIndex,M=e.className,N=e.id,R=e.style,F=e.motion,D=e.width,L=e.height,I=e.children,A=e.contentWrapperStyle,z=e.mask,H=e.maskClosable,B=e.maskMotion,K=e.maskClassName,_=e.maskStyle,V=e.afterOpenChange,U=e.onClose,W=e.onMouseEnter,G=e.onMouseOver,q=e.onMouseLeave,Y=e.onClick,X=e.onKeyDown,Z=e.onKeyUp,Q=d["useRef"](),$=d["useRef"](),J=d["useRef"]();d["useImperativeHandle"](t,(function(){return Q.current}));var ee=function(e){var t=e.keyCode,n=e.shiftKey;switch(t){case b["a"].TAB:var r;if(t===b["a"].TAB)if(n||document.activeElement!==J.current){if(n&&document.activeElement===$.current){var o;null===(o=J.current)||void 0===o||o.focus({preventScroll:!0})}}else null===(r=$.current)||void 0===r||r.focus({preventScroll:!0});break;case b["a"].ESC:U&&E&&(e.stopPropagation(),U(e));break}};d["useEffect"]((function(){var e;c&&w&&(null===(e=Q.current)||void 0===e||e.focus({preventScroll:!0}))}),[c]);var te,ne=d["useState"](!1),re=Object(s["a"])(ne,2),oe=re[0],ae=re[1],ie=d["useContext"](O);te=!1===g?{distance:0}:!0===g?{}:g||{};var ce=null!==(n=null!==(r=null===(o=te)||void 0===o?void 0:o.distance)&&void 0!==r?r:null===ie||void 0===ie?void 0:ie.pushDistance)&&void 0!==n?n:180,le=d["useMemo"]((function(){return{pushDistance:ce,push:function(){ae(!0)},pull:function(){ae(!1)}}}),[ce]);d["useEffect"]((function(){var e,t;c?null===ie||void 0===ie||null===(e=ie.push)||void 0===e||e.call(ie):null===ie||void 0===ie||null===(t=ie.pull)||void 0===t||t.call(ie)}),[c]),d["useEffect"]((function(){return function(){var e;null===ie||void 0===ie||null===(e=ie.pull)||void 0===e||e.call(ie)}}),[]);var ue=z&&d["createElement"](v["b"],Object(h["a"])({key:"mask"},B,{visible:c}),(function(e,t){var n=e.className,r=e.style;return d["createElement"]("div",{className:l()("".concat(i,"-mask"),n,K),style:Object(u["a"])(Object(u["a"])({},r),_),onClick:H&&c?U:void 0,ref:t})})),se="function"===typeof F?F(f):F,de={};if(oe&&ce)switch(f){case"top":de.transform="translateY(".concat(ce,"px)");break;case"bottom":de.transform="translateY(".concat(-ce,"px)");break;case"left":de.transform="translateX(".concat(ce,"px)");break;default:de.transform="translateX(".concat(-ce,"px)");break}"left"===f||"right"===f?de.width=C(D):de.height=C(L);var fe={onMouseEnter:W,onMouseOver:G,onMouseLeave:q,onClick:Y,onKeyDown:X,onKeyUp:Z},pe=d["createElement"](v["b"],Object(h["a"])({key:"panel"},se,{visible:c,forceRender:j,onVisibleChanged:function(e){null===V||void 0===V||V(e)},removeOnLeave:!1,leavedClassName:"".concat(i,"-content-wrapper-hidden")}),(function(t,n){var r=t.className,o=t.style;return d["createElement"]("div",Object(h["a"])({className:l()("".concat(i,"-content-wrapper"),r),style:Object(u["a"])(Object(u["a"])(Object(u["a"])({},de),o),A)},Object(y["a"])(e,{data:!0})),d["createElement"](x,Object(h["a"])({id:N,containerRef:n,prefixCls:i,className:M,style:R},fe),I))})),me=Object(u["a"])({},k);return T&&(me.zIndex=T),d["createElement"](O.Provider,{value:le},d["createElement"]("div",{className:l()(i,"".concat(i,"-").concat(f),P,(a={},Object(m["a"])(a,"".concat(i,"-open"),c),Object(m["a"])(a,"".concat(i,"-inline"),p),a)),style:me,tabIndex:-1,ref:Q,onKeyDown:ee},ue,d["createElement"]("div",{tabIndex:0,ref:$,style:S,"aria-hidden":"true","data-sentinel":"start"}),pe,d["createElement"]("div",{tabIndex:0,ref:J,style:S,"aria-hidden":"true","data-sentinel":"end"})))}var P=d["forwardRef"](E);var k=P,T=function(e){var t=e.open,n=void 0!==t&&t,r=e.prefixCls,o=void 0===r?"rc-drawer":r,a=e.placement,i=void 0===a?"right":a,c=e.autoFocus,l=void 0===c||c,m=e.keyboard,h=void 0===m||m,v=e.width,b=void 0===v?378:v,y=e.mask,g=void 0===y||y,O=e.maskClosable,j=void 0===O||O,x=e.getContainer,w=e.forceRender,C=e.afterOpenChange,S=e.destroyOnClose,E=e.onMouseEnter,P=e.onMouseOver,T=e.onMouseLeave,M=e.onClick,N=e.onKeyDown,R=e.onKeyUp,F=d["useState"](!1),D=Object(s["a"])(F,2),L=D[0],I=D[1];var A=d["useState"](!1),z=Object(s["a"])(A,2),H=z[0],B=z[1];Object(p["a"])((function(){B(!0)}),[]);var K=!!H&&n,_=d["useRef"](),V=d["useRef"]();Object(p["a"])((function(){K&&(V.current=document.activeElement)}),[K]);var U=function(e){var t,n;(I(e),null===C||void 0===C||C(e),e||!V.current||(null===(t=_.current)||void 0===t?void 0:t.contains(V.current)))||(null===(n=V.current)||void 0===n||n.focus({preventScroll:!0}))};if(!w&&!L&&!K&&S)return null;var W={onMouseEnter:E,onMouseOver:P,onMouseLeave:T,onClick:M,onKeyDown:N,onKeyUp:R},G=Object(u["a"])(Object(u["a"])({},e),{},{open:K,prefixCls:o,placement:i,autoFocus:l,keyboard:h,width:b,mask:g,maskClosable:j,inline:!1===x,afterOpenChange:U,ref:_},W);return d["createElement"](f["a"],{open:K||w||L,autoDestroy:!1,getContainer:x,autoLock:g&&(K||L)},d["createElement"](k,G))};var M=T,N=M,R=n("H84U"),F=n("ihLV"),D=n("EXcs"),L=n("CWQg"),I=n("+f9I"),A=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},z=(Object(L["a"])("default","large"),{distance:180});function H(e){var t=e.width,n=e.height,c=e.size,u=void 0===c?"default":c,s=e.closable,f=void 0===s||s,p=e.mask,m=void 0===p||p,h=e.push,v=void 0===h?z:h,b=e.closeIcon,y=void 0===b?d["createElement"](i["a"],null):b,g=e.bodyStyle,O=e.drawerStyle,j=e.className,x=e.visible,w=e.open,C=e.children,S=e.style,E=e.title,P=e.headerStyle,k=e.onClose,T=e.footer,M=e.footerStyle,L=e.prefixCls,H=e.getContainer,B=e.extra,K=e.afterVisibleChange,_=e.afterOpenChange,V=A(e,["width","height","size","closable","mask","push","closeIcon","bodyStyle","drawerStyle","className","visible","open","children","style","title","headerStyle","onClose","footer","footerStyle","prefixCls","getContainer","extra","afterVisibleChange","afterOpenChange"]),U=d["useContext"](R["b"]),W=U.getPopupContainer,G=U.getPrefixCls,q=U.direction,Y=G("drawer",L),X=void 0===H&&W?function(){return W(document.body)}:H,Z=f&&d["createElement"]("button",{type:"button",onClick:k,"aria-label":"Close",className:"".concat(Y,"-close")},y);function Q(){return E||f?d["createElement"]("div",{className:l()("".concat(Y,"-header"),Object(o["a"])({},"".concat(Y,"-header-close-only"),f&&!E&&!B)),style:P},d["createElement"]("div",{className:"".concat(Y,"-header-title")},Z,E&&d["createElement"]("div",{className:"".concat(Y,"-title")},E)),B&&d["createElement"]("div",{className:"".concat(Y,"-extra")},B)):null}function $(){if(!T)return null;var e="".concat(Y,"-footer");return d["createElement"]("div",{className:e,style:M},T)}[["visible","open"],["afterVisibleChange","afterOpenChange"]].forEach((function(e){var t=Object(a["a"])(e,2);t[0],t[1]}));var J=l()(Object(o["a"])({"no-mask":!m},"".concat(Y,"-rtl"),"rtl"===q),j),ee=d["useMemo"]((function(){return null!==t&&void 0!==t?t:"large"===u?736:378}),[t,u]),te=d["useMemo"]((function(){return null!==n&&void 0!==n?n:"large"===u?736:378}),[n,u]),ne={motionName:Object(D["c"])(Y,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},re=function(e){return{motionName:Object(D["c"])(Y,"panel-motion-".concat(e)),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500}};return d["createElement"](I["a"],null,d["createElement"](F["e"],{status:!0,override:!0},d["createElement"](N,Object(r["a"])({prefixCls:Y,onClose:k},V,{open:null!==w&&void 0!==w?w:x,mask:m,push:v,width:ee,height:te,rootClassName:J,getContainer:X,afterOpenChange:function(e){null===_||void 0===_||_(e),null===K||void 0===K||K(e)},maskMotion:ne,motion:re,rootStyle:S}),d["createElement"]("div",{className:"".concat(Y,"-wrapper-body"),style:Object(r["a"])({},O)},Q(),d["createElement"]("div",{className:"".concat(Y,"-body"),style:g},C),$()))))}t["a"]=H},"0XgM":function(e,t,n){},"15/o":function(e,t,n){},"1YHl":function(e,t,n){"use strict";n("EFp3"),n("15/o")},"3j9d":function(e,t,n){"use strict";n("J+/v");var r=n("MoRW");function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=n("qzPX");function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Object(a["a"])(r.key),r)}}function c(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}function u(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}function s(e){return s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},s(e)}function d(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}var f=n("A3Fk");function p(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function m(e,t){if(t&&("object"===Object(f["a"])(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return p(e)}function h(e){var t=d();return function(){var n,r=s(e);if(t){var o=s(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return m(this,n)}}var v=n("nKUr"),b=n("q1tI"),y=n.n(b),g=function(e){u(n,e);var t=h(n);function n(){var e;o(this,n);for(var r=arguments.length,a=new Array(r),i=0;i<r;i++)a[i]=arguments[i];return e=t.call.apply(t,[this].concat(a)),e.state={hasError:!1,errorInfo:""},e}return c(n,[{key:"componentDidCatch",value:function(e,t){console.log(e,t)}},{key:"render",value:function(){return this.state.hasError?Object(v["jsx"])(r["a"],{status:"error",title:"Something went wrong.",extra:this.state.errorInfo}):this.props.children}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,errorInfo:e.message}}}]),n}(y.a.Component);t["a"]=g},"5OYt":function(e,t,n){"use strict";var r=n("q1tI"),o=n("hkKa"),a=n("ACnJ");function i(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=Object(r["useRef"])({}),n=Object(o["a"])();return Object(r["useEffect"])((function(){var r=a["a"].subscribe((function(r){t.current=r,e&&n()}));return function(){return a["a"].unsubscribe(r)}}),[]),t.current}t["a"]=i},"7Dcv":function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n("wE4A");function o(e,t){if(e){if("string"===typeof e)return Object(r["a"])(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(r["a"])(e,t):void 0}}},"7z/i":function(e,t,n){"use strict";(function(e){var n="undefined"!==typeof e&&null!=e.versions&&null!=e.versions.node,r=function(){return"undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.matchMedia&&!n};t["a"]=r}).call(this,n("Q2Ig"))},"9ama":function(e,t,n){},"9mHw":function(e,t,n){"use strict";var r=n("iqvk"),o=n("Imx0");function a(e,t){var n=Object(o["a"])(e,t),a=Object(r["a"])(n,2),i=a[0],c=a[1];return[i,c]}t["a"]=a},A3Fk:function(e,t,n){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}n.d(t,"a",(function(){return r}))},AOa7:function(e,t,n){},"CWI+":function(e,t,n){},Codc:function(e,t,n){"use strict";function r(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(u){return void n(u)}c.done?t(l):Promise.resolve(l).then(r,o)}function o(e){return function(){var t=this,n=arguments;return new Promise((function(o,a){var i=e.apply(t,n);function c(e){r(i,o,a,c,l,"next",e)}function l(e){r(i,o,a,c,l,"throw",e)}c(void 0)}))}}n.d(t,"a",(function(){return o}))},DPCm:function(e,t,n){},DnfT:function(e,t,n){},HBdL:function(e,t,n){"use strict";var r=n("IewF"),o=n("Codc"),a=n("q1tI"),i=n("+i8S");function c(e,t){var n=Object(i["a"])(e),c=Object(a["useRef"])(),l=Object(a["useCallback"])((function(){c.current&&(clearTimeout(c.current),c.current=null)}),[]),u=Object(a["useCallback"])(Object(o["a"])(Object(r["a"])().mark((function e(){var a,i,u,s=arguments;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a=s.length,i=new Array(a),u=0;u<a;u++)i[u]=s[u];if(0!==t&&void 0!==t){e.next=3;break}return e.abrupt("return",n.apply(void 0,i));case 3:return l(),e.abrupt("return",new Promise((function(e){c.current=setTimeout(Object(o["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.t0=e,t.next=3,n.apply(void 0,i);case 3:t.t1=t.sent,(0,t.t0)(t.t1);case 5:case"end":return t.stop()}}),t)}))),t)})));case 5:case"end":return e.stop()}}),e)}))),[n,l,t]);return Object(a["useEffect"])((function(){return l}),[l]),{run:u,cancel:l}}t["a"]=c},Hx5s:function(e,t,n){"use strict";n.d(t,"a",(function(){return vr}));n("EFp3"),n("0XgM");var r=n("PKem"),o=n("ZX9x"),a=r["e"];a.Header=r["c"],a.Footer=r["b"],a.Content=r["a"],a.Sider=o["b"];var i=a,c=n("BRNN"),l=n("gWn6");function u(){u=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var a=t&&t.prototype instanceof g?t:g,i=Object.create(a.prototype),c=new R(r||[]);return o(i,"_invoke",{value:k(e,n,c)}),i}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var m="suspendedStart",h="suspendedYield",v="executing",b="completed",y={};function g(){}function O(){}function j(){}var x={};d(x,i,(function(){return this}));var w=Object.getPrototypeOf,C=w&&w(w(F([])));C&&C!==n&&r.call(C,i)&&(x=C);var S=j.prototype=g.prototype=Object.create(x);function E(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(o,a,i,c){var u=p(e[o],e,a);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==Object(l["a"])(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,i,c)}),(function(e){n("throw",e,i,c)})):t.resolve(d).then((function(e){s.value=e,i(s)}),(function(e){return n("throw",e,i,c)}))}c(u.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function k(t,n,r){var o=m;return function(a,i){if(o===v)throw new Error("Generator is already running");if(o===b){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var l=T(c,r);if(l){if(l===y)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===m)throw o=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=v;var u=p(t,n,r);if("normal"===u.type){if(o=r.done?b:h,u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=b,r.method="throw",r.arg=u.arg)}}}function T(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator["return"]&&(n.method="return",n.arg=e,T(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var a=p(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,y;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function M(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function R(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(M,this),this.reset(!0)}function F(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(Object(l["a"])(t)+" is not iterable")}return O.prototype=j,o(S,"constructor",{value:j,configurable:!0}),o(j,"constructor",{value:O,configurable:!0}),O.displayName=d(j,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===O||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,j):(e.__proto__=j,d(e,s,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},E(P.prototype),d(P.prototype,c,(function(){return this})),t.AsyncIterator=P,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new P(f(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},E(S),d(S,s,"Generator"),d(S,i,(function(){return this})),d(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=F,R.prototype={constructor:R,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;N(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:F(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function s(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(u){return void n(u)}c.done?t(l):Promise.resolve(l).then(r,o)}function d(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){s(a,r,o,i,c,"next",e)}function c(e){s(a,r,o,i,c,"throw",e)}i(void 0)}))}}var f=n("lcH3"),p=(n("GNNt"),n("wEI+")),m=n("kF4R"),h=n("sIPS"),v=n("nKUr"),b=n("LQCs"),y=n("9mHw"),g=n("q1tI"),O=n.n(g),j=n("7z/i");function x(e,t){var n="string"===typeof e.pageName?e.title:t;Object(g["useEffect"])((function(){Object(j["a"])()&&n&&(document.title=n)}),[e.title,n])}var w=x,C=n("GOef"),S=n("TSYQ"),E=n.n(S);function P(e,t){for(var n=Object.assign({},e),r=0;r<t.length;r+=1){var o=t[r];delete n[o]}return n}var k=P;function T(e,t){return D(e)||F(e,t)||N(e,t)||M()}function M(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function N(e,t){if(e){if("string"===typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?R(e,t):void 0}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function F(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,a=void 0;try{for(var i,c=e[Symbol.iterator]();!(r=(i=c.next()).done);r=!0)if(n.push(i.value),t&&n.length===t)break}catch(l){o=!0,a=l}finally{try{r||null==c["return"]||c["return"]()}finally{if(o)throw a}}return n}}function D(e){if(Array.isArray(e))return e}function L(e,t){var n=t||{},r=n.defaultValue,o=n.value,a=n.onChange,i=n.postState,c=g["useState"]((function(){return void 0!==o?o:void 0!==r?"function"===typeof r?r():r:"function"===typeof e?e():e})),l=T(c,2),u=l[0],s=l[1],d=void 0!==o?o:u;function f(e){s(e),d!==e&&a&&a(e,d)}i&&(d=i(d));var p=g["useRef"](!0);return g["useEffect"]((function(){p.current?p.current=!1:void 0===o&&s(o)}),[o]),[d,f]}var I=n("Lpa7"),A=n("SE9/"),z=n("2W6z"),H=n.n(z),B=(n("mQwV"),n("95SA")),K=(n("bbsP"),n("/wGt")),_=n("zP5H");function V(){var e=Object(g["useState"])([]),t=Object(m["a"])(e,2),n=t[0],r=t[1];return{flatMenuKeys:n,setFlatMenuKeys:r}}var U=Object(_["a"])(V),W=U,G=(n("lUTK"),n("BvKs")),q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"},Y=q,X=n("6VBw"),Z=function(e,t){return g["createElement"](X["a"],Object.assign({},e,{ref:t,icon:Y}))};Z.displayName="MenuUnfoldOutlined";var Q=g["forwardRef"](Z),$={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"},J=$,ee=function(e,t){return g["createElement"](X["a"],Object.assign({},e,{ref:t,icon:J}))};ee.displayName="MenuFoldOutlined";var te=g["forwardRef"](ee),ne=(n("cWXX"),n("/ezw")),re=n("ORfw"),oe=n("GTRU");function ae(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Object(oe["a"])(r.key),r)}}function ie(e,t,n){return t&&ae(e.prototype,t),n&&ae(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ce(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var le=n("Ff2n"),ue=n("rePB"),se=n("Qi1f");function de(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?de(Object(n),!0).forEach((function(t){Object(ue["a"])(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):de(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var pe=g["forwardRef"]((function(e,t){var n=e.className,r=e.component,o=e.viewBox,a=e.spin,i=e.rotate,c=e.tabIndex,l=e.onClick,u=e.children,s=Object(le["a"])(e,["className","component","viewBox","spin","rotate","tabIndex","onClick","children"]);Object(se["g"])(Boolean(r||u),"Should have `component` prop or `children`."),Object(se["f"])();var d=E()("anticon",n),f=E()({"anticon-spin":!!a}),p=i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0,m=fe(fe({},se["e"]),{},{className:f,style:p,viewBox:o});o||delete m.viewBox;var h=function(){return r?g["createElement"](r,Object.assign({},m),u):u?(Object(se["g"])(Boolean(o)||1===g["Children"].count(u)&&g["isValidElement"](u)&&"use"===g["Children"].only(u).type,"Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),g["createElement"]("svg",Object.assign({},m,{viewBox:o}),u)):null},v=c;return void 0===v&&l&&(v=-1),g["createElement"]("span",Object.assign({role:"img"},s,{ref:t,tabIndex:v,onClick:l,className:d}),h())}));pe.displayName="AntdIcon";var me=pe,he=new Set;function ve(e){return"string"===typeof e&&e.length&&!he.has(e)}function be(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e[t];if(ve(n)){var r=document.createElement("script");r.setAttribute("src",n),r.setAttribute("data-namespace",n),e.length>t+1&&(r.onload=function(){be(e,t+1)},r.onerror=function(){be(e,t+1)}),he.add(n),document.body.appendChild(r)}}function ye(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.scriptUrl,n=e.extraCommonProps,r=void 0===n?{}:n;"undefined"!==typeof document&&"undefined"!==typeof window&&"function"===typeof document.createElement&&(Array.isArray(t)?be(t.reverse()):be([t]));var o=g["forwardRef"]((function(e,t){var n=e.type,o=e.children,a=Object(le["a"])(e,["type","children"]),i=null;return e.type&&(i=g["createElement"]("use",{xlinkHref:"#".concat(n)})),o&&(i=o),g["createElement"](me,Object.assign({},r,a,{ref:t}),i)}));return o.displayName="Iconfont",o}var ge=function(e){if(!e)return!1;if(!e.startsWith("http"))return!1;try{var t=new URL(e);return!!t}catch(n){return!1}},Oe=ge;function je(e){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(e)}var xe=je,we={navTheme:"dark",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!1,headerHeight:48,iconfontUrl:"",primaryColor:"#1890ff",splitMenus:!1},Ce=we,Se=function e(t){return(t||[]).reduce((function(t,n){if(n.key&&t.push(n.key),n.routes){var r=t.concat(e(n.routes)||[]);return r}return t}),[])};function Ee(e){return e.map((function(e){var t=e.children||e.routes,n=Object(h["a"])({},e);if(!n.name||n.hideInMenu)return null;if(n&&(null===n||void 0===n?void 0:n.routes)){if(!n.hideChildrenInMenu&&t.some((function(e){return e&&e.name&&!e.hideInMenu})))return Object(h["a"])(Object(h["a"])({},e),{},{children:Ee(t),routes:Ee(t)});delete n.routes,delete n.children}return n})).filter((function(e){return e}))}n("Re9Q");var Pe=ye({scriptUrl:Ce.iconfontUrl}),ke=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"icon-";if("string"===typeof e&&""!==e){if(Oe(e)||xe(e))return Object(v["jsx"])(me,{component:function(){return Object(v["jsx"])("img",{src:e,alt:"icon",className:"ant-pro-sider-menu-icon"})}});if(e.startsWith(t))return Object(v["jsx"])(Pe,{type:e})}return e},Te=ie((function e(t){var n=this;ce(this,e),this.props=void 0,this.getNavMenuItems=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;return e.map((function(e){return n.getSubMenuOrItem(e,t)})).filter((function(e){return e}))},this.getSubMenuOrItem=function(e,t){var r=(null===e||void 0===e?void 0:e.children)||(null===e||void 0===e?void 0:e.routes);if(Array.isArray(r)&&r.length>0){var o=n.getIntlName(e),a=n.props,i=a.subMenuItemRender,c=a.prefixCls,l=a.menu,u=a.iconPrefixes,s=e.icon?Object(v["jsxs"])("span",{className:"".concat(c,"-menu-item"),title:o,children:[!t&&ke(e.icon,u),Object(v["jsx"])("span",{className:"".concat(c,"-menu-item-title"),children:o})]}):Object(v["jsx"])("span",{className:"".concat(c,"-menu-item"),title:o,children:o}),d=i?i(Object(h["a"])(Object(h["a"])({},e),{},{isUrl:!1}),s,n.props):s;return{type:"group"===(null===l||void 0===l?void 0:l.type)?"group":void 0,label:d,children:n.getNavMenuItems(r,!0),onTitleClick:e.onTitleClick,key:e.key||e.path}}return{label:n.getMenuItemPath(e,t),title:n.getIntlName(e),key:e.key||e.path,disabled:e.disabled,onClick:function(t){var n;Oe(null===e||void 0===e?void 0:e.path)&&window.open(e.path,"_blank"),null===(n=e.onTitleClick)||void 0===n||n.call(e,t)}}},this.getIntlName=function(e){var t=e.name,r=e.locale,o=n.props,a=o.menu,i=o.formatMessage;return r&&!1!==(null===a||void 0===a?void 0:a.locale)?null===i||void 0===i?void 0:i({id:r,defaultMessage:t}):t},this.getMenuItemPath=function(e,t){var r=n.conversionPath(e.path||"/"),o=n.props,a=o.location,i=void 0===a?{pathname:"/"}:a,l=o.isMobile,u=o.onCollapse,s=o.menuItemRender,d=o.iconPrefixes,f=n.getIntlName(e),p=n.props.prefixCls,m=t?null:ke(e.icon,d),b=Oe(r),y=Object(v["jsxs"])("span",{className:E()("".concat(p,"-menu-item"),Object(c["a"])({},"".concat(p,"-menu-item-link"),b)),children:[m,Object(v["jsx"])("span",{className:"".concat(p,"-menu-item-title"),children:f})]});if(s){var g=Object(h["a"])(Object(h["a"])({},e),{},{isUrl:b,itemPath:r,isMobile:l,replace:r===i.pathname,onClick:function(){b&&window.open(r,"_blank"),u&&u(!0)},children:void 0});return s(g,y,n.props)}return y},this.conversionPath=function(e){return e&&0===e.indexOf("http")?e:"/".concat(e||"").replace(/\/+/g,"/")},this.props=t})),Me=function(e,t){var n=t.layout,r=t.collapsed,o={};return e&&!r&&["side","mix"].includes(n||"mix")&&(o={openKeys:e}),o},Ne=function(e){var t=e.theme,n=e.mode,r=e.className,o=e.handleOpenChange,a=e.style,i=e.menuData,c=e.menu,l=e.matchMenuKeys,u=e.iconfontUrl,s=e.collapsed,d=e.selectedKeys,f=e.onSelect,p=e.openKeys,b=Object(g["useRef"])([]),O=W.useContainer(),j=O.flatMenuKeys,x=Object(y["a"])(null===c||void 0===c?void 0:c.defaultOpenAll),w=Object(m["a"])(x,2),C=w[0],S=w[1],P=Object(y["a"])((function(){return(null===c||void 0===c?void 0:c.defaultOpenAll)?Se(i)||[]:!1!==p&&[]}),{value:!1===p?void 0:p,onChange:o}),k=Object(m["a"])(P,2),T=k[0],M=k[1],N=Object(y["a"])([],{value:d,onChange:f?function(e){f&&e&&f(e)}:void 0}),R=Object(m["a"])(N,2),F=R[0],D=R[1];Object(g["useEffect"])((function(){(null===c||void 0===c?void 0:c.defaultOpenAll)||!1===p||j.length||l&&(M(l),D(l))}),[l.join("-")]),Object(g["useEffect"])((function(){u&&(Pe=ye({scriptUrl:u}))}),[u]),Object(g["useEffect"])((function(){if(l.join("-")!==(F||[]).join("-")&&D(l),C||!1===p||l.join("-")===(T||[]).join("-"))(null===c||void 0===c?void 0:c.ignoreFlatMenu)&&C?M(Se(i)):j.length>0&&S(!1);else{var e=l;!1===(null===c||void 0===c?void 0:c.autoClose)&&(e=Array.from(new Set([].concat(Object(re["a"])(l),Object(re["a"])(T||[]))))),M(e)}}),[l.join("-"),s]);var L=Object(g["useMemo"])((function(){return Me(T,e)}),[T&&T.join(","),e.layout,e.collapsed]),I=Object(g["useState"])((function(){return new Te(e)})),A=Object(m["a"])(I,1),z=A[0];if(null===c||void 0===c?void 0:c.loading)return Object(v["jsx"])("div",{style:(null===n||void 0===n?void 0:n.includes("inline"))?{padding:24}:{marginTop:16},children:Object(v["jsx"])(ne["a"],{active:!0,title:!1,paragraph:{rows:(null===n||void 0===n?void 0:n.includes("inline"))?6:1}})});var H=E()(r,{"top-nav-menu":"horizontal"===n});z.props=e,!1!==e.openKeys||e.handleOpenChange||(b.current=l);var B=e.postMenuData?e.postMenuData(i):i;return B&&(null===B||void 0===B?void 0:B.length)<1?null:Object(g["createElement"])(G["a"],Object(h["a"])(Object(h["a"])({},L),{},{key:"Menu",mode:n,items:z.getNavMenuItems(B,!1),inlineIndent:16,defaultOpenKeys:b.current,theme:t,selectedKeys:F,style:a,className:H,onOpenChange:M},e.menuProps))};Ne.defaultProps={postMenuData:function(e){return e||[]}};var Re=Ne,Fe=i.Sider,De=function(e){return"string"===typeof e?Object(v["jsx"])("img",{src:e,alt:"logo"}):"function"===typeof e?e():e},Le=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"menuHeaderRender",n=e.logo,r=e.title,o=e.layout,a=e[t||""];if(!1===a)return null;var i=De(n),c=Object(v["jsx"])("h1",{children:null!==r&&void 0!==r?r:"Ant Design Pro"});return a?a(i,e.collapsed?null:c,e):"mix"===o&&"menuHeaderRender"===t?null:Object(v["jsxs"])("a",{children:[i,e.collapsed?null:c]})},Ie=function(e){return e?Object(v["jsx"])(Q,{}):Object(v["jsx"])(te,{})},Ae=function(e){var t,n=e.collapsed,r=e.fixSiderbar,o=e.menuFooterRender,a=e.onCollapse,i=e.theme,l=e.siderWidth,u=e.isMobile,s=e.onMenuHeaderClick,d=e.breakpoint,f=void 0===d?"lg":d,p=e.style,m=e.layout,b=e.menuExtraRender,y=void 0!==b&&b,O=e.collapsedButtonRender,j=void 0===O?Ie:O,x=e.links,w=e.menuContentRender,C=e.prefixCls,S=e.onOpenChange,P=e.headerHeight,k=e.logoStyle,T="".concat(C,"-sider"),M=W.useContainer(),N=M.flatMenuKeys,R=E()("".concat(T),(t={},Object(c["a"])(t,"".concat(T,"-fixed"),r),Object(c["a"])(t,"".concat(T,"-layout-").concat(m),m&&!u),Object(c["a"])(t,"".concat(T,"-light"),"dark"!==i),t)),F=Le(e),D=y&&y(e),L=!1!==w&&N&&Object(g["createElement"])(Re,Object(h["a"])(Object(h["a"])({},e),{},{key:"base-menu",mode:"inline",handleOpenChange:S,style:{width:"100%"},className:"".concat(T,"-menu")})),I=w?w(e,L):L,A=(x||[]).map((function(e,t){return{className:"".concat(T,"-link"),label:e,key:t}}));return j&&!u&&A.push({className:"".concat(T,"-collapsed-button"),title:!1,key:"collapsed",onClick:function(){a&&a(!n)},label:j(n)}),Object(v["jsxs"])(v["Fragment"],{children:[r&&Object(v["jsx"])("div",{style:Object(h["a"])({width:n?48:l,overflow:"hidden",flex:"0 0 ".concat(n?48:l,"px"),maxWidth:n?48:l,minWidth:n?48:l,transition:"background-color 0.3s, min-width 0.3s, max-width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"},p)}),Object(v["jsxs"])(Fe,{collapsible:!0,trigger:null,collapsed:n,breakpoint:!1===f?void 0:f,onCollapse:function(e){u||null===a||void 0===a||a(e)},collapsedWidth:48,style:Object(h["a"])({overflow:"hidden",paddingTop:"mix"!==m||u?void 0:P},p),width:l,theme:i,className:R,children:[F&&Object(v["jsx"])("div",{className:E()("".concat(T,"-logo"),Object(c["a"])({},"".concat(T,"-collapsed"),n)),onClick:"mix"!==m?s:void 0,id:"logo",style:k,children:F}),D&&Object(v["jsx"])("div",{className:"".concat(T,"-extra ").concat(!F&&"".concat(T,"-extra-no-logo")),children:D}),Object(v["jsx"])("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:I}),Object(v["jsx"])("div",{className:"".concat(T,"-links"),children:Object(v["jsx"])(G["a"],{theme:i,inlineIndent:16,className:"".concat(T,"-link-menu"),selectedKeys:[],openKeys:[],mode:"inline",items:A})}),o&&Object(v["jsx"])("div",{className:E()("".concat(T,"-footer"),Object(c["a"])({},"".concat(T,"-footer-collapsed"),!n)),children:o(e)})]})]})},ze=Ae,He=function(e){var t=e.isMobile,n=e.menuData,r=e.siderWidth,o=e.collapsed,a=e.onCollapse,i=e.style,c=e.className,l=e.hide,u=e.getContainer,s=e.prefixCls,d=e.matchMenuKeys,f=W.useContainer(),p=f.setFlatMenuKeys;Object(g["useEffect"])((function(){if(n&&!(n.length<1)){var e=Object(C["a"])(n);p(Object.keys(e))}}),[d.join("-")]),Object(g["useEffect"])((function(){!0===t&&(null===a||void 0===a||a(!0))}),[t]);var m=k(e,["className","style"]);return l?null:t?Object(v["jsx"])(K["a"],{visible:!o,placement:"left",className:E()("".concat(s,"-drawer-sider"),c),onClose:function(){return null===a||void 0===a?void 0:a(!0)},style:Object(h["a"])({padding:0,height:"100vh"},i),getContainer:u,width:r,bodyStyle:{height:"100vh",padding:0,display:"flex",flexDirection:"row"},children:Object(v["jsx"])(ze,Object(h["a"])(Object(h["a"])({},m),{},{className:E()("".concat(s,"-sider"),c),collapsed:!t&&o,splitMenus:!1}))}):Object(v["jsx"])(ze,Object(h["a"])(Object(h["a"])({className:E()("".concat(s,"-sider"),c)},m),{},{style:i}))},Be=He,Ke={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"}}]},name:"copyright",theme:"outlined"},_e=Ke,Ve=function(e,t){return g["createElement"](X["a"],Object.assign({},e,{ref:t,icon:_e}))};Ve.displayName="CopyrightOutlined";var Ue=g["forwardRef"](Ve),We=(n("DPCm"),function(e){var t=e.className,n=e.prefixCls,r=e.links,o=e.copyright,a=e.style,i=Object(g["useContext"])(p["a"].ConfigContext),c=i.getPrefixCls(n||"pro-global-footer");if((null==r||!1===r||Array.isArray(r)&&0===r.length)&&(null==o||!1===o))return null;var l=E()(c,t);return Object(v["jsxs"])("div",{className:l,style:a,children:[r&&Object(v["jsx"])("div",{className:"".concat(c,"-links"),children:r.map((function(e){return Object(v["jsx"])("a",{title:e.key,target:e.blankTarget?"_blank":"_self",href:e.href,rel:"noreferrer",children:e.title},e.key)}))}),o&&Object(v["jsx"])("div",{className:"".concat(c,"-copyright"),children:o})]})}),Ge=i.Footer,qe=function(e){var t=e.links,n=e.copyright,r=e.style,o=e.className,a=e.prefixCls;return Object(v["jsx"])(Ge,{className:o,style:Object(h["a"])({padding:0},r),children:Object(v["jsx"])(We,{links:t,prefixCls:a,copyright:!1===n?null:Object(v["jsxs"])(g["Fragment"],{children:[Object(v["jsx"])(Ue,{})," ",n]})})})},Ye=qe,Xe=n("su3W");function Ze(e,t){return Ze=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ze(e,t)}function Qe(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ze(e,t)}function $e(e){return $e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$e(e)}function Je(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function et(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function tt(e,t){if(t&&("object"===Object(l["a"])(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return et(e)}function nt(e){var t=Je();return function(){var n,r=$e(e);if(t){var o=$e(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return tt(this,n)}}var rt=n("HBdL"),ot=n("t23M"),at=(n("UPUD"),["rightContentRender","prefixCls"]),it=function(e){var t=e.rightContentRender,n=e.prefixCls,r=Object(f["a"])(e,at),o=Object(g["useState"])("auto"),a=Object(m["a"])(o,2),i=a[0],c=a[1],l=Object(rt["a"])(function(){var e=d(u().mark((function e(t){return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:c(t);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),160);return Object(v["jsx"])("div",{className:"".concat(n,"-right-content"),style:{minWidth:i},children:Object(v["jsx"])("div",{style:{paddingRight:8},children:Object(v["jsx"])(ot["a"],{onResize:function(e){var t=e.width;l.run(t)},children:t&&Object(v["jsx"])("div",{className:"".concat(n,"-right-content-resize"),children:t(Object(h["a"])(Object(h["a"])({},r),{},{rightContentSize:i}))})})})})},ct=function(e){var t=Object(g["useRef"])(null),n=e.theme,r=e.onMenuHeaderClick,o=e.contentWidth,a=e.rightContentRender,i=e.className,c=e.style,l=e.headerContentRender,u=e.layout,s="".concat(e.prefixCls||"ant-pro","-top-nav-header"),d=Le(Object(h["a"])(Object(h["a"])({},e),{},{collapsed:!1}),"mix"===u?"headerTitleRender":void 0),f=E()(s,i,{light:"light"===n}),p=Object(v["jsx"])(Re,Object(h["a"])(Object(h["a"])({},e),e.menuProps)),m=l?null===l||void 0===l?void 0:l(e,p):p;return Object(v["jsx"])("div",{className:f,style:c,children:Object(v["jsxs"])("div",{ref:t,className:"".concat(s,"-main ").concat("Fixed"===o?"wide":""),children:[d&&Object(v["jsx"])("div",{className:"".concat(s,"-main-left"),onClick:r,children:Object(v["jsx"])("div",{className:"".concat(s,"-logo"),id:"logo",children:d},"logo")}),Object(v["jsx"])("div",{style:{flex:1},className:"".concat(s,"-menu"),children:m}),a&&Object(v["jsx"])(it,Object(h["a"])({rightContentRender:a,prefixCls:s},e))]})})},lt=ct,ut=(n("W3vr"),function(e,t){return!1===e?null:e?e(t,null):t}),st=function(e){var t=e.isMobile,n=e.logo,r=e.collapsed,o=e.onCollapse,a=e.collapsedButtonRender,i=void 0===a?Ie:a,l=e.rightContentRender,u=e.menuHeaderRender,s=e.onMenuHeaderClick,d=e.className,f=e.style,m=e.layout,b=e.children,y=e.headerTheme,O=void 0===y?"dark":y,j=e.splitMenus,x=e.menuData,w=e.prefixCls,C=Object(g["useContext"])(p["a"].ConfigContext),S=C.direction,P="".concat(w,"-global-header"),k=E()(d,P,Object(c["a"])({},"".concat(P,"-layout-").concat(m),m&&"dark"===O));if("mix"===m&&!t&&j){var T=(x||[]).map((function(e){return Object(h["a"])(Object(h["a"])({},e),{},{children:void 0,routes:void 0})})),M=Ee(T);return Object(v["jsx"])(lt,Object(h["a"])(Object(h["a"])({mode:"horizontal"},e),{},{splitMenus:!1,menuData:M,theme:O}))}var N=E()("".concat(P,"-logo"),Object(c["a"])({},"".concat(P,"-logo-rtl"),"rtl"===S)),R=Object(v["jsx"])("span",{className:N,children:Object(v["jsx"])("a",{children:De(n)})},"logo");return Object(v["jsxs"])("div",{className:k,style:Object(h["a"])({},f),children:[t&&ut(u,R),t&&i&&Object(v["jsx"])("span",{className:"".concat(P,"-collapsed-button"),onClick:function(){o&&o(!r)},children:i(r)}),"mix"===m&&!t&&Object(v["jsx"])(v["Fragment"],{children:Object(v["jsx"])("div",{className:N,onClick:s,children:Le(Object(h["a"])(Object(h["a"])({},e),{},{collapsed:!1}),"headerTitleRender")})}),Object(v["jsx"])("div",{style:{flex:1},children:b}),l&&l(e)]})},dt=st,ft=(n("YPDd"),i.Header),pt=function(e){Qe(n,e);var t=nt(n);function n(){var e;ce(this,n);for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];return e=t.call.apply(t,[this].concat(o)),e.renderContent=function(){var t=e.props,n=t.isMobile,r=t.onCollapse,o=t.navTheme,a=t.layout,i=t.headerRender,c=t.headerContentRender,l="top"===a,u=Ee(e.props.menuData||[]),s=Object(v["jsx"])(dt,Object(h["a"])(Object(h["a"])({onCollapse:r},e.props),{},{menuData:u,children:c&&c(e.props,null)}));return l&&!n&&(s=Object(v["jsx"])(lt,Object(h["a"])(Object(h["a"])({theme:o,mode:"horizontal",onCollapse:r},e.props),{},{menuData:u}))),i&&"function"===typeof i?i(e.props,s):s},e}return ie(n,[{key:"render",value:function(){var e,t=this.props,n=t.fixedHeader,r=t.layout,o=t.className,a=t.style,i=t.navTheme,l=t.collapsed,u=t.siderWidth,s=t.hasSiderMenu,d=t.isMobile,f=t.prefixCls,p=t.headerHeight,m=n||"mix"===r,b="top"===r,y=m&&s&&!b&&!d,g=E()(o,(e={},Object(c["a"])(e,"".concat(f,"-fixed-header"),m),Object(c["a"])(e,"".concat(f,"-fixed-header-action"),!l),Object(c["a"])(e,"".concat(f,"-top-menu"),b),Object(c["a"])(e,"".concat(f,"-header-").concat(i),i&&"mix"!==r),e)),O="mix"!==r&&y?"calc(100% - ".concat(l?48:u,"px)"):"100%",j=m?0:void 0;return Object(v["jsxs"])(v["Fragment"],{children:[m&&Object(v["jsx"])(ft,{style:{height:p,lineHeight:"".concat(p,"px"),background:"transparent"}}),Object(v["jsx"])(ft,{style:Object(h["a"])({padding:0,height:p,lineHeight:"".concat(p,"px"),width:O,zIndex:"mix"===r?100:19,right:j},a),className:g,children:this.renderContent()})]})}}]),n}(g["Component"]),mt=pt,ht={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.daybreak":"Daybreak Blue (default)","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uff0cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"},vt=Object(h["a"])({},ht),bt={"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.daybreak":"Blu cielo mattutino (default)","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xe0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xe8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"},yt=Object(h["a"])({},bt),gt={"app.setting.pagestyle":"\uc2a4\ud0c0\uc77c \uc124\uc815","app.setting.pagestyle.dark":"\ub2e4\ud06c \ubaa8\ub4dc","app.setting.pagestyle.light":"\ub77c\uc774\ud2b8 \ubaa8\ub4dc","app.setting.content-width":"\ucee8\ud150\uce20 \ub108\ube44","app.setting.content-width.fixed":"\uace0\uc815","app.setting.content-width.fluid":"\ud750\ub984","app.setting.themecolor":"\ud14c\ub9c8 \uc0c9\uc0c1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.daybreak":"Daybreak Blue (default)","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\ub124\ube44\uac8c\uc774\uc158 \ubaa8\ub4dc","app.setting.regionalsettings":"\uc601\uc5ed\ubcc4 \uc124\uc815","app.setting.regionalsettings.header":"\ud5e4\ub354","app.setting.regionalsettings.menu":"\uba54\ub274","app.setting.regionalsettings.footer":"\ubc14\ub2e5\uae00","app.setting.regionalsettings.menuHeader":"\uba54\ub274 \ud5e4\ub354","app.setting.sidemenu":"\uba54\ub274 \uc0ac\uc774\ub4dc \ubc30\uce58","app.setting.topmenu":"\uba54\ub274 \uc0c1\ub2e8 \ubc30\uce58","app.setting.mixmenu":"\ud63c\ud569\ud615 \ubc30\uce58","app.setting.splitMenus":"\uba54\ub274 \ubd84\ub9ac","app.setting.fixedheader":"\ud5e4\ub354 \uace0\uc815","app.setting.fixedsidebar":"\uc0ac\uc774\ub4dc\ubc14 \uace0\uc815","app.setting.fixedsidebar.hint":"'\uba54\ub274 \uc0ac\uc774\ub4dc \ubc30\uce58'\ub97c \uc120\ud0dd\ud588\uc744 \ub54c \ub3d9\uc791\ud568","app.setting.hideheader":"\uc2a4\ud06c\ub864 \uc911 \ud5e4\ub354 \uac10\ucd94\uae30","app.setting.hideheader.hint":"'\ud5e4\ub354 \uac10\ucd94\uae30 \uc635\uc158'\uc744 \uc120\ud0dd\ud588\uc744 \ub54c \ub3d9\uc791\ud568","app.setting.othersettings":"\ub2e4\ub978 \uc124\uc815","app.setting.weakmode":"\uace0\ub300\ube44 \ubaa8\ub4dc","app.setting.copy":"\uc124\uc815\uac12 \ubcf5\uc0ac","app.setting.loading":"\ud14c\ub9c8 \ub85c\ub529 \uc911","app.setting.copyinfo":"\ubcf5\uc0ac \uc131\uacf5. src/models/settings.js\uc5d0 \uc788\ub294 defaultSettings\ub97c \uad50\uccb4\ud574 \uc8fc\uc138\uc694.","app.setting.production.hint":"\uc124\uc815 \ud310\ub12c\uc740 \uac1c\ubc1c \ud658\uacbd\uc5d0\uc11c\ub9cc \ubcf4\uc5ec\uc9d1\ub2c8\ub2e4. \uc9c1\uc811 \uc218\ub3d9\uc73c\ub85c \ubcc0\uacbd\ubc14\ub78d\ub2c8\ub2e4."},Ot=Object(h["a"])({},gt),jt={"app.setting.pagestyle":"\u6574\u4f53\u98ce\u683c\u8bbe\u7f6e","app.setting.pagestyle.dark":"\u6697\u8272\u83dc\u5355\u98ce\u683c","app.setting.pagestyle.light":"\u4eae\u8272\u83dc\u5355\u98ce\u683c","app.setting.pagestyle.realdark":"\u6697\u8272\u98ce\u683c(\u5b9e\u9a8c\u529f\u80fd)","app.setting.content-width":"\u5185\u5bb9\u533a\u57df\u5bbd\u5ea6","app.setting.content-width.fixed":"\u5b9a\u5bbd","app.setting.content-width.fluid":"\u6d41\u5f0f","app.setting.themecolor":"\u4e3b\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66ae","app.setting.themecolor.volcano":"\u706b\u5c71","app.setting.themecolor.sunset":"\u65e5\u66ae","app.setting.themecolor.cyan":"\u660e\u9752","app.setting.themecolor.green":"\u6781\u5149\u7eff","app.setting.themecolor.daybreak":"\u62c2\u6653\u84dd\uff08\u9ed8\u8ba4\uff09","app.setting.themecolor.geekblue":"\u6781\u5ba2\u84dd","app.setting.themecolor.purple":"\u9171\u7d2b","app.setting.navigationmode":"\u5bfc\u822a\u6a21\u5f0f","app.setting.regionalsettings":"\u5185\u5bb9\u533a\u57df","app.setting.regionalsettings.header":"\u9876\u680f","app.setting.regionalsettings.menu":"\u83dc\u5355","app.setting.regionalsettings.footer":"\u9875\u811a","app.setting.regionalsettings.menuHeader":"\u83dc\u5355\u5934","app.setting.sidemenu":"\u4fa7\u8fb9\u83dc\u5355\u5e03\u5c40","app.setting.topmenu":"\u9876\u90e8\u83dc\u5355\u5e03\u5c40","app.setting.mixmenu":"\u6df7\u5408\u83dc\u5355\u5e03\u5c40","app.setting.splitMenus":"\u81ea\u52a8\u5206\u5272\u83dc\u5355","app.setting.fixedheader":"\u56fa\u5b9a Header","app.setting.fixedsidebar":"\u56fa\u5b9a\u4fa7\u8fb9\u83dc\u5355","app.setting.fixedsidebar.hint":"\u4fa7\u8fb9\u83dc\u5355\u5e03\u5c40\u65f6\u53ef\u914d\u7f6e","app.setting.hideheader":"\u4e0b\u6ed1\u65f6\u9690\u85cf Header","app.setting.hideheader.hint":"\u56fa\u5b9a Header \u65f6\u53ef\u914d\u7f6e","app.setting.othersettings":"\u5176\u4ed6\u8bbe\u7f6e","app.setting.weakmode":"\u8272\u5f31\u6a21\u5f0f","app.setting.copy":"\u62f7\u8d1d\u8bbe\u7f6e","app.setting.loading":"\u6b63\u5728\u52a0\u8f7d\u4e3b\u9898","app.setting.copyinfo":"\u62f7\u8d1d\u6210\u529f\uff0c\u8bf7\u5230 src/defaultSettings.js \u4e2d\u66ff\u6362\u9ed8\u8ba4\u914d\u7f6e","app.setting.production.hint":"\u914d\u7f6e\u680f\u53ea\u5728\u5f00\u53d1\u73af\u5883\u7528\u4e8e\u9884\u89c8\uff0c\u751f\u4ea7\u73af\u5883\u4e0d\u4f1a\u5c55\u73b0\uff0c\u8bf7\u62f7\u8d1d\u540e\u624b\u52a8\u4fee\u6539\u914d\u7f6e\u6587\u4ef6"},xt=Object(h["a"])({},jt),wt={"app.setting.pagestyle":"\u6574\u9ad4\u98a8\u683c\u8a2d\u7f6e","app.setting.pagestyle.dark":"\u6697\u8272\u83dc\u55ae\u98a8\u683c","app.setting.pagestyle.realdark":"\u6697\u8272\u98a8\u683c(\u5b9e\u9a8c\u529f\u80fd)","app.setting.pagestyle.light":"\u4eae\u8272\u83dc\u55ae\u98a8\u683c","app.setting.content-width":"\u5167\u5bb9\u5340\u57df\u5bec\u5ea6","app.setting.content-width.fixed":"\u5b9a\u5bec","app.setting.content-width.fluid":"\u6d41\u5f0f","app.setting.themecolor":"\u4e3b\u984c\u8272","app.setting.themecolor.dust":"\u8584\u66ae","app.setting.themecolor.volcano":"\u706b\u5c71","app.setting.themecolor.sunset":"\u65e5\u66ae","app.setting.themecolor.cyan":"\u660e\u9752","app.setting.themecolor.green":"\u6975\u5149\u7da0","app.setting.themecolor.daybreak":"\u62c2\u66c9\u85cd\uff08\u9ed8\u8a8d\uff09","app.setting.themecolor.geekblue":"\u6975\u5ba2\u85cd","app.setting.themecolor.purple":"\u91ac\u7d2b","app.setting.navigationmode":"\u5c0e\u822a\u6a21\u5f0f","app.setting.sidemenu":"\u5074\u908a\u83dc\u55ae\u5e03\u5c40","app.setting.topmenu":"\u9802\u90e8\u83dc\u55ae\u5e03\u5c40","app.setting.mixmenu":"\u6df7\u5408\u83dc\u55ae\u5e03\u5c40","app.setting.splitMenus":"\u81ea\u52a8\u5206\u5272\u83dc\u5355","app.setting.fixedheader":"\u56fa\u5b9a Header","app.setting.fixedsidebar":"\u56fa\u5b9a\u5074\u908a\u83dc\u55ae","app.setting.fixedsidebar.hint":"\u5074\u908a\u83dc\u55ae\u5e03\u5c40\u6642\u53ef\u914d\u7f6e","app.setting.hideheader":"\u4e0b\u6ed1\u6642\u96b1\u85cf Header","app.setting.hideheader.hint":"\u56fa\u5b9a Header \u6642\u53ef\u914d\u7f6e","app.setting.othersettings":"\u5176\u4ed6\u8a2d\u7f6e","app.setting.weakmode":"\u8272\u5f31\u6a21\u5f0f","app.setting.copy":"\u62f7\u8c9d\u8a2d\u7f6e","app.setting.loading":"\u6b63\u5728\u52a0\u8f09\u4e3b\u984c","app.setting.copyinfo":"\u62f7\u8c9d\u6210\u529f\uff0c\u8acb\u5230 src/defaultSettings.js \u4e2d\u66ff\u63db\u9ed8\u8a8d\u914d\u7f6e","app.setting.production.hint":"\u914d\u7f6e\u6b04\u53ea\u5728\u958b\u767c\u74b0\u5883\u7528\u65bc\u9810\u89bd\uff0c\u751f\u7522\u74b0\u5883\u4e0d\u6703\u5c55\u73fe\uff0c\u8acb\u62f7\u8c9d\u5f8c\u624b\u52d5\u4fee\u6539\u914d\u7f6e\u6587\u4ef6"},Ct=Object(h["a"])({},wt),St={"zh-CN":xt,"zh-TW":Ct,"en-US":vt,"it-IT":yt,"ko-KR":Ot},Et=function(){if(!Object(j["a"])())return"zh-CN";var e=window.localStorage.getItem("umi_locale");return e||window.g_locale||navigator.language},Pt=function(){var e=Et();return St[e]||St["zh-CN"]},kt=Object(g["createContext"])({}),Tt=kt,Mt=function(e){var t=["sidemenu","topmenu"];return t.includes(e)?null===e||void 0===e?void 0:e.replace("menu",""):e},Nt=Mt,Rt=n("vRGJ"),Ft=n.n(Rt);function Dt(e){if(!e||"/"===e)return["/"];var t=e.split("/").filter((function(e){return e}));return t.map((function(e,n){return"/".concat(t.slice(0,n+1).join("/"))}))}var Lt=function(e){var t=e.breadcrumbName,n=e.path;return Object(v["jsx"])("a",{href:n,children:t})},It=function(e,t){var n=t.formatMessage,r=t.menu;return e.locale&&n&&!1!==(null===r||void 0===r?void 0:r.locale)?n({id:e.locale,defaultMessage:e.name}):e.name},At=function(e,t){var n=e.get(t);if(!n){var r=Array.from(e.keys())||[],o=r.find((function(e){return Ft()(e.replace("?","")).test(t)}));o&&(n=e.get(o))}return n||{path:""}},zt=function(e){var t=e.location,n=e.breadcrumbMap;return{location:t,breadcrumbMap:n}},Ht=function(e,t,n){var r=Dt(null===e||void 0===e?void 0:e.pathname),o=r.map((function(e){var r=At(t,e),o=It(r,n),a=r.hideInBreadcrumb;return o&&!a?{path:e,breadcrumbName:o,component:r.component}:{path:"",breadcrumbName:""}})).filter((function(e){return e&&e.path}));return o},Bt=function(e){var t=zt(e),n=t.location,r=t.breadcrumbMap;return n&&n.pathname&&r?Ht(n,r,e):[]},Kt=function(e,t){var n=e.breadcrumbRender,r=e.itemRender,o=t.breadcrumbProps||{},a=o.minLength,i=void 0===a?2:a,c=Bt(e),l=r||Lt,u=c;return n&&(u=n(u)||[]),(u&&u.length<i||!1===n)&&(u=void 0),{routes:u,itemRender:l}},_t=n("Qv07"),Vt=n("PjWh"),Ut=function(e){var t=Object(g["useState"])({}),n=Object(m["a"])(t,2),r=n[0],o=n[1];return Object(g["useEffect"])((function(){o(Object(Vt["a"])({layout:"object"!==Object(l["a"])(e.layout)?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar,headerTheme:e.headerTheme}))}),[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar,e.headerTheme]),r},Wt=Ut,Gt=n("3j9d"),qt=function(e){var t=e.autoClearCache,n=void 0===t||t,r=e.style,o=e.className,a=e.children,c=e.ErrorBoundary||Gt["a"];return Object(v["jsx"])(b["a"],{autoClearCache:n,children:!1===e.ErrorBoundary?Object(v["jsx"])(i.Content,{className:o,style:r,children:a}):Object(v["jsx"])(c,{children:Object(v["jsx"])(i.Content,{className:o,style:r,children:a})})})},Yt=qt,Xt=["id","defaultMessage"],Zt=["fixSiderbar","navTheme","layout"],Qt=0,$t=function(e,t){return!1===e.headerRender||e.pure?null:Object(v["jsx"])(mt,Object(h["a"])({matchMenuKeys:t},e))},Jt=function(e){return!1===e.footerRender||e.pure?null:e.footerRender?e.footerRender(Object(h["a"])({},e),Object(v["jsx"])(Ye,{})):null},en=function(e,t){var n=e.layout,r=e.navTheme,o=e.isMobile,a=e.openKeys,i=e.splitMenus,c=e.menuRender;if(!1===e.menuRender||e.pure)return null;var l=e.menuData;if(i&&(!1!==a||"mix"===n)&&!o){var u,s,d=Object(m["a"])(t,1),f=d[0];if(f)l=(null===(u=e.menuData)||void 0===u||null===(s=u.find((function(e){return e.key===f})))||void 0===s?void 0:s.routes)||[];else l=[]}var p=Ee(l||[]);if(p&&(null===p||void 0===p?void 0:p.length)<1&&i)return null;if("top"===n&&!o)return Object(v["jsx"])(Be,Object(h["a"])(Object(h["a"])({matchMenuKeys:t},e),{},{hide:!0}));var b=Object(v["jsx"])(Be,Object(h["a"])(Object(h["a"])({matchMenuKeys:t},e),{},{style:"realDark"===r?{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"}:{},menuData:p}));return c?c(e,b):b},tn=function(e,t){var n=t.pageTitleRender,r=Object(Xe["b"])(e);if(!1===n)return{title:t.title||"",id:"",pageName:""};if(n){var o=n(e,r.title,r);if("string"===typeof o)return Object(h["a"])(Object(h["a"])({},r),{},{title:o});H()("string"===typeof o,"pro-layout: renderPageTitle return value should be a string")}return r},nn=function(e,t,n){return e?t?48:n:0},rn=function(e){var t,n,r,o,a=e||{},l=a.children,s=a.onCollapse,O=a.location,j=void 0===O?{pathname:"/"}:O,x=a.contentStyle,S=a.route,P=a.defaultCollapsed,T=a.style,M=a.disableContentMargin,N=a.siderWidth,R=void 0===N?208:N,F=a.menu,D=a.isChildrenLayout,z=a.menuDataRender,H=a.actionRef,K=a.formatMessage,_=a.loading,V=Object(g["useContext"])(p["a"].ConfigContext),U=null!==(t=e.prefixCls)&&void 0!==t?t:V.getPrefixCls("pro"),G=Object(y["a"])(!1,{value:null===F||void 0===F?void 0:F.loading,onChange:null===F||void 0===F?void 0:F.onLoadingChange}),q=Object(m["a"])(G,2),Y=q[0],X=q[1],Z=Object(g["useState"])((function(){return Qt+=1,"pro-layout-".concat(Qt)})),Q=Object(m["a"])(Z,1),$=Q[0],J=Object(g["useCallback"])((function(e){var t=e.id,n=e.defaultMessage,r=Object(f["a"])(e,Xt);if(K)return K(Object(h["a"])({id:t,defaultMessage:n},r));var o=Pt();return o[t]?o[t]:n}),[K]),ee=Object(I["b"])((function(){return(null===F||void 0===F?void 0:F.params)?[$,null===F||void 0===F?void 0:F.params]:[$,{}]}),function(){var e=d(u().mark((function e(t,n){var r,o;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return X(!0),e.next=3,null===F||void 0===F||null===(r=F.request)||void 0===r?void 0:r.call(F,n,(null===S||void 0===S?void 0:S.routes)||[]);case 3:return o=e.sent,X(!1),e.abrupt("return",o);case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),te=ee.data,ne=ee.mutate,re=Object(I["c"])(),oe=re.cache;Object(g["useEffect"])((function(){return function(){oe instanceof Map&&oe.clear()}}),[]);var ae=Object(g["useMemo"])((function(){return Object(_t["a"])(te||(null===S||void 0===S?void 0:S.routes)||[],F,J,z)}),[J,F,z,te,null===S||void 0===S?void 0:S.routes]),ie=ae||{},ce=ie.breadcrumb,le=void 0===ce?{}:ce,ue=ie.breadcrumbMap,se=ie.menuData,de=void 0===se?[]:se;H&&(null===F||void 0===F?void 0:F.request)&&(H.current={reload:function(){ne()}});var fe=Object(g["useMemo"])((function(){return Object(C["b"])(j.pathname||"/",de||[],!0)}),[j.pathname,de]),pe=Object(g["useMemo"])((function(){return Array.from(new Set(fe.map((function(e){return e.key||e.path||""}))))}),[fe]),me=fe[fe.length-1]||{},he=Wt(me),ve=Object(h["a"])(Object(h["a"])({},e),he),be=ve.fixSiderbar,ye=ve.navTheme,ge=ve.layout,Oe=Object(f["a"])(ve,Zt),je=Nt(ge),xe=Object(A["a"])(),we=("sm"===xe||"xs"===xe)&&!e.disableMobile,Ce="top"!==je&&!we,Se=L((function(){return void 0!==P?P:!!we||"md"===xe}),{value:e.collapsed,onChange:s}),Ee=Object(m["a"])(Se,2),Pe=Ee[0],ke=Ee[1],Te=k(Object(h["a"])(Object(h["a"])(Object(h["a"])({prefixCls:U},e),{},{siderWidth:R},he),{},{formatMessage:J,breadcrumb:le,menu:Object(h["a"])(Object(h["a"])({},F),{},{loading:Y}),layout:je}),["className","style","breadcrumbRender"]),Me=tn(Object(h["a"])(Object(h["a"])({pathname:j.pathname},Te),{},{breadcrumbMap:ue}),e),Ne=Kt(Object(h["a"])(Object(h["a"])({},Te),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:ue}),e),Re=en(Object(h["a"])(Object(h["a"])({},Te),{},{menuData:de,onCollapse:ke,isMobile:we,theme:"dark"===ye?"dark":"light",collapsed:Pe}),pe),Fe=$t(Object(h["a"])(Object(h["a"])({},Te),{},{hasSiderMenu:!!Re,menuData:de,isMobile:we,collapsed:Pe,onCollapse:ke,theme:"dark"===ye?"dark":"light"}),pe),De=Jt(Object(h["a"])({isMobile:we,collapsed:Pe},Te)),Le=Object(g["useContext"])(Tt),Ie=Le.isChildrenLayout,Ae=void 0!==D?D:Ie,ze="".concat(U,"-basicLayout"),He=E()(e.className,"ant-design-pro",ze,(n={},Object(c["a"])(n,"screen-".concat(xe),xe),Object(c["a"])(n,"".concat(ze,"-top-menu"),"top"===je),Object(c["a"])(n,"".concat(ze,"-is-children"),Ae),Object(c["a"])(n,"".concat(ze,"-fix-siderbar"),be),Object(c["a"])(n,"".concat(ze,"-").concat(je),je),n)),Be=nn(!!Ce,Pe,R),Ke={position:"relative"};(Ae||x&&x.minHeight)&&(Ke.minHeight=0);var _e=E()("".concat(ze,"-content"),(r={},Object(c["a"])(r,"".concat(ze,"-has-header"),Fe),Object(c["a"])(r,"".concat(ze,"-content-disable-margin"),M),r));Object(g["useEffect"])((function(){var t;null===(t=e.onPageChange)||void 0===t||t.call(e,e.location)}),[j.pathname,null===(o=j.pathname)||void 0===o?void 0:o.search]);var Ve=Object(g["useState"])(!1),Ue=Object(m["a"])(Ve,2),We=Ue[0],Ge=Ue[1];return w(Me,e.title||!1),Object(v["jsx"])(W.Provider,{children:Object(v["jsx"])(Tt.Provider,{value:Object(h["a"])(Object(h["a"])({},Te),{},{breadcrumb:Ne,menuData:de,isMobile:we,collapsed:Pe,isChildrenLayout:!0,title:Me.pageName,hasSiderMenu:!!Re,hasHeader:!!Fe,siderWidth:Be,hasFooter:!!De,hasFooterToolbar:We,setHasFooterToolbar:Ge,pageTitleInfo:Me,matchMenus:fe,matchMenuKeys:pe,currentMenu:me}),children:e.pure?Object(v["jsx"])(b["a"],{autoClearCache:!0,children:l}):Object(v["jsx"])("div",{className:He,children:Object(v["jsxs"])(i,{style:Object(h["a"])({minHeight:"100%"},T),children:[Re,Object(v["jsxs"])("div",{style:Ke,className:V.getPrefixCls("layout"),children:[Fe,Object(v["jsx"])(Yt,Object(h["a"])(Object(h["a"])({autoClearCache:!1,isChildrenLayout:Ae},Oe),{},{className:_e,style:x,children:_?Object(v["jsx"])(B["a"],{}):l})),De]})]})})})})},on=function(){return Object(v["jsxs"])("svg",{width:"32px",height:"32px",viewBox:"0 0 200 200",children:[Object(v["jsxs"])("defs",{children:[Object(v["jsxs"])("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[Object(v["jsx"])("stop",{stopColor:"#4285EB",offset:"0%"}),Object(v["jsx"])("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),Object(v["jsxs"])("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[Object(v["jsx"])("stop",{stopColor:"#29CDFF",offset:"0%"}),Object(v["jsx"])("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),Object(v["jsx"])("stop",{stopColor:"#0A60FF",offset:"100%"})]}),Object(v["jsxs"])("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[Object(v["jsx"])("stop",{stopColor:"#FA816E",offset:"0%"}),Object(v["jsx"])("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),Object(v["jsx"])("stop",{stopColor:"#F51D2C",offset:"100%"})]}),Object(v["jsxs"])("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[Object(v["jsx"])("stop",{stopColor:"#FA8E7D",offset:"0%"}),Object(v["jsx"])("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),Object(v["jsx"])("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),Object(v["jsx"])("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:Object(v["jsx"])("g",{transform:"translate(-20.000000, -20.000000)",children:Object(v["jsx"])("g",{transform:"translate(20.000000, 20.000000)",children:Object(v["jsxs"])("g",{children:[Object(v["jsxs"])("g",{fillRule:"nonzero",children:[Object(v["jsxs"])("g",{children:[Object(v["jsx"])("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),Object(v["jsx"])("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),Object(v["jsx"])("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),Object(v["jsx"])("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})};rn.defaultProps=Object(h["a"])(Object(h["a"])({logo:Object(v["jsx"])(on,{})},Ce),{},{location:Object(j["a"])()?window.location:void 0});var an=rn,cn=(n("1YHl"),n("VNzZ")),ln=(n("YV/h"),n("Telt"),n("AOa7"),n("qVdP"),n("DYRE"),n("+y50")),un=n("Z97s"),sn=n("jiTG"),dn=n("2s+V"),fn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},pn=fn,mn=n("/1Lp"),hn=function(e,t){return g["createElement"](mn["a"],Object(dn["a"])(Object(dn["a"])({},e),{},{ref:t,icon:pn}))};hn.displayName="ArrowLeftOutlined";var vn=g["forwardRef"](hn),bn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"},yn=bn,gn=function(e,t){return g["createElement"](mn["a"],Object(dn["a"])(Object(dn["a"])({},e),{},{ref:t,icon:yn}))};gn.displayName="ArrowRightOutlined";var On=g["forwardRef"](gn),jn=n("dm2S"),xn=n("Tckk"),wn=n("xGeg"),Cn=n("Zm9Q"),Sn=n("H84U"),En=n("0n0R"),Pn=n("sqw5"),kn=n("XBQK"),Tn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},Mn=function(e){var t=e.prefixCls,n=e.separator,r=void 0===n?"/":n,o=e.children,a=e.menu,i=e.overlay,c=e.dropdownProps,l=Tn(e,["prefixCls","separator","children","menu","overlay","dropdownProps"]),u=g["useContext"](Sn["b"]),s=u.getPrefixCls,d=s("breadcrumb",t);var f,p=function(t){if(a||i){var n=Object(sn["a"])({},c);return"overlay"in e&&(n.overlay=i),g["createElement"](kn["a"],Object(sn["a"])({menu:a,placement:"bottom"},n),g["createElement"]("span",{className:"".concat(d,"-overlay-link")},t,g["createElement"](Pn["a"],null)))}return t};return f="href"in l?g["createElement"]("a",Object(sn["a"])({className:"".concat(d,"-link")},l),o):g["createElement"]("span",Object(sn["a"])({className:"".concat(d,"-link")},l),o),f=p(f),void 0!==o&&null!==o?g["createElement"]("li",null,f,r&&g["createElement"]("span",{className:"".concat(d,"-separator")},r)):null};Mn.__ANT_BREADCRUMB_ITEM=!0;var Nn=Mn,Rn=function(e){var t=e.children,n=g["useContext"](Sn["b"]),r=n.getPrefixCls,o=r("breadcrumb");return g["createElement"]("span",{className:"".concat(o,"-separator")},t||"/")};Rn.__ANT_BREADCRUMB_SEPARATOR=!0;var Fn=Rn,Dn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function Ln(e,t){if(!e.breadcrumbName)return null;var n=Object.keys(t).join("|"),r=e.breadcrumbName.replace(new RegExp(":(".concat(n,")"),"g"),(function(e,n){return t[n]||e}));return r}function In(e,t,n,r){var o=n.indexOf(e)===n.length-1,a=Ln(e,t);return o?g["createElement"]("span",null,a):g["createElement"]("a",{href:"#/".concat(r.join("/"))},a)}var An=function(e,t){return e=(e||"").replace(/^\//,""),Object.keys(t).forEach((function(n){e=e.replace(":".concat(n),t[n])})),e},zn=function(e,t,n){var r=Object(wn["a"])(e),o=An(t||"",n);return o&&r.push(o),r},Hn=function(e){var t,n=e.prefixCls,r=e.separator,o=void 0===r?"/":r,a=e.style,i=e.className,c=e.routes,l=e.children,u=e.itemRender,s=void 0===u?In:u,d=e.params,f=void 0===d?{}:d,p=Dn(e,["prefixCls","separator","style","className","routes","children","itemRender","params"]),m=g["useContext"](Sn["b"]),h=m.getPrefixCls,v=m.direction,b=h("breadcrumb",n);if(c&&c.length>0){var y=[];t=c.map((function(e){var t,n=An(e.path,f);n&&y.push(n),e.children&&e.children.length&&(t=g["createElement"](G["a"],{items:e.children.map((function(e){return{key:e.path||e.breadcrumbName,label:s(e,f,c,zn(y,e.path,f))}}))}));var r={separator:o};return t&&(r.overlay=t),g["createElement"](Nn,Object(sn["a"])({},r,{key:n||e.breadcrumbName}),s(e,f,c,y))}))}else l&&(t=Object(Cn["a"])(l).map((function(e,t){return e?Object(En["a"])(e,{separator:o,key:t}):e})));var O=E()(b,Object(ln["a"])({},"".concat(b,"-rtl"),"rtl"===v),i);return g["createElement"]("nav",Object(sn["a"])({className:O,style:a},p),g["createElement"]("ol",null,t))};Hn.Item=Nn,Hn.Separator=Fn;var Bn=Hn,Kn=Bn,_n=n("YMnH"),Vn=n("zeV3"),Un=n("gDlH"),Wn=function(e,t,n){return t&&n?g["createElement"](_n["a"],{componentName:"PageHeader"},(function(r){return g["createElement"]("div",{className:"".concat(e,"-back")},g["createElement"](Un["a"],{onClick:function(e){null===n||void 0===n||n(e)},className:"".concat(e,"-back-button"),"aria-label":r.back},t))})):null},Gn=function(e){return g["createElement"](Kn,Object(sn["a"])({},e))},qn=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ltr";return void 0!==e.backIcon?e.backIcon:"rtl"===t?g["createElement"](On,null):g["createElement"](vn,null)},Yn=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",r=t.title,o=t.avatar,a=t.subTitle,i=t.tags,c=t.extra,l=t.onBack,u="".concat(e,"-heading"),s=r||a||i||c;if(!s)return null;var d=qn(t,n),f=Wn(e,d,l),p=f||o||s;return g["createElement"]("div",{className:u},p&&g["createElement"]("div",{className:"".concat(u,"-left")},f,o&&g["createElement"](xn["a"],Object(sn["a"])({},o)),r&&g["createElement"]("span",{className:"".concat(u,"-title"),title:"string"===typeof r?r:void 0},r),a&&g["createElement"]("span",{className:"".concat(u,"-sub-title"),title:"string"===typeof a?a:void 0},a),i&&g["createElement"]("span",{className:"".concat(u,"-tags")},i)),c&&g["createElement"]("span",{className:"".concat(u,"-extra")},g["createElement"](Vn["b"],null,c)))},Xn=function(e,t){return t?g["createElement"]("div",{className:"".concat(e,"-footer")},t):null},Zn=function(e,t){return g["createElement"]("div",{className:"".concat(e,"-content")},t)},Qn=function(e){var t=Object(jn["a"])(!1),n=Object(un["a"])(t,2),r=n[0],o=n[1],a=function(e){var t=e.width;o(t<768,!0)};return g["createElement"](Sn["a"],null,(function(t){var n,o,i=t.getPrefixCls,c=t.pageHeader,l=t.direction,u=e.prefixCls,s=e.style,d=e.footer,f=e.children,p=e.breadcrumb,m=e.breadcrumbRender,h=e.className,v=!0;"ghost"in e?v=e.ghost:c&&"ghost"in c&&(v=c.ghost);var b=i("page-header",u),y=function(){return(null===p||void 0===p?void 0:p.routes)?Gn(p):null},O=y(),j=p&&"props"in p,x=null!==(o=null===m||void 0===m?void 0:m(e,O))&&void 0!==o?o:O,w=j?p:x,C=E()(b,h,(n={"has-breadcrumb":!!w,"has-footer":!!d},Object(ln["a"])(n,"".concat(b,"-ghost"),v),Object(ln["a"])(n,"".concat(b,"-rtl"),"rtl"===l),Object(ln["a"])(n,"".concat(b,"-compact"),r),n));return g["createElement"](ot["a"],{onResize:a},g["createElement"]("div",{className:C,style:s},w,Yn(b,e,l),f&&Zn(b,f),Xn(b,d)))}))},$n=Qn,Jn=(n("Znn+"),n("ZTPi")),er=(n("rsCp"),["children","className","extra","style","renderContent"]),tr=function(e){var t=e.children,n=e.className,r=e.extra,o=e.style,a=e.renderContent,i=Object(f["a"])(e,er),c=Object(g["useContext"])(p["a"].ConfigContext),l=c.getPrefixCls,u=e.prefixCls||l("pro"),s="".concat(u,"-footer-bar"),d=Object(g["useContext"])(Tt),m=Object(g["useMemo"])((function(){var e=d.hasSiderMenu,t=d.isMobile,n=d.siderWidth;if(e)return n?t?"100%":"calc(100% - ".concat(n,"px)"):"100%"}),[d.collapsed,d.hasSiderMenu,d.isMobile,d.siderWidth]),b=Object(v["jsxs"])(v["Fragment"],{children:[Object(v["jsx"])("div",{className:"".concat(s,"-left"),children:r}),Object(v["jsx"])("div",{className:"".concat(s,"-right"),children:t})]});return Object(g["useEffect"])((function(){return d&&(null===d||void 0===d?void 0:d.setHasFooterToolbar)?(null===d||void 0===d||d.setHasFooterToolbar(!0),function(){var e;null===d||void 0===d||null===(e=d.setHasFooterToolbar)||void 0===e||e.call(d,!1)}):function(){}}),[]),Object(v["jsx"])("div",Object(h["a"])(Object(h["a"])({className:E()(n,"".concat(s)),style:Object(h["a"])({width:m},o)},k(i,["prefixCls"])),{},{children:a?a(Object(h["a"])(Object(h["a"])(Object(h["a"])({},e),d),{},{leftWidth:m}),b):b}))},nr=tr,rr=(n("DnfT"),function(e){var t=Object(g["useContext"])(Tt),n=e.children,r=e.contentWidth,o=e.className,a=e.style,i=Object(g["useContext"])(p["a"].ConfigContext),c=i.getPrefixCls,l=e.prefixCls||c("pro"),u=r||t.contentWidth,s="".concat(l,"-grid-content");return Object(v["jsx"])("div",{className:E()(s,o,{wide:"Fixed"===u}),style:a,children:Object(v["jsx"])("div",{className:"".concat(l,"-grid-content-children"),children:n})})}),or=rr,ar=function(e){if(!e)return 1;var t=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/t},ir=function(e){var t=e.children,n=e.style,r=e.className,o=e.markStyle,a=e.markClassName,i=e.zIndex,c=void 0===i?9:i,l=e.gapX,u=void 0===l?212:l,s=e.gapY,d=void 0===s?222:s,f=e.width,b=void 0===f?120:f,y=e.height,O=void 0===y?64:y,j=e.rotate,x=void 0===j?-22:j,w=e.image,C=e.content,S=e.offsetLeft,P=e.offsetTop,k=e.fontStyle,T=void 0===k?"normal":k,M=e.fontWeight,N=void 0===M?"normal":M,R=e.fontColor,F=void 0===R?"rgba(0,0,0,.15)":R,D=e.fontSize,L=void 0===D?16:D,I=e.fontFamily,A=void 0===I?"sans-serif":I,z=e.prefixCls,H=Object(g["useContext"])(p["a"].ConfigContext),B=H.getPrefixCls,K=B("pro-layout-watermark",z),_=E()("".concat(K,"-wrapper"),r),V=E()(K,a),U=Object(g["useState"])(""),W=Object(m["a"])(U,2),G=W[0],q=W[1];return Object(g["useEffect"])((function(){var e=document.createElement("canvas"),t=e.getContext("2d"),n=ar(t),r="".concat((u+b)*n,"px"),o="".concat((d+O)*n,"px"),a=S||u/2,i=P||d/2;if(e.setAttribute("width",r),e.setAttribute("height",o),t){t.translate(a*n,i*n),t.rotate(Math.PI/180*Number(x));var c=b*n,l=O*n;if(w){var s=new Image;s.crossOrigin="anonymous",s.referrerPolicy="no-referrer",s.src=w,s.onload=function(){t.drawImage(s,0,0,c,l),q(e.toDataURL())}}else if(C){var f=Number(L)*n;t.font="".concat(T," normal ").concat(N," ").concat(f,"px/").concat(l,"px ").concat(A),t.fillStyle=F,Array.isArray(C)?null===C||void 0===C||C.forEach((function(e,n){return t.fillText(e,0,50*n)})):t.fillText(C,0,0),q(e.toDataURL())}}else console.error("\u5f53\u524d\u73af\u5883\u4e0d\u652f\u6301Canvas")}),[u,d,S,P,x,T,N,b,O,A,F,w,C,L]),Object(v["jsxs"])("div",{style:Object(h["a"])({position:"relative"},n),className:_,children:[t,Object(v["jsx"])("div",{className:V,style:Object(h["a"])(Object(h["a"])({zIndex:c,position:"absolute",left:0,top:0,width:"100%",height:"100%",backgroundSize:"".concat(u+b,"px"),pointerEvents:"none",backgroundRepeat:"repeat"},G?{backgroundImage:"url('".concat(G,"')")}:null),o)})]})},cr=ir,lr=(n("u/V1"),["title","content","pageHeaderRender","header","prefixedClassName","extraContent","style","prefixCls","breadcrumbRender"]),ur=["children","loading","className","style","footer","affixProps","ghost","fixedHeader","breadcrumbRender"];function sr(e){return"object"===Object(l["a"])(e)?e:{spinning:e}}var dr=function(e){var t=e.tabList,n=e.tabActiveKey,r=e.onTabChange,o=e.tabBarExtraContent,a=e.tabProps,i=e.prefixedClassName;return Array.isArray(t)||o?Object(v["jsx"])(Jn["a"],Object(h["a"])(Object(h["a"])({className:"".concat(i,"-tabs"),activeKey:n,onChange:function(e){r&&r(e)},tabBarExtraContent:o},a),{},{children:null===t||void 0===t?void 0:t.map((function(e,t){return Object(g["createElement"])(Jn["a"].TabPane,Object(h["a"])(Object(h["a"])({},e),{},{tab:e.tab,key:e.key||t}))}))})):null},fr=function(e,t,n){return e||t?Object(v["jsx"])("div",{className:"".concat(n,"-detail"),children:Object(v["jsx"])("div",{className:"".concat(n,"-main"),children:Object(v["jsxs"])("div",{className:"".concat(n,"-row"),children:[e&&Object(v["jsx"])("div",{className:"".concat(n,"-content"),children:e}),t&&Object(v["jsx"])("div",{className:"".concat(n,"-extraContent"),children:t})]})})}):null},pr=function(e){var t,n=Object(g["useContext"])(Tt),r=e.title,o=e.content,a=e.pageHeaderRender,i=e.header,c=e.prefixedClassName,l=e.extraContent,u=(e.style,e.prefixCls),s=e.breadcrumbRender,d=Object(f["a"])(e,lr),p=Object(g["useMemo"])((function(){if(s)return s}),[s]);if(!1===a)return null;if(a)return Object(v["jsxs"])(v["Fragment"],{children:[" ",a(Object(h["a"])(Object(h["a"])({},e),n))]});var m=r;r||!1===r||(m=n.title);var b=Object(h["a"])(Object(h["a"])(Object(h["a"])({},n),{},{title:m},d),{},{footer:dr(Object(h["a"])(Object(h["a"])({},d),{},{breadcrumbRender:s,prefixedClassName:c}))},i),y=b.breadcrumb,O=(!y||!(null===y||void 0===y?void 0:y.itemRender)&&!(null===y||void 0===y||null===(t=y.routes)||void 0===t?void 0:t.length))&&!s;return["title","subTitle","extra","tags","footer","avatar","backIcon"].every((function(e){return!b[e]}))&&O&&!o&&!l?null:Object(v["jsx"])("div",{className:"".concat(c,"-warp"),children:Object(v["jsx"])($n,Object(h["a"])(Object(h["a"])({},b),{},{breadcrumb:!1===s?void 0:Object(h["a"])(Object(h["a"])({},b.breadcrumb),n.breadcrumbProps),breadcrumbRender:p,prefixCls:u,children:(null===i||void 0===i?void 0:i.children)||fr(o,l,c)}))})},mr=function(e){var t,n,r=e.children,o=e.loading,a=void 0!==o&&o,i=e.className,l=e.style,u=e.footer,s=e.affixProps,d=e.ghost,m=e.fixedHeader,b=e.breadcrumbRender,y=Object(f["a"])(e,ur),j=Object(g["useContext"])(Tt),x=Object(g["useContext"])(p["a"].ConfigContext),w=x.getPrefixCls,C=e.prefixCls||w("pro"),S="".concat(C,"-page-container"),P=E()(S,i,(t={},Object(c["a"])(t,"".concat(C,"-page-container-ghost"),d),Object(c["a"])(t,"".concat(C,"-page-container-with-footer"),u),t)),k=Object(g["useMemo"])((function(){return r?Object(v["jsxs"])(v["Fragment"],{children:[Object(v["jsx"])("div",{className:"".concat(S,"-children-content"),children:r}),j.hasFooterToolbar&&Object(v["jsx"])("div",{style:{height:48,marginTop:24}})]}):null}),[r,S,j.hasFooterToolbar]),T=Object(g["useMemo"])((function(){var e;return 0!=b&&(b||(null===y||void 0===y||null===(e=y.header)||void 0===e?void 0:e.breadcrumbRender))}),[b,null===y||void 0===y||null===(n=y.header)||void 0===n?void 0:n.breadcrumbRender]),M=Object(v["jsx"])(pr,Object(h["a"])(Object(h["a"])({},y),{},{breadcrumbRender:T,ghost:d,prefixCls:void 0,prefixedClassName:S})),N=Object(g["useMemo"])((function(){if(O.a.isValidElement(a))return a;if("boolean"===typeof a&&!a)return null;var e=sr(a);return e.spinning?Object(v["jsx"])(B["a"],Object(h["a"])({},e)):null}),[a]),R=Object(g["useMemo"])((function(){var t=N||k;if(e.waterMarkProps||j.waterMarkProps){var n=Object(h["a"])(Object(h["a"])({},j.waterMarkProps),e.waterMarkProps);return Object(v["jsx"])(cr,Object(h["a"])(Object(h["a"])({},n),{},{children:t}))}return t}),[e.waterMarkProps,j.waterMarkProps,N,k]);return Object(v["jsxs"])("div",{style:l,className:P,children:[m&&M?Object(v["jsx"])(cn["a"],Object(h["a"])(Object(h["a"])({offsetTop:j.hasHeader&&j.fixedHeader?j.headerHeight:0},s),{},{children:M})):M,R&&Object(v["jsx"])(or,{children:R}),u&&Object(v["jsx"])(nr,{prefixCls:C,children:u})]})},hr=mr,vr=hr;t["b"]=an},IewF:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n("A3Fk");function o(){o=function(){return t};var e,t={},n=Object.prototype,a=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},c="function"==typeof Symbol?Symbol:{},l=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",s=c.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var o=t&&t.prototype instanceof g?t:g,a=Object.create(o.prototype),c=new R(r||[]);return i(a,"_invoke",{value:k(e,n,c)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var m="suspendedStart",h="suspendedYield",v="executing",b="completed",y={};function g(){}function O(){}function j(){}var x={};d(x,l,(function(){return this}));var w=Object.getPrototypeOf,C=w&&w(w(F([])));C&&C!==n&&a.call(C,l)&&(x=C);var S=j.prototype=g.prototype=Object.create(x);function E(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(o,i,c,l){var u=p(e[o],e,i);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==Object(r["a"])(d)&&a.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,c,l)}),(function(e){n("throw",e,c,l)})):t.resolve(d).then((function(e){s.value=e,c(s)}),(function(e){return n("throw",e,c,l)}))}l(u.arg)}var o;i(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(a,a):a()}})}function k(t,n,r){var o=m;return function(a,i){if(o===v)throw new Error("Generator is already running");if(o===b){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var l=T(c,r);if(l){if(l===y)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===m)throw o=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=v;var u=p(t,n,r);if("normal"===u.type){if(o=r.done?b:h,u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=b,r.method="throw",r.arg=u.arg)}}}function T(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator["return"]&&(n.method="return",n.arg=e,T(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var a=p(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,y;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function M(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function R(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(M,this),this.reset(!0)}function F(t){if(t||""===t){var n=t[l];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(a.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(Object(r["a"])(t)+" is not iterable")}return O.prototype=j,i(S,"constructor",{value:j,configurable:!0}),i(j,"constructor",{value:O,configurable:!0}),O.displayName=d(j,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===O||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,j):(e.__proto__=j,d(e,s,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},E(P.prototype),d(P.prototype,u,(function(){return this})),t.AsyncIterator=P,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new P(f(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},E(S),d(S,s,"Generator"),d(S,l,(function(){return this})),d(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=F,R.prototype={constructor:R,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=a.call(i,"catchLoc"),u=a.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;N(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:F(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}},Imx0:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n("q1tI");function o(e,t){return u(e)||l(e,t)||i(e,t)||a()}function a(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(e,t){if(e){if("string"===typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function l(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,a=void 0;try{for(var i,c=e[Symbol.iterator]();!(r=(i=c.next()).done);r=!0)if(n.push(i.value),t&&n.length===t)break}catch(l){o=!0,a=l}finally{try{r||null==c["return"]||c["return"]()}finally{if(o)throw a}}return n}}function u(e){if(Array.isArray(e))return e}function s(e,t){var n=t||{},a=n.defaultValue,i=n.value,c=n.onChange,l=n.postState,u=r["useState"]((function(){return void 0!==i?i:void 0!==a?"function"===typeof a?a():a:"function"===typeof e?e():e})),s=o(u,2),d=s[0],f=s[1],p=void 0!==i?i:d;function m(e){f(e),p!==e&&c&&c(e,p)}l&&(p=l(p));var h=r["useRef"](!0);return r["useEffect"]((function(){h.current?h.current=!1:void 0===i&&f(i)}),[i]),[p,m]}},LQCs:function(e,t,n){"use strict";n.d(t,"c",(function(){return fe})),n.d(t,"a",(function(){return ge})),n.d(t,"d",(function(){return Oe}));n("GNNt");var r=n("wEI+");function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function a(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function i(e){var t=a(e,"string");return"symbol"===o(t)?t:String(t)}function c(e,t,n){return t=i(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function d(e,t){if(e){if("string"===typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function f(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=d(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){c=!0,a=e},f:function(){try{i||null==n["return"]||n["return"]()}finally{if(c)throw a}}}}var p=n("nKUr"),m=n("+Gva"),h=n("q1tI"),v=n.n(h),b=n("Lpa7"),y={moneySymbol:"$",form:{lightFilter:{more:"\u0627\u0644\u0645\u0632\u064a\u062f",clear:"\u0646\u0638\u0641",confirm:"\u062a\u0623\u0643\u064a\u062f",itemUnit:"\u0639\u0646\u0627\u0635\u0631"}},tableForm:{search:"\u0627\u0628\u062d\u062b",reset:"\u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646",submit:"\u0627\u0631\u0633\u0627\u0644",collapsed:"\u0645\u064f\u0642\u0644\u0635",expand:"\u0645\u064f\u0648\u0633\u0639",inputPlaceholder:"\u0627\u0644\u0631\u062c\u0627\u0621 \u0627\u0644\u0625\u062f\u062e\u0627\u0644",selectPlaceholder:"\u0627\u0644\u0631\u062c\u0627\u0621 \u0627\u0644\u0625\u062e\u062a\u064a\u0627\u0631"},alert:{clear:"\u0646\u0638\u0641",selected:"\u0645\u062d\u062f\u062f",item:"\u0639\u0646\u0635\u0631"},pagination:{total:{range:" ",total:"\u0645\u0646",item:"\u0639\u0646\u0627\u0635\u0631"}},tableToolBar:{leftPin:"\u062b\u0628\u062a \u0639\u0644\u0649 \u0627\u0644\u064a\u0633\u0627\u0631",rightPin:"\u062b\u0628\u062a \u0639\u0644\u0649 \u0627\u0644\u064a\u0645\u064a\u0646",noPin:"\u0627\u0644\u063a\u0627\u0621 \u0627\u0644\u062a\u062b\u0628\u064a\u062a",leftFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064a\u0633\u0627\u0631",rightFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064a\u0645\u064a\u0646",noFixedTitle:"\u0625\u0644\u063a\u0627\u0621 \u0627\u0644\u0625\u0644\u0635\u0627\u0642",reset:"\u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646",columnDisplay:"\u0627\u0644\u0623\u0639\u0645\u062f\u0629 \u0627\u0644\u0645\u0639\u0631\u0648\u0636\u0629",columnSetting:"\u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a",fullScreen:"\u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",exitFullScreen:"\u0627\u0644\u062e\u0631\u0648\u062c \u0645\u0646 \u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",reload:"\u062a\u062d\u062f\u064a\u062b",density:"\u0627\u0644\u0643\u062b\u0627\u0641\u0629",densityDefault:"\u0627\u0641\u062a\u0631\u0627\u0636\u064a",densityLarger:"\u0623\u0643\u0628\u0631",densityMiddle:"\u0648\u0633\u0637",densitySmall:"\u0645\u062f\u0645\u062c"},stepsForm:{next:"\u0627\u0644\u062a\u0627\u0644\u064a",prev:"\u0627\u0644\u0633\u0627\u0628\u0642",submit:"\u0623\u0646\u0647\u0649"},loginForm:{submitText:"\u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u062f\u062e\u0648\u0644"},editableTable:{action:{save:"\u0623\u0646\u0642\u0630",cancel:"\u0625\u0644\u063a\u0627\u0621 \u0627\u0644\u0623\u0645\u0631",delete:"\u062d\u0630\u0641",add:"\u0625\u0636\u0627\u0641\u0629 \u0635\u0641 \u0645\u0646 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a"}},switch:{open:"\u0645\u0641\u062a\u0648\u062d",close:"\u063a\u0644\u0642"}},g={moneySymbol:"\u20ac",form:{lightFilter:{more:"M\xe1is",clear:"Limpar",confirm:"Confirmar",itemUnit:"Elementos"}},tableForm:{search:"Cercar",reset:"Netejar",submit:"Enviar",collapsed:"Expandir",expand:"Col\xb7lapsar",inputPlaceholder:"Introdu\xefu valor",selectPlaceholder:"Seleccioneu valor"},alert:{clear:"Netejar",selected:"Seleccionat",item:"Article"},pagination:{total:{range:" ",total:"de",item:"articles"}},tableToolBar:{leftPin:"Pin a l'esquerra",rightPin:"Pin a la dreta",noPin:"Sense Pin",leftFixedTitle:"Fixat a l'esquerra",rightFixedTitle:"Fixat a la dreta",noFixedTitle:"Sense fixar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xf3",fullScreen:"Pantalla Completa",exitFullScreen:"Sortir Pantalla Completa",reload:"Refrescar",density:"Densitat",densityDefault:"Per Defecte",densityLarger:"Llarg",densityMiddle:"Mitj\xe0",densitySmall:"Compacte"},stepsForm:{next:"Seg\xfcent",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Gardar",cancel:"Cancelar",delete:"Eliminar",add:"engadir unha fila de datos"}},switch:{open:"aberto",close:"pechar"}},O={moneySymbol:"\u20ac",form:{lightFilter:{more:"Mehr",clear:"Zur\xfccksetzen",confirm:"Best\xe4tigen",itemUnit:"Eintr\xe4ge"}},tableForm:{search:"Suchen",reset:"Zur\xfccksetzen",submit:"Absenden",collapsed:"Zeige mehr",expand:"Zeige weniger",inputPlaceholder:"Bitte eingeben",selectPlaceholder:"Bitte ausw\xe4hlen"},alert:{clear:"Zur\xfccksetzen",selected:"Ausgew\xe4hlt",item:"Eintrag"},pagination:{total:{range:" ",total:"von",item:"Eintr\xe4gen"}},tableToolBar:{leftPin:"Links anheften",rightPin:"Rechts anheften",noPin:"Nicht angeheftet",leftFixedTitle:"Links fixiert",rightFixedTitle:"Rechts fixiert",noFixedTitle:"Nicht fixiert",reset:"Zur\xfccksetzen",columnDisplay:"Angezeigte Reihen",columnSetting:"Einstellungen",fullScreen:"Vollbild",exitFullScreen:"Vollbild verlassen",reload:"Aktualisieren",density:"Abstand",densityDefault:"Standard",densityLarger:"Gr\xf6\xdfer",densityMiddle:"Mittel",densitySmall:"Kompakt"},stepsForm:{next:"Weiter",prev:"Zur\xfcck",submit:"Abschlie\xdfen"},loginForm:{submitText:"Anmelden"},editableTable:{action:{save:"Retten",cancel:"Abbrechen",delete:"L\xf6schen",add:"Hinzuf\xfcgen einer Datenzeile"}},switch:{open:"offen",close:"schlie\xdfen"}},j={moneySymbol:"\xa3",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed the left",rightFixedTitle:"Fixed the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},x={moneySymbol:"$",deleteThisLine:"Delete this line",copyThisLine:"Copy this line",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed the left",rightFixedTitle:"Fixed the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},w={moneySymbol:"\u20ac",form:{lightFilter:{more:"M\xe1s",clear:"Limpiar",confirm:"Confirmar",itemUnit:"art\xedculos"}},tableForm:{search:"Buscar",reset:"Limpiar",submit:"Submit",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Ingrese valor",selectPlaceholder:"Seleccione valor"},alert:{clear:"Limpiar",selected:"Seleccionado",item:"Articulo"},pagination:{total:{range:" ",total:"de",item:"art\xedculos"}},tableToolBar:{leftPin:"Pin a la izquierda",rightPin:"Pin a la derecha",noPin:"Sin Pin",leftFixedTitle:"Fijado a la izquierda",rightFixedTitle:"Fijado a la derecha",noFixedTitle:"Sin Fijar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xf3n",fullScreen:"Pantalla Completa",exitFullScreen:"Salir Pantalla Completa",reload:"Refrescar",density:"Densidad",densityDefault:"Por Defecto",densityLarger:"Largo",densityMiddle:"Medio",densitySmall:"Compacto"},stepsForm:{next:"Siguiente",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Descartar",delete:"Borrar",add:"a\xf1adir una fila de datos"}},switch:{open:"abrir",close:"cerrar"}},C={moneySymbol:"\u062a\u0648\u0645\u0627\u0646",form:{lightFilter:{more:"\u0628\u06cc\u0634\u062a\u0631",clear:"\u067e\u0627\u06a9 \u06a9\u0631\u062f\u0646",confirm:"\u062a\u0627\u06cc\u06cc\u062f",itemUnit:"\u0645\u0648\u0631\u062f"}},tableForm:{search:"\u062c\u0633\u062a\u062c\u0648",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06cc",submit:"\u062a\u0627\u06cc\u06cc\u062f",collapsed:"\u0646\u0645\u0627\u06cc\u0634 \u0628\u06cc\u0634\u062a\u0631",expand:"\u0646\u0645\u0627\u06cc\u0634 \u06a9\u0645\u062a\u0631",inputPlaceholder:"\u067e\u06cc\u062f\u0627 \u06a9\u0646\u06cc\u062f",selectPlaceholder:"\u0627\u0646\u062a\u062e\u0627\u0628 \u06a9\u0646\u06cc\u062f"},alert:{clear:"\u067e\u0627\u06a9 \u0633\u0627\u0632\u06cc",selected:"\u0627\u0646\u062a\u062e\u0627\u0628",item:"\u0645\u0648\u0631\u062f"},pagination:{total:{range:" ",total:"\u0627\u0632",item:"\u0645\u0648\u0631\u062f"}},tableToolBar:{leftPin:"\u0633\u0646\u062c\u0627\u0642 \u0628\u0647 \u0686\u067e",rightPin:"\u0633\u0646\u062c\u0627\u0642 \u0628\u0647 \u0631\u0627\u0633\u062a",noPin:"\u0633\u0646\u062c\u0627\u0642 \u0646\u0634\u062f\u0647",leftFixedTitle:"\u062b\u0627\u0628\u062a \u0634\u062f\u0647 \u062f\u0631 \u0686\u067e",rightFixedTitle:"\u062b\u0627\u0628\u062a \u0634\u062f\u0647 \u062f\u0631 \u0631\u0627\u0633\u062a",noFixedTitle:"\u0634\u0646\u0627\u0648\u0631",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06cc",columnDisplay:"\u0646\u0645\u0627\u06cc\u0634 \u0647\u0645\u0647",columnSetting:"\u062a\u0646\u0638\u06cc\u0645\u0627\u062a",fullScreen:"\u062a\u0645\u0627\u0645 \u0635\u0641\u062d\u0647",exitFullScreen:"\u062e\u0631\u0648\u062c \u0627\u0632 \u062d\u0627\u0644\u062a \u062a\u0645\u0627\u0645 \u0635\u0641\u062d\u0647",reload:"\u062a\u0627\u0632\u0647 \u0633\u0627\u0632\u06cc",density:"\u062a\u0631\u0627\u06a9\u0645",densityDefault:"\u067e\u06cc\u0634 \u0641\u0631\u0636",densityLarger:"\u0628\u0632\u0631\u06af",densityMiddle:"\u0645\u062a\u0648\u0633\u0637",densitySmall:"\u06a9\u0648\u0686\u06a9"},stepsForm:{next:"\u0628\u0639\u062f\u06cc",prev:"\u0642\u0628\u0644\u06cc",submit:"\u0627\u062a\u0645\u0627\u0645"},loginForm:{submitText:"\u0648\u0631\u0648\u062f"},editableTable:{action:{save:"\u0630\u062e\u06cc\u0631\u0647",cancel:"\u0644\u063a\u0648",delete:"\u062d\u0630\u0641",add:"\u06cc\u06a9 \u0631\u062f\u06cc\u0641 \u062f\u0627\u062f\u0647 \u0627\u0636\u0627\u0641\u0647 \u06a9\u0646\u06cc\u062f"}},switch:{open:"\u0628\u0627\u0632",close:"\u0646\u0632\u062f\u06cc\u06a9"}},S={moneySymbol:"\u20ac",form:{lightFilter:{more:"Plus",clear:"Effacer",confirm:"Confirmer",itemUnit:"Items"}},tableForm:{search:"Rechercher",reset:"R\xe9initialiser",submit:"Envoyer",collapsed:"Agrandir",expand:"R\xe9duire",inputPlaceholder:"Entrer une valeur",selectPlaceholder:"S\xe9lectionner une valeur"},alert:{clear:"R\xe9initialiser",selected:"S\xe9lectionn\xe9",item:"Item"},pagination:{total:{range:" ",total:"sur",item:"\xe9l\xe9ments"}},tableToolBar:{leftPin:"\xc9pingler \xe0 gauche",rightPin:"\xc9pingler \xe0 gauche",noPin:"Sans \xe9pingle",leftFixedTitle:"Fixer \xe0 gauche",rightFixedTitle:"Fixer \xe0 droite",noFixedTitle:"Non fix\xe9",reset:"R\xe9initialiser",columnDisplay:"Affichage colonne",columnSetting:"R\xe9glages",fullScreen:"Plein \xe9cran",exitFullScreen:"Quitter Plein \xe9cran",reload:"Rafraichir",density:"Densit\xe9",densityDefault:"Par d\xe9faut",densityLarger:"Larger",densityMiddle:"Moyenne",densitySmall:"Compacte"},stepsForm:{next:"Suivante",prev:"Pr\xe9c\xe9dente",submit:"Finaliser"},loginForm:{submitText:"Se connecter"},editableTable:{action:{save:"Sauvegarder",cancel:"Annuler",delete:"Supprimer",add:"ajouter une ligne de donn\xe9es"}},switch:{open:"ouvert",close:"pr\xe8s"}},E={moneySymbol:"kn",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pretra\u017ei",reset:"Poni\u0161ti",submit:"Potvrdi",collapsed:"Ra\u0161iri",expand:"Skupi",inputPlaceholder:"Unesite",selectPlaceholder:"Odaberite"},alert:{clear:"O\u010disti",selected:"Odaberi",item:"stavke"},pagination:{total:{range:" ",total:"od",item:"stavke"}},tableToolBar:{leftPin:"Prika\u010di lijevo",rightPin:"Prika\u010di desno",noPin:"Bez prika\u010denja",leftFixedTitle:"Fiksiraj lijevo",rightFixedTitle:"Fiksiraj desno",noFixedTitle:"Bez fiksiranja",reset:"Resetiraj",columnDisplay:"Prikaz stupaca",columnSetting:"Postavke",fullScreen:"Puni zaslon",exitFullScreen:"Iza\u0111i iz punog zaslona",reload:"Ponovno u\u010ditaj",density:"Veli\u010dina",densityDefault:"Zadano",densityLarger:"Veliko",densityMiddle:"Srednje",densitySmall:"Malo"},stepsForm:{next:"Sljede\u0107i",prev:"Prethodni",submit:"Kraj"},loginForm:{submitText:"Prijava"},editableTable:{action:{save:"Spremi",cancel:"Odustani",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"otvori",close:"zatvori"}},P={moneySymbol:"RP",form:{lightFilter:{more:"Lebih",clear:"Hapus",confirm:"Konfirmasi",itemUnit:"Unit"}},tableForm:{search:"Cari",reset:"Atur ulang",submit:"Kirim",collapsed:"Lebih sedikit",expand:"Lebih banyak",inputPlaceholder:"Masukkan pencarian",selectPlaceholder:"Pilih"},alert:{clear:"Hapus",selected:"Dipilih",item:"Butir"},pagination:{total:{range:" ",total:"Dari",item:"Butir"}},tableToolBar:{leftPin:"Pin kiri",rightPin:"Pin kanan",noPin:"Tidak ada pin",leftFixedTitle:"Rata kiri",rightFixedTitle:"Rata kanan",noFixedTitle:"Tidak tetap",reset:"Atur ulang",columnDisplay:"Tampilan kolom",columnSetting:"Pengaturan",fullScreen:"Layar penuh",exitFullScreen:"Keluar layar penuh",reload:"Atur ulang",density:"Kerapatan",densityDefault:"Standar",densityLarger:"Lebih besar",densityMiddle:"Sedang",densitySmall:"Rapat"},stepsForm:{next:"Selanjutnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Login"},editableTable:{action:{save:"simpan",cancel:"batal",delete:"hapus",add:"Tambahkan baris data"}},switch:{open:"buka",close:"tutup"}},k={moneySymbol:"\u20ac",form:{lightFilter:{more:"pi\xf9",clear:"pulisci",confirm:"conferma",itemUnit:"elementi"}},tableForm:{search:"Filtra",reset:"Pulisci",submit:"Invia",collapsed:"Espandi",expand:"Contrai",inputPlaceholder:"Digita",selectPlaceholder:"Seleziona"},alert:{clear:"Rimuovi",selected:"Selezionati",item:"elementi"},pagination:{total:{range:" ",total:"di",item:"elementi"}},tableToolBar:{leftPin:"Fissa a sinistra",rightPin:"Fissa a destra",noPin:"Ripristina posizione",leftFixedTitle:"Fissato a sinistra",rightFixedTitle:"Fissato a destra",noFixedTitle:"Non fissato",reset:"Ripristina",columnDisplay:"Disposizione colonne",columnSetting:"Impostazioni",fullScreen:"Modalit\xe0 schermo intero",exitFullScreen:"Esci da modalit\xe0 schermo intero",reload:"Ricarica",density:"Grandezza tabella",densityDefault:"predefinito",densityLarger:"Grande",densityMiddle:"Media",densitySmall:"Compatta"},stepsForm:{next:"successivo",prev:"precedente",submit:"finisci"},loginForm:{submitText:"Accedi"},editableTable:{action:{save:"salva",cancel:"annulla",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"chiudi"}},T={moneySymbol:"\xa5",form:{lightFilter:{more:"\u3082\u3063\u3068",clear:"\u660e\u78ba",confirm:"\u78ba\u8a8d",itemUnit:"\u9805\u76ee"}},tableForm:{search:"\u691c\u7d22",reset:"\u30ea\u30bb\u30c3\u30c8",submit:"\u63d0\u4ea4",collapsed:"\u5c55\u958b",expand:"\u53ce\u7d0d",inputPlaceholder:"\u5165\u529b\u3057\u3066\u304f\u3060\u3055\u3044",selectPlaceholder:"\u9078\u629e\u3057\u3066\u304f\u3060\u3055\u3044"},alert:{clear:"\u30af\u30ea\u30a2",selected:"\u9078\u629e\u3057\u305f",item:"\u9805\u76ee"},pagination:{total:{range:"\u8a18\u4e8b",total:"/\u5408\u8a08",item:" "}},tableToolBar:{leftPin:"\u5de6\u306b\u56fa\u5b9a",rightPin:"\u53f3\u306b\u56fa\u5b9a",noPin:"\u30ad\u30e3\u30f3\u30bb\u30eb",leftFixedTitle:"\u5de6\u306b\u56fa\u5b9a\u3055\u308c\u305f\u9805\u76ee",rightFixedTitle:"\u53f3\u306b\u56fa\u5b9a\u3055\u308c\u305f\u9805\u76ee",noFixedTitle:"\u56fa\u5b9a\u3055\u308c\u3066\u306a\u3044\u9805\u76ee",reset:"\u30ea\u30bb\u30c3\u30c8",columnDisplay:"\u8868\u793a\u5217",columnSetting:"\u5217\u8868\u793a\u8a2d\u5b9a",fullScreen:"\u30d5\u30eb\u30b9\u30af\u30ea\u30fc\u30f3",exitFullScreen:"\u7d42\u4e86",reload:"\u66f4\u65b0",density:"\u884c\u9ad8",densityDefault:"\u30c7\u30d5\u30a9\u30eb\u30c8",densityLarger:"\u9ed8\u8ba4",densityMiddle:"\u4e2d",densitySmall:"\u5c0f"},stepsForm:{next:"\u6b21\u306e\u30b9\u30c6\u30c3\u30d7",prev:"\u524d",submit:"\u9001\u4fe1"},loginForm:{submitText:"\u30ed\u30b0\u30a4\u30f3"},editableTable:{action:{save:"\u6551\u3046",cancel:"\u30ad\u30e3\u30f3\u30bb\u30eb",delete:"\u524a\u9664",add:"1\u884c\u306e\u30c7\u30fc\u30bf\u3092\u8ffd\u52a0\u3057\u307e\u3059"}},switch:{open:"\u30aa\u30fc\u30d7\u30f3",close:"\u8fd1\u3044"}},M={moneySymbol:"\u20a9",form:{lightFilter:{more:"\ub354\ubcf4\uae30",clear:"\ucde8\uc18c",confirm:"\ud655\uc778",itemUnit:"\uac74\uc218"}},tableForm:{search:"\uc870\ud68c",reset:"\ucd08\uae30\ud654",submit:"\uc81c\ucd9c",collapsed:"\ud655\uc7a5",expand:"\ub2eb\uae30",inputPlaceholder:"\uc785\ub825\ud574 \uc8fc\uc138\uc694",selectPlaceholder:"\uc120\ud0dd\ud574 \uc8fc\uc138\uc694"},alert:{clear:"\ucde8\uc18c",selected:"\uc120\ud0dd",item:"\uac74"},pagination:{total:{range:" ",total:"/ \ucd1d",item:"\uac74"}},tableToolBar:{leftPin:"\uc67c\ucabd\uc73c\ub85c \ud540",rightPin:"\uc624\ub978\ucabd\uc73c\ub85c \ud540",noPin:"\ud540 \uc81c\uac70",leftFixedTitle:"\uc67c\ucabd\uc73c\ub85c \uace0\uc815",rightFixedTitle:"\uc624\ub978\ucabd\uc73c\ub85c \uace0\uc815",noFixedTitle:"\ube44\uace0\uc815",reset:"\ucd08\uae30\ud654",columnDisplay:"\uceec\ub7fc \ud45c\uc2dc",columnSetting:"\uc124\uc815",fullScreen:"\uc804\uccb4 \ud654\uba74",exitFullScreen:"\uc804\uccb4 \ud654\uba74 \ucde8\uc18c",reload:"\ub2e4\uc2dc \uc77d\uae30",density:"\uc5ec\ubc31",densityDefault:"\uae30\ubcf8",densityLarger:"\ub9ce\uc740 \uc5ec\ubc31",densityMiddle:"\uc911\uac04 \uc5ec\ubc31",densitySmall:"\uc881\uc740 \uc5ec\ubc31"},stepsForm:{next:"\ub2e4\uc74c",prev:"\uc774\uc804",submit:"\uc885\ub8cc"},loginForm:{submitText:"\ub85c\uadf8\uc778"},editableTable:{action:{save:"\uc800\uc7a5",cancel:"\ucde8\uc18c",delete:"\uc0ad\uc81c",add:"\ub370\uc774\ud130 \ud589 \ucd94\uac00"}},switch:{open:"\uc5f4",close:"\uac00\uae4c \uc6b4"}},N={moneySymbol:"\u20ae",form:{lightFilter:{more:"\u0418\u043b\u04af\u04af",clear:"\u0426\u044d\u0432\u044d\u0440\u043b\u044d\u0445",confirm:"\u0411\u0430\u0442\u0430\u043b\u0433\u0430\u0430\u0436\u0443\u0443\u043b\u0430\u0445",itemUnit:"\u041d\u044d\u0433\u0436\u04af\u04af\u0434"}},tableForm:{search:"\u0425\u0430\u0439\u0445",reset:"\u0428\u0438\u043d\u044d\u0447\u043b\u044d\u0445",submit:"\u0418\u043b\u0433\u044d\u044d\u0445",collapsed:"\u04e8\u0440\u0433\u04e9\u0442\u0433\u04e9\u0445",expand:"\u0425\u0443\u0440\u0430\u0430\u0445",inputPlaceholder:"\u0423\u0442\u0433\u0430 \u043e\u0440\u0443\u0443\u043b\u043d\u0430 \u0443\u0443",selectPlaceholder:"\u0423\u0442\u0433\u0430 \u0441\u043e\u043d\u0433\u043e\u043d\u043e \u0443\u0443"},alert:{clear:"\u0426\u044d\u0432\u044d\u0440\u043b\u044d\u0445",selected:"\u0421\u043e\u043d\u0433\u043e\u0433\u0434\u0441\u043e\u043d",item:"\u041d\u044d\u0433\u0436"},pagination:{total:{range:" ",total:"\u041d\u0438\u0439\u0442",item:"\u043c\u04e9\u0440"}},tableToolBar:{leftPin:"\u0417\u04af\u04af\u043d \u0442\u0438\u0439\u0448 \u0431\u044d\u0445\u043b\u044d\u0445",rightPin:"\u0411\u0430\u0440\u0443\u0443\u043d \u0442\u0438\u0439\u0448 \u0431\u044d\u0445\u043b\u044d\u0445",noPin:"\u0411\u044d\u0445\u043b\u044d\u0445\u0433\u04af\u0439",leftFixedTitle:"\u0417\u04af\u04af\u043d \u0437\u044d\u0440\u044d\u0433\u0446\u04af\u04af\u043b\u044d\u0445",rightFixedTitle:"\u0411\u0430\u0440\u0443\u0443\u043d \u0437\u044d\u0440\u044d\u0433\u0446\u04af\u04af\u043b\u044d\u0445",noFixedTitle:"\u0417\u044d\u0440\u044d\u0433\u0446\u04af\u04af\u043b\u044d\u0445\u0433\u04af\u0439",reset:"\u0428\u0438\u043d\u044d\u0447\u043b\u044d\u0445",columnDisplay:"\u0411\u0430\u0433\u0430\u043d\u0430\u0430\u0440 \u0445\u0430\u0440\u0443\u0443\u043b\u0430\u0445",columnSetting:"\u0422\u043e\u0445\u0438\u0440\u0433\u043e\u043e",fullScreen:"\u0411\u04af\u0442\u044d\u043d \u0434\u044d\u043b\u0433\u044d\u0446\u044d\u044d\u0440",exitFullScreen:"\u0411\u04af\u0442\u044d\u043d \u0434\u044d\u043b\u0433\u044d\u0446 \u0446\u0443\u0446\u043b\u0430\u0445",reload:"\u0428\u0438\u043d\u044d\u0447\u043b\u044d\u0445",density:"\u0425\u044d\u043c\u0436\u044d\u044d",densityDefault:"\u0425\u044d\u0432\u0438\u0439\u043d",densityLarger:"\u0422\u043e\u043c",densityMiddle:"\u0414\u0443\u043d\u0434",densitySmall:"\u0416\u0438\u0436\u0438\u0433"},stepsForm:{next:"\u0414\u0430\u0440\u0430\u0430\u0445",prev:"\u04e8\u043c\u043d\u04e9\u0445",submit:"\u0414\u0443\u0443\u0441\u0433\u0430\u0445"},loginForm:{submitText:"\u041d\u044d\u0432\u0442\u0440\u044d\u0445"},editableTable:{action:{save:"\u0425\u0430\u0434\u0433\u0430\u043b\u0430\u0445",cancel:"\u0426\u0443\u0446\u043b\u0430\u0445",delete:"\u0423\u0441\u0442\u0433\u0430\u0445",add:"\u041c\u04e9\u0440 \u043d\u044d\u043c\u044d\u0445"}},switch:{open:"\u041d\u044d\u044d\u0445",close:"\u0425\u0430\u0430\u0445"}},R={moneySymbol:"RM",form:{lightFilter:{more:"Lebih banyak",clear:"Jelas",confirm:"Mengesahkan",itemUnit:"Item"}},tableForm:{search:"Cari",reset:"Menetapkan semula",submit:"Hantar",collapsed:"Kembang",expand:"Kuncup",inputPlaceholder:"Sila masuk",selectPlaceholder:"Sila pilih"},alert:{clear:"Padam",selected:"Dipilih",item:"Item"},pagination:{total:{range:" ",total:"daripada",item:"item"}},tableToolBar:{leftPin:"Pin ke kiri",rightPin:"Pin ke kanan",noPin:"Tidak pin",leftFixedTitle:"Tetap ke kiri",rightFixedTitle:"Tetap ke kanan",noFixedTitle:"Tidak Tetap",reset:"Menetapkan semula",columnDisplay:"Lajur",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Keluar Full Screen",reload:"Muat Semula",density:"Densiti",densityDefault:"Biasa",densityLarger:"Besar",densityMiddle:"Tengah",densitySmall:"Kecil"},stepsForm:{next:"Seterusnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Log Masuk"},editableTable:{action:{save:"Simpan",cancel:"Membatalkan",delete:"Menghapuskan",add:"tambah baris data"}},switch:{open:"Terbuka",close:"Tutup"}},F={moneySymbol:"z\u0142",form:{lightFilter:{more:"Wi\u0119cej",clear:"Wyczy\u015b\u0107",confirm:"Potwierd\u017a",itemUnit:"Ilo\u015b\u0107"}},tableForm:{search:"Szukaj",reset:"Reset",submit:"Zatwierd\u017a",collapsed:"Poka\u017c wiecej",expand:"Poka\u017c mniej",inputPlaceholder:"Prosz\u0119 poda\u0107",selectPlaceholder:"Prosz\u0119 wybra\u0107"},alert:{clear:"Wyczy\u015b\u0107",selected:"Wybrane",item:"Wpis"},pagination:{total:{range:" ",total:"z",item:"Wpis\xf3w"}},tableToolBar:{leftPin:"Przypnij do lewej",rightPin:"Przypnij do prawej",noPin:"Odepnij",leftFixedTitle:"Przypi\u0119te do lewej",rightFixedTitle:"Przypi\u0119te do prawej",noFixedTitle:"Nieprzypi\u0119te",reset:"Reset",columnDisplay:"Wy\u015bwietlane wiersze",columnSetting:"Ustawienia",fullScreen:"Pe\u0142en ekran",exitFullScreen:"Zamknij pe\u0142en ekran",reload:"Od\u015bwie\u017c",density:"Odst\u0119p",densityDefault:"Standard",densityLarger:"Wiekszy",densityMiddle:"Sredni",densitySmall:"Kompaktowy"},stepsForm:{next:"Weiter",prev:"Zur\xfcck",submit:"Abschlie\xdfen"},loginForm:{submitText:"Zaloguj si\u0119"},editableTable:{action:{save:"Zapisa\u0107",cancel:"Anuluj",delete:"Usun\u0105\u0107",add:"dodawanie wiersza danych"}},switch:{open:"otwiera\u0107",close:"zamyka\u0107"}},D={moneySymbol:"R$",form:{lightFilter:{more:"Mais",clear:"Limpar",confirm:"Confirmar",itemUnit:"Itens"}},tableForm:{search:"Filtrar",reset:"Limpar",submit:"Confirmar",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Por favor insira",selectPlaceholder:"Por favor selecione"},alert:{clear:"Limpar",selected:"Selecionado(s)",item:"Item(s)"},pagination:{total:{range:" ",total:"de",item:"itens"}},tableToolBar:{leftPin:"Fixar \xe0 esquerda",rightPin:"Fixar \xe0 direita",noPin:"Desfixado",leftFixedTitle:"Fixado \xe0 esquerda",rightFixedTitle:"Fixado \xe0 direita",noFixedTitle:"N\xe3o fixado",reset:"Limpar",columnDisplay:"Mostrar Coluna",columnSetting:"Configura\xe7\xf5es",fullScreen:"Tela Cheia",exitFullScreen:"Sair da Tela Cheia",reload:"Atualizar",density:"Densidade",densityDefault:"Padr\xe3o",densityLarger:"Largo",densityMiddle:"M\xe9dio",densitySmall:"Compacto"},stepsForm:{next:"Pr\xf3ximo",prev:"Anterior",submit:"Enviar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Salvar",cancel:"Cancelar",delete:"Apagar",add:"adicionar uma linha de dados"}},switch:{open:"abrir",close:"fechar"}},L={moneySymbol:"\u20bd",form:{lightFilter:{more:"\u0415\u0449\u0435",clear:"\u041e\u0447\u0438\u0441\u0442\u0438\u0442\u044c",confirm:"\u041e\u041a",itemUnit:"\u041f\u043e\u0437\u0438\u0446\u0438\u0438"}},tableForm:{search:"\u041d\u0430\u0439\u0442\u0438",reset:"\u0421\u0431\u0440\u043e\u0441",submit:"\u041e\u0442\u043f\u0440\u0430\u0432\u0438\u0442\u044c",collapsed:"\u0420\u0430\u0437\u0432\u0435\u0440\u043d\u0443\u0442\u044c",expand:"\u0421\u0432\u0435\u0440\u043d\u0443\u0442\u044c",inputPlaceholder:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043d\u0430\u0447\u0435\u043d\u0438\u0435",selectPlaceholder:"\u0412\u044b\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043d\u0430\u0447\u0435\u043d\u0438\u0435"},alert:{clear:"\u041e\u0447\u0438\u0441\u0442\u0438\u0442\u044c",selected:"\u0412\u044b\u0431\u0440\u0430\u043d\u043e",item:"\u044d\u043b\u0435\u043c\u0435\u043d\u0442\u043e\u0432"},pagination:{total:{range:" ",total:"\u0438\u0437",item:"\u044d\u043b\u0435\u043c\u0435\u043d\u0442\u043e\u0432"}},tableToolBar:{leftPin:"\u0417\u0430\u043a\u0440\u0435\u043f\u0438\u0442\u044c \u0441\u043b\u0435\u0432\u0430",rightPin:"\u0417\u0430\u043a\u0440\u0435\u043f\u0438\u0442\u044c \u0441\u043f\u0440\u0430\u0432\u0430",noPin:"\u041e\u0442\u043a\u0440\u0435\u043f\u0438\u0442\u044c",leftFixedTitle:"\u0417\u0430\u043a\u0440\u0435\u043f\u043b\u0435\u043d\u043e \u0441\u043b\u0435\u0432\u0430",rightFixedTitle:"\u0417\u0430\u043a\u0440\u0435\u043f\u043b\u0435\u043d\u043e \u0441\u043f\u0440\u0430\u0432\u0430",noFixedTitle:"\u041d\u0435 \u0437\u0430\u043a\u0440\u0435\u043f\u043b\u0435\u043d\u043e",reset:"\u0421\u0431\u0440\u043e\u0441",columnDisplay:"\u041e\u0442\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435 \u0441\u0442\u043e\u043b\u0431\u0446\u0430",columnSetting:"\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438",fullScreen:"\u041f\u043e\u043b\u043d\u044b\u0439 \u044d\u043a\u0440\u0430\u043d",exitFullScreen:"\u0412\u044b\u0439\u0442\u0438 \u0438\u0437 \u043f\u043e\u043b\u043d\u043e\u044d\u043a\u0440\u0430\u043d\u043d\u043e\u0433\u043e \u0440\u0435\u0436\u0438\u043c\u0430",reload:"\u041e\u0431\u043d\u043e\u0432\u0438\u0442\u044c",density:"\u0420\u0430\u0437\u043c\u0435\u0440",densityDefault:"\u041f\u043e \u0443\u043c\u043e\u043b\u0447\u0430\u043d\u0438\u044e",densityLarger:"\u0411\u043e\u043b\u044c\u0448\u043e\u0439",densityMiddle:"\u0421\u0440\u0435\u0434\u043d\u0438\u0439",densitySmall:"\u0421\u0436\u0430\u0442\u044b\u0439"},stepsForm:{next:"\u0421\u043b\u0435\u0434\u0443\u044e\u0449\u0438\u0439",prev:"\u041f\u0440\u0435\u0434\u044b\u0434\u0443\u0449\u0438\u0439",submit:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044c"},loginForm:{submitText:"\u0412\u0445\u043e\u0434"},editableTable:{action:{save:"\u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c",cancel:"\u041e\u0442\u043c\u0435\u043d\u0438\u0442\u044c",delete:"\u0423\u0434\u0430\u043b\u0438\u0442\u044c",add:"\u0434\u043e\u0431\u0430\u0432\u0438\u0442\u044c \u0440\u044f\u0434 \u0434\u0430\u043d\u043d\u044b\u0445"}},switch:{open:"\u041e\u0442\u043a\u0440\u044b\u0442\u044b\u0439 \u0447\u0435\u043c\u043f\u0438\u043e\u043d\u0430\u0442 \u043c\u0438\u0440\u0430 \u043f\u043e \u0442\u0435\u043d\u043d\u0438\u0441\u0443",close:"\u041f\u043e \u0430\u0434\u0440\u0435\u0441\u0443:"}},I={moneySymbol:"RSD",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Prona\u0111i",reset:"Resetuj",submit:"Po\u0161alji",collapsed:"Pro\u0161iri",expand:"Skupi",inputPlaceholder:"Molimo unesite",selectPlaceholder:"Molimo odaberite"},alert:{clear:"O\u010disti",selected:"Odabrano",item:"Stavka"},pagination:{total:{range:" ",total:"od",item:"stavki"}},tableToolBar:{leftPin:"Zaka\u010di levo",rightPin:"Zaka\u010di desno",noPin:"Nije zaka\u010deno",leftFixedTitle:"Fiksirano levo",rightFixedTitle:"Fiksirano desno",noFixedTitle:"Nije fiksirano",reset:"Resetuj",columnDisplay:"Prikaz kolona",columnSetting:"Pode\u0161avanja",fullScreen:"Pun ekran",exitFullScreen:"Zatvori pun ekran",reload:"Osve\u017ei",density:"Veli\u010dina",densityDefault:"Podrazumevana",densityLarger:"Ve\u0107a",densityMiddle:"Srednja",densitySmall:"Kompaktna"},stepsForm:{next:"Dalje",prev:"Nazad",submit:"Gotovo"},loginForm:{submitText:"Prijavi se"},editableTable:{action:{save:"Sa\u010duvaj",cancel:"Poni\u0161ti",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"\u041e\u0442\u0432\u043e\u0440\u0438\u0442\u0435",close:"\u0417\u0430\u0442\u0432\u043e\u0440\u0438\u0442\u0435"}},A={moneySymbol:"\u20ba",form:{lightFilter:{more:"Daha Fazla",clear:"Temizle",confirm:"Onayla",itemUnit:"\xd6\u011feler"}},tableForm:{search:"Filtrele",reset:"S\u0131f\u0131rla",submit:"G\xf6nder",collapsed:"Daha fazla",expand:"Daha az",inputPlaceholder:"Filtrelemek i\xe7in bir de\u011fer girin",selectPlaceholder:"Filtrelemek i\xe7in bir de\u011fer se\xe7in"},alert:{clear:"Temizle",selected:"Se\xe7ili",item:"\xd6\u011fe"},pagination:{total:{range:" ",total:"Toplam",item:"\xd6\u011fe"}},tableToolBar:{leftPin:"Sola sabitle",rightPin:"Sa\u011fa sabitle",noPin:"Sabitlemeyi kald\u0131r",leftFixedTitle:"Sola sabitlendi",rightFixedTitle:"Sa\u011fa sabitlendi",noFixedTitle:"Sabitlenmedi",reset:"S\u0131f\u0131rla",columnDisplay:"Kolon G\xf6r\xfcn\xfcm\xfc",columnSetting:"Ayarlar",fullScreen:"Tam Ekran",exitFullScreen:"Tam Ekrandan \xc7\u0131k",reload:"Yenile",density:"Kal\u0131nl\u0131k",densityDefault:"Varsay\u0131lan",densityLarger:"B\xfcy\xfck",densityMiddle:"Orta",densitySmall:"K\xfc\xe7\xfck"},stepsForm:{next:"S\u0131radaki",prev:"\xd6nceki",submit:"G\xf6nder"},loginForm:{submitText:"Giri\u015f Yap"},editableTable:{action:{save:"Kaydet",cancel:"Vazge\xe7",delete:"Sil",add:"foegje in rige gegevens ta"}},switch:{open:"a\xe7\u0131k",close:"kapatmak"}},z={moneySymbol:"\u20ab",form:{lightFilter:{more:"Nhi\u1ec1u h\u01a1n",clear:"Trong",confirm:"X\xe1c nh\u1eadn",itemUnit:"M\u1ee5c"}},tableForm:{search:"T\xecm ki\u1ebfm",reset:"L\xe0m l\u1ea1i",submit:"G\u1eedi \u0111i",collapsed:"M\u1edf r\u1ed9ng",expand:"Thu g\u1ecdn",inputPlaceholder:"nh\u1eadp d\u1eef li\u1ec7u",selectPlaceholder:"Vui l\xf2ng ch\u1ecdn"},alert:{clear:"X\xf3a",selected:"\u0111\xe3 ch\u1ecdn",item:"m\u1ee5c"},pagination:{total:{range:" ",total:"tr\xean",item:"m\u1eb7t h\xe0ng"}},tableToolBar:{leftPin:"Ghim tr\xe1i",rightPin:"Ghim ph\u1ea3i",noPin:"B\u1ecf ghim",leftFixedTitle:"C\u1ed1 \u0111\u1ecbnh tr\xe1i",rightFixedTitle:"C\u1ed1 \u0111\u1ecbnh ph\u1ea3i",noFixedTitle:"Ch\u01b0a c\u1ed1 \u0111\u1ecbnh",reset:"L\xe0m l\u1ea1i",columnDisplay:"C\u1ed9t hi\u1ec3n th\u1ecb",columnSetting:"C\u1ea5u h\xecnh",fullScreen:"Ch\u1ebf \u0111\u1ed9 to\xe0n m\xe0n h\xecnh",exitFullScreen:"Tho\xe1t ch\u1ebf \u0111\u1ed9 to\xe0n m\xe0n h\xecnh",reload:"L\xe0m m\u1edbi",density:"M\u1eadt \u0111\u1ed9 hi\u1ec3n th\u1ecb",densityDefault:"M\u1eb7c \u0111\u1ecbnh",densityLarger:"M\u1eb7c \u0111\u1ecbnh",densityMiddle:"Trung b\xecnh",densitySmall:"Ch\u1eadt"},stepsForm:{next:"Sau",prev:"Tr\u01b0\u1edbc",submit:"K\u1ebft th\xfac"},loginForm:{submitText:"\u0110\u0103ng nh\u1eadp"},editableTable:{action:{save:"C\u1ee9u",cancel:"H\u1ee7y",delete:"X\xf3a",add:"th\xeam m\u1ed9t h\xe0ng d\u1eef li\u1ec7u"}},switch:{open:"m\u1edf",close:"\u0111\xf3ng"}},H={moneySymbol:"\uffe5",deleteThisLine:"\u5220\u9664\u6b64\u884c",copyThisLine:"\u590d\u5236\u6b64\u884c",form:{lightFilter:{more:"\u66f4\u591a\u7b5b\u9009",clear:"\u6e05\u9664",confirm:"\u786e\u8ba4",itemUnit:"\u9879"}},tableForm:{search:"\u67e5\u8be2",reset:"\u91cd\u7f6e",submit:"\u63d0\u4ea4",collapsed:"\u5c55\u5f00",expand:"\u6536\u8d77",inputPlaceholder:"\u8bf7\u8f93\u5165",selectPlaceholder:"\u8bf7\u9009\u62e9"},alert:{clear:"\u53d6\u6d88\u9009\u62e9",selected:"\u5df2\u9009\u62e9",item:"\u9879"},pagination:{total:{range:"\u7b2c",total:"\u6761/\u603b\u5171",item:"\u6761"}},tableToolBar:{leftPin:"\u56fa\u5b9a\u5728\u5217\u9996",rightPin:"\u56fa\u5b9a\u5728\u5217\u5c3e",noPin:"\u4e0d\u56fa\u5b9a",leftFixedTitle:"\u56fa\u5b9a\u5728\u5de6\u4fa7",rightFixedTitle:"\u56fa\u5b9a\u5728\u53f3\u4fa7",noFixedTitle:"\u4e0d\u56fa\u5b9a",reset:"\u91cd\u7f6e",columnDisplay:"\u5217\u5c55\u793a",columnSetting:"\u5217\u8bbe\u7f6e",fullScreen:"\u5168\u5c4f",exitFullScreen:"\u9000\u51fa\u5168\u5c4f",reload:"\u5237\u65b0",density:"\u5bc6\u5ea6",densityDefault:"\u6b63\u5e38",densityLarger:"\u9ed8\u8ba4",densityMiddle:"\u4e2d\u7b49",densitySmall:"\u7d27\u51d1"},stepsForm:{next:"\u4e0b\u4e00\u6b65",prev:"\u4e0a\u4e00\u6b65",submit:"\u63d0\u4ea4"},loginForm:{submitText:"\u767b\u5f55"},editableTable:{onlyOneLineEditor:"\u53ea\u80fd\u540c\u65f6\u7f16\u8f91\u4e00\u884c",action:{save:"\u4fdd\u5b58",cancel:"\u53d6\u6d88",delete:"\u5220\u9664",add:"\u6dfb\u52a0\u4e00\u884c\u6570\u636e"}},switch:{open:"\u6253\u5f00",close:"\u5173\u95ed"}},B={moneySymbol:"NT$",form:{lightFilter:{more:"\u66f4\u591a\u7be9\u9078",clear:"\u6e05\u9664",confirm:"\u78ba\u8a8d",itemUnit:"\u9805"}},tableForm:{search:"\u67e5\u8a62",reset:"\u91cd\u7f6e",submit:"\u63d0\u4ea4",collapsed:"\u5c55\u958b",expand:"\u6536\u8d77",inputPlaceholder:"\u8acb\u8f38\u5165",selectPlaceholder:"\u8acb\u9078\u64c7"},alert:{clear:"\u53d6\u6d88\u9078\u64c7",selected:"\u5df2\u9078\u64c7",item:"\u9805"},pagination:{total:{range:"\u7b2c",total:"\u689d/\u7e3d\u5171",item:"\u689d"}},tableToolBar:{leftPin:"\u56fa\u5b9a\u5230\u5de6\u908a",rightPin:"\u56fa\u5b9a\u5230\u53f3\u908a",noPin:"\u4e0d\u56fa\u5b9a",leftFixedTitle:"\u56fa\u5b9a\u5728\u5de6\u5074",rightFixedTitle:"\u56fa\u5b9a\u5728\u53f3\u5074",noFixedTitle:"\u4e0d\u56fa\u5b9a",reset:"\u91cd\u7f6e",columnDisplay:"\u5217\u5c55\u793a",columnSetting:"\u5217\u8a2d\u7f6e",fullScreen:"\u5168\u5c4f",exitFullScreen:"\u9000\u51fa\u5168\u5c4f",reload:"\u5237\u65b0",density:"\u5bc6\u5ea6",densityDefault:"\u6b63\u5e38",densityLarger:"\u9ed8\u8a8d",densityMiddle:"\u4e2d\u7b49",densitySmall:"\u7dca\u6e4a"},stepsForm:{next:"\u4e0b\u4e00\u500b",prev:"\u4ee5\u524d\u7684",submit:"\u5b8c\u6210"},loginForm:{submitText:"\u767b\u5165"},editableTable:{action:{save:"\u4fdd\u5b58",cancel:"\u53d6\u6d88",delete:"\u522a\u9664",add:"\u6dfb\u52a0\u4e00\u884c\u6578\u64da"}},switch:{open:"\u6253\u958b",close:"\u95dc\u9589"}};function K(e,t,n){var r,o=t.replace(/\[(\d+)\]/g,".$1").split("."),a=e,i=n,c=f(o);try{for(c.s();!(r=c.n()).done;){var l=r.value;if(i=Object(a)[l],a=Object(a)[l],void 0===i)return n}}catch(u){c.e(u)}finally{c.f()}return i}var _=function(e,t){return{getMessage:function(e,n){return K(t,e,n)||n},locale:e}},V=_("mn_MN",N),U=_("ar_EG",y),W=_("zh_CN",H),G=_("en_US",x),q=_("en_GB",j),Y=_("vi_VN",z),X=_("it_IT",k),Z=_("ja_JP",T),Q=_("es_ES",w),$=_("ca_ES",g),J=_("ru_RU",L),ee=_("sr_RS",I),te=_("ms_MY",R),ne=_("zh_TW",B),re=_("fr_FR",S),oe=_("pt_BR",D),ae=_("ko_KR",M),ie=_("id_ID",P),ce=_("de_DE",O),le=_("fa_IR",C),ue=_("tr_TR",A),se=_("pl_PL",F),de=_("hr_",E),fe={"mn-MN":V,"ar-EG":U,"zh-CN":W,"en-US":G,"en-GB":q,"vi-VN":Y,"it-IT":X,"ja-JP":Z,"es-ES":Q,"ca-ES":$,"ru-RU":J,"sr-RS":ee,"ms-MY":te,"zh-TW":ne,"fr-FR":re,"pt-BR":oe,"ko-KR":ae,"id-ID":ie,"de-DE":ce,"fa-IR":le,"tr-TR":ue,"pl-PL":se,"hr-HR":de},pe=Object.keys(fe),me=v.a.createContext({intl:u(u({},W),{},{locale:"default"}),valueTypeMap:{}}),he=me.Consumer,ve=me.Provider,be=function(e){if(!e)return"zh-CN";var t=e.toLocaleLowerCase();return pe.find((function(e){var n=e.toLocaleLowerCase();return n.includes(t)}))},ye=function(){var e=Object(b["c"])(),t=e.cache;return Object(h["useEffect"])((function(){return function(){t.clear()}}),[]),null},ge=function(e){var t=e.children,n=e.autoClearCache,o=void 0!==n&&n,a=Object(h["useContext"])(r["a"].ConfigContext),i=a.locale,c=void 0===i?r["a"]:v.a.Fragment,l=Object(p["jsx"])(he,{children:function(e){var n,r=null===i||void 0===i?void 0:i.locale,a=be(r),l=r&&"default"===(null===(n=e.intl)||void 0===n?void 0:n.locale)?fe[a]:e.intl||fe[a],s=void 0===i?{locale:m["default"]}:{};return Object(p["jsx"])(c,u(u({},s),{},{children:Object(p["jsx"])(ve,{value:u(u({},e),{},{intl:l||W}),children:Object(p["jsxs"])(p["Fragment"],{children:[o&&Object(p["jsx"])(ye,{}),t]})})}))}});return o?Object(p["jsx"])(b["a"],{value:{provider:function(){return new Map}},children:l}):l};function Oe(){var e=Object(h["useContext"])(r["a"].ConfigContext),t=e.locale,n=Object(h["useContext"])(me),o=n.intl;return o&&"default"!==o.locale?o:(null===t||void 0===t?void 0:t.locale)?fe[be(t.locale)]:W}t["b"]=me},Lpa7:function(e,t,n){"use strict";n.d(t,"a",(function(){return ue})),n.d(t,"b",(function(){return se})),n.d(t,"c",(function(){return oe}));var r=n("q1tI");function o(e,t,n,r){function o(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,a){function i(e){try{l(r.next(e))}catch(t){a(t)}}function c(e){try{l(r["throw"](e))}catch(t){a(t)}}function l(e){e.done?n(e.value):o(e.value).then(i,c)}l((r=r.apply(e,t||[])).next())}))}function a(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"===typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(e){return function(t){return l([e,t])}}function l(a){if(n)throw new TypeError("Generator is already executing.");while(i)try{if(n=1,r&&(o=2&a[0]?r["return"]:a[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(o=i.trys,!(o=o.length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(c){a=[6,c],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}}var i=function(){},c=i(),l=Object,u=function(e){return e===c},s=function(e){return"function"==typeof e},d=function(e,t){return l.assign({},e,t)},f="undefined",p=function(){return typeof window!=f},m=function(){return typeof document!=f},h=function(){return p()&&typeof window["requestAnimationFrame"]!=f},v=new WeakMap,b=0,y=function e(t){var n,r,o=typeof t,a=t&&t.constructor,i=a==Date;if(l(t)!==t||i||a==RegExp)n=i?t.toJSON():"symbol"==o?t.toString():"string"==o?JSON.stringify(t):""+t;else{if(n=v.get(t),n)return n;if(n=++b+"~",v.set(t,n),a==Array){for(n="@",r=0;r<t.length;r++)n+=e(t[r])+",";v.set(t,n)}if(a==l){n="#";var c=l.keys(t).sort();while(!u(r=c.pop()))u(t[r])||(n+=r+":"+e(t[r])+",");v.set(t,n)}}return n},g=!0,O=function(){return g},j=p(),x=m(),w=j&&window.addEventListener?window.addEventListener.bind(window):i,C=x?document.addEventListener.bind(document):i,S=j&&window.removeEventListener?window.removeEventListener.bind(window):i,E=x?document.removeEventListener.bind(document):i,P=function(){var e=x&&document.visibilityState;return u(e)||"hidden"!==e},k=function(e){return C("visibilitychange",e),w("focus",e),function(){E("visibilitychange",e),S("focus",e)}},T=function(e){var t=function(){g=!0,e()},n=function(){g=!1};return w("online",t),w("offline",n),function(){S("online",t),S("offline",n)}},M={isOnline:O,isVisible:P},N={initFocus:k,initReconnect:T},R=!p()||"Deno"in window,F=function(e){return h()?window["requestAnimationFrame"](e):setTimeout(e,1)},D=R?r["useEffect"]:r["useLayoutEffect"],L="undefined"!==typeof navigator&&navigator.connection,I=!R&&L&&(["slow-2g","2g"].includes(L.effectiveType)||L.saveData),A=function(e){if(s(e))try{e=e()}catch(r){e=""}var t=[].concat(e);e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?y(e):"";var n=e?"$swr$"+e:"";return[e,t,n]},z=new WeakMap,H=0,B=1,K=2,_=function(e,t,n,r,o,a,i){void 0===i&&(i=!0);var c=z.get(e),l=c[0],u=c[1],s=c[3],d=l[t],f=u[t];if(i&&f)for(var p=0;p<f.length;++p)f[p](n,r,o);return a&&(delete s[t],d&&d[0])?d[0](K).then((function(){return e.get(t)})):e.get(t)},V=0,U=function(){return++V},W=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o(void 0,void 0,void 0,(function(){var t,n,r,o,i,l,f,p,m,h,v,b,y,g,O,j,x,w,C,S,E;return a(this,(function(a){switch(a.label){case 0:if(t=e[0],n=e[1],r=e[2],o=e[3],i="boolean"===typeof o?{revalidate:o}:o||{},l=!!u(i.populateCache)||i.populateCache,f=!1!==i.revalidate,p=!1!==i.rollbackOnError,m=i.optimisticData,h=A(n),v=h[0],b=h[2],!v)return[2];if(y=z.get(t),g=y[2],e.length<3)return[2,_(t,v,t.get(v),c,c,f,!0)];if(O=r,x=U(),g[v]=[x,0],w=!u(m),C=t.get(v),w&&(S=s(m)?m(C):m,t.set(v,S),_(t,v,S)),s(O))try{O=O(t.get(v))}catch(P){j=P}return O&&s(O.then)?[4,O.catch((function(e){j=e}))]:[3,2];case 1:if(O=a.sent(),x!==g[v][0]){if(j)throw j;return[2,O]}j&&w&&p&&(l=!0,O=C,t.set(v,C)),a.label=2;case 2:return l&&(j||(s(l)&&(O=l(O,C)),t.set(v,O)),t.set(b,d(t.get(b),{error:j}))),g[v][1]=U(),[4,_(t,v,O,j,c,f,!!l)];case 3:if(E=a.sent(),j)throw j;return[2,l?E:O]}}))}))},G=function(e,t){for(var n in e)e[n][0]&&e[n][0](t)},q=function(e,t){if(!z.has(e)){var n=d(N,t),r={},o=W.bind(c,e),a=i;if(z.set(e,[r,{},{},{},o]),!R){var l=n.initFocus(setTimeout.bind(c,G.bind(c,r,H))),u=n.initReconnect(setTimeout.bind(c,G.bind(c,r,B)));a=function(){l&&l(),u&&u(),z.delete(e)}}return[e,o,a]}return[e,z.get(e)[4]]},Y=function(e,t,n,r,o){var a=n.errorRetryCount,i=o.retryCount,c=~~((Math.random()+.5)*(1<<(i<8?i:8)))*n.errorRetryInterval;!u(a)&&i>a||setTimeout(r,c,o)},X=q(new Map),Z=X[0],Q=X[1],$=d({onLoadingSlow:i,onSuccess:i,onError:i,onErrorRetry:Y,onDiscarded:i,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:I?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:I?5e3:3e3,compare:function(e,t){return y(e)==y(t)},isPaused:function(){return!1},cache:Z,mutate:Q,fallback:{}},M),J=function(e,t){var n=d(e,t);if(t){var r=e.use,o=e.fallback,a=t.use,i=t.fallback;r&&a&&(n.use=r.concat(a)),o&&i&&(n.fallback=d(o,i))}return n},ee=Object(r["createContext"])({}),te=function(e){var t=e.value,n=J(Object(r["useContext"])(ee),t),o=t&&t.provider,a=Object(r["useState"])((function(){return o?q(o(n.cache||Z),t):c}))[0];return a&&(n.cache=a[0],n.mutate=a[1]),D((function(){return a?a[2]:c}),[]),Object(r["createElement"])(ee.Provider,d(e,{value:n}))},ne=function(e,t){var n=Object(r["useState"])({})[1],o=Object(r["useRef"])(e),a=Object(r["useRef"])({data:!1,error:!1,isValidating:!1}),i=Object(r["useCallback"])((function(e){var r=!1,i=o.current;for(var c in e){var l=c;i[l]!==e[l]&&(i[l]=e[l],a.current[l]&&(r=!0))}r&&!t.current&&n({})}),[]);return D((function(){o.current=e})),[o,a.current,i]},re=function(e){return s(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}]},oe=function(){return d($,Object(r["useContext"])(ee))},ae=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=oe(),o=re(t),a=o[0],i=o[1],c=o[2],l=J(r,c),u=e,s=l.use;if(s)for(var d=s.length;d-- >0;)u=s[d](u);return u(a,i||l.fetcher,l)}},ie=function(e,t,n){var r=t[e]||(t[e]=[]);return r.push(n),function(){var e=r.indexOf(n);e>=0&&(r[e]=r[r.length-1],r.pop())}},ce={dedupe:!0},le=function(e,t,n){var i=n.cache,l=n.compare,f=n.fallbackData,p=n.suspense,m=n.revalidateOnMount,h=n.refreshInterval,v=n.refreshWhenHidden,b=n.refreshWhenOffline,y=z.get(i),g=y[0],O=y[1],j=y[2],x=y[3],w=A(e),C=w[0],S=w[1],E=w[2],P=Object(r["useRef"])(!1),k=Object(r["useRef"])(!1),T=Object(r["useRef"])(C),M=Object(r["useRef"])(t),N=Object(r["useRef"])(n),L=function(){return N.current},I=function(){return L().isVisible()&&L().isOnline()},V=function(e){return i.set(E,d(i.get(E),e))},G=i.get(C),q=u(f)?n.fallback[C]:f,Y=u(G)?q:G,X=i.get(E)||{},Z=X.error,Q=!P.current,$=function(){return Q&&!u(m)?m:!L().isPaused()&&(p?!u(Y)&&n.revalidateIfStale:u(Y)||n.revalidateIfStale)},J=function(){return!(!C||!t)&&(!!X.isValidating||Q&&$())},ee=J(),te=ne({data:Y,error:Z,isValidating:ee},k),re=te[0],oe=te[1],ae=te[2],le=Object(r["useCallback"])((function(e){return o(void 0,void 0,void 0,(function(){var t,r,o,d,f,p,m,h,v,b,y,g,O;return a(this,(function(a){switch(a.label){case 0:if(t=M.current,!C||!t||k.current||L().isPaused())return[2,!1];d=!0,f=e||{},p=!x[C]||!f.dedupe,m=function(){return!k.current&&C===T.current&&P.current},h=function(){var e=x[C];e&&e[1]===o&&delete x[C]},v={isValidating:!1},b=function(){V({isValidating:!1}),m()&&ae(v)},V({isValidating:!0}),ae({isValidating:!0}),a.label=1;case 1:return a.trys.push([1,3,,4]),p&&(_(i,C,re.current.data,re.current.error,!0),n.loadingTimeout&&!i.get(C)&&setTimeout((function(){d&&m()&&L().onLoadingSlow(C,n)}),n.loadingTimeout),x[C]=[t.apply(void 0,S),U()]),O=x[C],r=O[0],o=O[1],[4,r];case 2:return r=a.sent(),p&&setTimeout(h,n.dedupingInterval),x[C]&&x[C][1]===o?(V({error:c}),v.error=c,y=j[C],!u(y)&&(o<=y[0]||o<=y[1]||0===y[1])?(b(),p&&m()&&L().onDiscarded(C),[2,!1]):(l(re.current.data,r)?v.data=re.current.data:v.data=r,l(i.get(C),r)||i.set(C,r),p&&m()&&L().onSuccess(r,C,n),[3,4])):(p&&m()&&L().onDiscarded(C),[2,!1]);case 3:return g=a.sent(),h(),L().isPaused()||(V({error:g}),v.error=g,p&&m()&&(L().onError(g,C,n),("boolean"===typeof n.shouldRetryOnError&&n.shouldRetryOnError||s(n.shouldRetryOnError)&&n.shouldRetryOnError(g))&&I()&&L().onErrorRetry(g,C,n,le,{retryCount:(f.retryCount||0)+1,dedupe:!0}))),[3,4];case 4:return d=!1,b(),m()&&p&&_(i,C,v.data,v.error,!1),[2,!0]}}))}))}),[C]),ue=Object(r["useCallback"])(W.bind(c,i,(function(){return T.current})),[]);if(D((function(){M.current=t,N.current=n})),D((function(){if(C){var e=C!==T.current,t=le.bind(c,ce),n=function(e,t,n){ae(d({error:t,isValidating:n},l(re.current.data,e)?c:{data:e}))},r=0,o=function(e){if(e==H){var n=Date.now();L().revalidateOnFocus&&n>r&&I()&&(r=n+L().focusThrottleInterval,t())}else if(e==B)L().revalidateOnReconnect&&I()&&t();else if(e==K)return le()},a=ie(C,O,n),i=ie(C,g,o);return k.current=!1,T.current=C,P.current=!0,e&&ae({data:Y,error:Z,isValidating:ee}),$()&&(u(Y)||R?t():F(t)),function(){k.current=!0,a(),i()}}}),[C,le]),D((function(){var e;function t(){var t=s(h)?h(Y):h;t&&-1!==e&&(e=setTimeout(n,t))}function n(){re.current.error||!v&&!L().isVisible()||!b&&!L().isOnline()?t():le(ce).then(t)}return t(),function(){e&&(clearTimeout(e),e=-1)}}),[h,v,b,le]),Object(r["useDebugValue"])(Y),p&&u(Y)&&C)throw M.current=t,N.current=n,k.current=!1,u(Z)?le(ce):Z;return{mutate:ue,get data(){return oe.data=!0,Y},get error(){return oe.error=!0,Z},get isValidating(){return oe.isValidating=!0,ee}}},ue=l.defineProperty(te,"default",{value:$}),se=ae(le)},PjWh:function(e,t,n){"use strict";var r=function(e){var t={};if(Object.keys(e||{}).forEach((function(n){void 0!==e[n]&&(t[n]=e[n])})),!(Object.keys(t).length<1))return t};t["a"]=r},Q9mQ:function(e,t,n){"use strict";n("EFp3"),n("UADf")},Re9Q:function(e,t,n){},"SE9/":function(e,t,n){"use strict";var r=n("q1tI");function o(e,t){return u(e)||l(e,t)||i(e,t)||a()}function a(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(e,t){if(e){if("string"===typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function l(e,t){var n=e&&("undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=n){var r,o,a=[],i=!0,c=!1;try{for(n=n.call(e);!(i=(r=n.next()).done);i=!0)if(a.push(r.value),t&&a.length===t)break}catch(l){c=!0,o=l}finally{try{i||null==n["return"]||n["return"]()}finally{if(c)throw o}}return a}}function u(e){if(Array.isArray(e))return e}function s(e){var t="undefined"===typeof window,n=Object(r["useState"])((function(){return!t&&window.matchMedia(e).matches})),a=o(n,2),i=a[0],c=a[1];return Object(r["useLayoutEffect"])((function(){if(!t){var n=window.matchMedia(e),r=function(e){return c(e.matches)};return n.addListener(r),function(){return n.removeListener(r)}}}),[e]),i}function d(e,t){return v(e)||h(e,t)||p(e,t)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){if(e){if("string"===typeof e)return m(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function h(e,t){var n=e&&("undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=n){var r,o,a=[],i=!0,c=!1;try{for(n=n.call(e);!(i=(r=n.next()).done);i=!0)if(a.push(r.value),t&&a.length===t)break}catch(l){c=!0,o=l}finally{try{i||null==n["return"]||n["return"]()}finally{if(c)throw o}}return a}}function v(e){if(Array.isArray(e))return e}var b={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},y=function(){var e="md";if("undefined"===typeof window)return e;var t=Object.keys(b).find((function(e){var t=b[e].matchMedia;return!!window.matchMedia(t).matches}));return e=t,e},g=function(){var e=s(b.md.matchMedia),t=s(b.lg.matchMedia),n=s(b.xxl.matchMedia),o=s(b.xl.matchMedia),a=s(b.sm.matchMedia),i=s(b.xs.matchMedia),c=Object(r["useState"])(y()),l=d(c,2),u=l[0],f=l[1];return Object(r["useEffect"])((function(){f(n?"xxl":o?"xl":t?"lg":e?"md":a?"sm":i?"xs":"md")}),[e,t,n,o,a,i]),u};t["a"]=g},Tckk:function(e,t,n){"use strict";var r=n("jiTG"),o=n("+y50"),a=n("rTrx"),i=n("Z97s"),c=n("TSYQ"),l=n.n(c),u=n("t23M"),s=n("c+Xe"),d=n("q1tI"),f=n("H84U"),p=n("5OYt"),m=n("ACnJ"),h=d["createContext"]("default"),v=function(e){var t=e.children,n=e.size;return d["createElement"](h.Consumer,null,(function(e){return d["createElement"](h.Provider,{value:n||e},t)}))},b=h,y=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},g=function(e,t){var n,c,h=d["useContext"](b),v=d["useState"](1),g=Object(i["a"])(v,2),O=g[0],j=g[1],x=d["useState"](!1),w=Object(i["a"])(x,2),C=w[0],S=w[1],E=d["useState"](!0),P=Object(i["a"])(E,2),k=P[0],T=P[1],M=d["useRef"](null),N=d["useRef"](null),R=Object(s["a"])(t,M),F=d["useContext"](f["b"]),D=F.getPrefixCls,L=function(){if(N.current&&M.current){var t=N.current.offsetWidth,n=M.current.offsetWidth;if(0!==t&&0!==n){var r=e.gap,o=void 0===r?4:r;2*o<n&&j(n-2*o<t?(n-2*o)/t:1)}}};d["useEffect"]((function(){S(!0)}),[]),d["useEffect"]((function(){T(!0),j(1)}),[e.src]),d["useEffect"]((function(){L()}),[e.gap]);var I,A=function(){var t=e.onError,n=t?t():void 0;!1!==n&&T(!1)},z=e.prefixCls,H=e.shape,B=void 0===H?"circle":H,K=e.size,_=void 0===K?"default":K,V=e.src,U=e.srcSet,W=e.icon,G=e.className,q=e.alt,Y=e.draggable,X=e.children,Z=e.crossOrigin,Q=y(e,["prefixCls","shape","size","src","srcSet","icon","className","alt","draggable","children","crossOrigin"]),$="default"===_?h:_,J=Object.keys("object"===Object(a["a"])($)&&$||{}).some((function(e){return["xs","sm","md","lg","xl","xxl"].includes(e)})),ee=Object(p["a"])(J),te=d["useMemo"]((function(){if("object"!==Object(a["a"])($))return{};var e=m["b"].find((function(e){return ee[e]})),t=$[e];return t?{width:t,height:t,lineHeight:"".concat(t,"px"),fontSize:W?t/2:18}:{}}),[ee,$]),ne=D("avatar",z),re=l()((n={},Object(o["a"])(n,"".concat(ne,"-lg"),"large"===$),Object(o["a"])(n,"".concat(ne,"-sm"),"small"===$),n)),oe=d["isValidElement"](V),ae=l()(ne,re,(c={},Object(o["a"])(c,"".concat(ne,"-").concat(B),!!B),Object(o["a"])(c,"".concat(ne,"-image"),oe||V&&k),Object(o["a"])(c,"".concat(ne,"-icon"),!!W),c),G),ie="number"===typeof $?{width:$,height:$,lineHeight:"".concat($,"px"),fontSize:W?$/2:18}:{};if("string"===typeof V&&k)I=d["createElement"]("img",{src:V,draggable:Y,srcSet:U,onError:A,alt:q,crossOrigin:Z});else if(oe)I=V;else if(W)I=W;else if(C||1!==O){var ce="scale(".concat(O,") translateX(-50%)"),le={msTransform:ce,WebkitTransform:ce,transform:ce},ue="number"===typeof $?{lineHeight:"".concat($,"px")}:{};I=d["createElement"](u["a"],{onResize:L},d["createElement"]("span",{className:"".concat(ne,"-string"),ref:N,style:Object(r["a"])(Object(r["a"])({},ue),le)},X))}else I=d["createElement"]("span",{className:"".concat(ne,"-string"),style:{opacity:0},ref:N},X);return delete Q.onError,delete Q.gap,d["createElement"]("span",Object(r["a"])({},Q,{style:Object(r["a"])(Object(r["a"])(Object(r["a"])({},ie),te),Q.style),className:ae,ref:R}),I)},O=d["forwardRef"](g);var j=O,x=n("Zm9Q"),w=n("diRs"),C=n("0n0R"),S=function(e){var t=d["useContext"](f["b"]),n=t.getPrefixCls,r=t.direction,a=e.prefixCls,i=e.className,c=void 0===i?"":i,u=e.maxCount,s=e.maxStyle,p=e.size,m=n("avatar-group",a),h=l()(m,Object(o["a"])({},"".concat(m,"-rtl"),"rtl"===r),c),b=e.children,y=e.maxPopoverPlacement,g=void 0===y?"top":y,O=e.maxPopoverTrigger,S=void 0===O?"hover":O,E=Object(x["a"])(b).map((function(e,t){return Object(C["a"])(e,{key:"avatar-key-".concat(t)})})),P=E.length;if(u&&u<P){var k=E.slice(0,u),T=E.slice(u,P);return k.push(d["createElement"](w["a"],{key:"avatar-popover-key",content:T,trigger:S,placement:g,overlayClassName:"".concat(m,"-popover")},d["createElement"](j,{style:s},"+".concat(P-u)))),d["createElement"](v,{size:p},d["createElement"]("div",{className:h,style:e.style},k))}return d["createElement"](v,{size:p},d["createElement"]("div",{className:h,style:e.style},E))},E=S,P=j;P.Group=E;t["a"]=P},Telt:function(e,t,n){"use strict";n("EFp3"),n("ifDB"),n("Q9mQ")},UADf:function(e,t,n){},UPUD:function(e,t,n){},VNzZ:function(e,t,n){"use strict";var r=n("jiTG"),o=n("+y50"),a=n("cdhz"),i=n("u13E"),c=n("Ek/p"),l=n("UvXy"),u=n("rTrx"),s=n("TSYQ"),d=n.n(s),f=n("t23M"),p=n("bT9E"),m=n("q1tI"),h=n("H84U"),v=n("xGeg"),b=n("wgJM");function y(e){var t,n=function(n){return function(){t=null,e.apply(void 0,Object(v["a"])(n))}},r=function(){if(null==t){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];t=Object(b["a"])(n(r))}};return r.cancel=function(){b["a"].cancel(t),t=null},r}function g(){return function(e,t,n){var r=n.value,o=!1;return{configurable:!0,get:function(){if(o||this===e.prototype||this.hasOwnProperty(t))return r;var n=y(r.bind(this));return o=!0,Object.defineProperty(this,t,{value:n,configurable:!0,writable:!0}),o=!1,n}}}}var O=n("zT1h");function j(e){return e!==window?e.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function x(e,t,n){if(void 0!==n&&t.top>e.top-n)return n+t.top}function w(e,t,n){if(void 0!==n&&t.bottom<e.bottom+n){var r=window.innerHeight-t.bottom;return n+r}}var C=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"],S=[];function E(e,t){if(e){var n=S.find((function(t){return t.target===e}));n?n.affixList.push(t):(n={target:e,affixList:[t],eventHandlers:{}},S.push(n),C.forEach((function(t){n.eventHandlers[t]=Object(O["a"])(e,t,(function(){n.affixList.forEach((function(e){e.lazyUpdatePosition()}))}))})))}}function P(e){var t=S.find((function(t){var n=t.affixList.some((function(t){return t===e}));return n&&(t.affixList=t.affixList.filter((function(t){return t!==e}))),n}));t&&0===t.affixList.length&&(S=S.filter((function(e){return e!==t})),C.forEach((function(e){var n=t.eventHandlers[e];n&&n.remove&&n.remove()})))}var k,T=function(e,t,n,r){var o,a=arguments.length,i=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"===("undefined"===typeof Reflect?"undefined":Object(u["a"])(Reflect))&&"function"===typeof Reflect.decorate)i=Reflect.decorate(e,t,n,r);else for(var c=e.length-1;c>=0;c--)(o=e[c])&&(i=(a<3?o(i):a>3?o(t,n,i):o(t,n))||i);return a>3&&i&&Object.defineProperty(t,n,i),i};function M(){return"undefined"!==typeof window?window:null}(function(e){e[e["None"]=0]="None",e[e["Prepare"]=1]="Prepare"})(k||(k={}));var N=function(e){Object(c["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;return Object(a["a"])(this,n),e=t.apply(this,arguments),e.state={status:k.None,lastAffix:!1,prevTarget:null},e.getOffsetTop=function(){var t=e.props,n=t.offsetBottom,r=t.offsetTop;return void 0===n&&void 0===r?0:r},e.getOffsetBottom=function(){return e.props.offsetBottom},e.savePlaceholderNode=function(t){e.placeholderNode=t},e.saveFixedNode=function(t){e.fixedNode=t},e.measure=function(){var t=e.state,n=t.status,r=t.lastAffix,o=e.props.onChange,a=e.getTargetFunc();if(n===k.Prepare&&e.fixedNode&&e.placeholderNode&&a){var i=e.getOffsetTop(),c=e.getOffsetBottom(),l=a();if(l){var u={status:k.None},s=j(l),d=j(e.placeholderNode),f=x(d,s,i),p=w(d,s,c);0===d.top&&0===d.left&&0===d.width&&0===d.height||(void 0!==f?(u.affixStyle={position:"fixed",top:f,width:d.width,height:d.height},u.placeholderStyle={width:d.width,height:d.height}):void 0!==p&&(u.affixStyle={position:"fixed",bottom:p,width:d.width,height:d.height},u.placeholderStyle={width:d.width,height:d.height}),u.lastAffix=!!u.affixStyle,o&&r!==u.lastAffix&&o(u.lastAffix),e.setState(u))}}},e.prepareMeasure=function(){e.setState({status:k.Prepare,affixStyle:void 0,placeholderStyle:void 0})},e}return Object(i["a"])(n,[{key:"getTargetFunc",value:function(){var e=this.context.getTargetContainer,t=this.props.target;return void 0!==t?t:null!==e&&void 0!==e?e:M}},{key:"componentDidMount",value:function(){var e=this,t=this.getTargetFunc();t&&(this.timeout=setTimeout((function(){E(t(),e),e.updatePosition()})))}},{key:"componentDidUpdate",value:function(e){var t=this.state.prevTarget,n=this.getTargetFunc(),r=(null===n||void 0===n?void 0:n())||null;t!==r&&(P(this),r&&(E(r,this),this.updatePosition()),this.setState({prevTarget:r})),e.offsetTop===this.props.offsetTop&&e.offsetBottom===this.props.offsetBottom||this.updatePosition(),this.measure()}},{key:"componentWillUnmount",value:function(){clearTimeout(this.timeout),P(this),this.updatePosition.cancel(),this.lazyUpdatePosition.cancel()}},{key:"updatePosition",value:function(){this.prepareMeasure()}},{key:"lazyUpdatePosition",value:function(){var e=this.getTargetFunc(),t=this.state.affixStyle;if(e&&t){var n=this.getOffsetTop(),r=this.getOffsetBottom(),o=e();if(o&&this.placeholderNode){var a=j(o),i=j(this.placeholderNode),c=x(i,a,n),l=w(i,a,r);if(void 0!==c&&t.top===c||void 0!==l&&t.bottom===l)return}}this.prepareMeasure()}},{key:"render",value:function(){var e=this,t=this.state,n=t.affixStyle,a=t.placeholderStyle,i=this.props,c=i.affixPrefixCls,l=i.children,u=d()(Object(o["a"])({},c,!!n)),s=Object(p["a"])(this.props,["prefixCls","offsetTop","offsetBottom","target","onChange","affixPrefixCls"]);return m["createElement"](f["a"],{onResize:function(){e.updatePosition()}},m["createElement"]("div",Object(r["a"])({},s,{ref:this.savePlaceholderNode}),n&&m["createElement"]("div",{style:a,"aria-hidden":"true"}),m["createElement"]("div",{className:u,ref:this.saveFixedNode,style:n},m["createElement"](f["a"],{onResize:function(){e.updatePosition()}},l))))}}]),n}(m["Component"]);N.contextType=h["b"],T([g()],N.prototype,"updatePosition",null),T([g()],N.prototype,"lazyUpdatePosition",null);var R=m["forwardRef"]((function(e,t){var n=e.prefixCls,o=m["useContext"](h["b"]),a=o.getPrefixCls,i=a("affix",n),c=Object(r["a"])(Object(r["a"])({},e),{affixPrefixCls:i});return m["createElement"](N,Object(r["a"])({},c,{ref:t}))}));t["a"]=R},W3vr:function(e,t,n){},YPDd:function(e,t,n){},"YV/h":function(e,t,n){},ZTPi:function(e,t,n){"use strict";var r=n("+y50"),o=n("jiTG"),a=n("064x"),i=n("oN2U"),c=n("2s+V"),l=n("q1tI"),u={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},s=u,d=n("/1Lp"),f=function(e,t){return l["createElement"](d["a"],Object(c["a"])(Object(c["a"])({},e),{},{ref:t,icon:s}))};f.displayName="PlusOutlined";var p=l["forwardRef"](f),m=n("TSYQ"),h=n.n(m);function v(){return v=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},v.apply(this,arguments)}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function O(e){if(Array.isArray(e))return e}function j(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,a=void 0;try{for(var i,c=e[Symbol.iterator]();!(r=(i=c.next()).done);r=!0)if(n.push(i.value),t&&n.length===t)break}catch(l){o=!0,a=l}finally{try{r||null==c["return"]||c["return"]()}finally{if(o)throw a}}return n}}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function w(e,t){if(e){if("string"===typeof e)return x(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?x(e,t):void 0}}function C(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function S(e,t){return O(e)||j(e,t)||w(e,t)||C()}function E(e){return E="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E(e)}function P(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}function k(e,t){if(null==e)return{};var n,r,o=P(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var T=n("5Z9U"),M=n("6cGi"),N=n("8XRh"),R=Object(l["createContext"])(null),F=l["forwardRef"]((function(e,t){var n=e.prefixCls,r=e.className,o=e.style,a=e.id,i=e.active,c=e.tabKey,u=e.children;return l["createElement"]("div",{id:a&&"".concat(a,"-panel-").concat(c),role:"tabpanel",tabIndex:i?0:-1,"aria-labelledby":a&&"".concat(a,"-tab-").concat(c),"aria-hidden":!i,style:o,className:h()(n,i&&"".concat(n,"-active"),r),ref:t},u)}));var D=F,L=["key","forceRender","style","className"];function I(e){var t=e.id,n=e.activeKey,r=e.animated,o=e.tabPosition,a=e.destroyInactiveTabPane,i=l["useContext"](R),c=i.prefixCls,u=i.tabs,s=r.tabPane,d="".concat(c,"-tabpane");return l["createElement"]("div",{className:h()("".concat(c,"-content-holder"))},l["createElement"]("div",{className:h()("".concat(c,"-content"),"".concat(c,"-content-").concat(o),b({},"".concat(c,"-content-animated"),s))},u.map((function(e){var o=e.key,i=e.forceRender,c=e.style,u=e.className,f=k(e,L),p=o===n;return l["createElement"](N["b"],v({key:o,visible:p,forceRender:i,removeOnLeave:!!a,leavedClassName:"".concat(d,"-hidden")},r.tabPaneMotion),(function(e,n){var r=e.style,a=e.className;return l["createElement"](D,v({},f,{prefixCls:d,id:t,tabKey:o,animated:s,active:p,style:g(g({},c),r),className:h()(u,a),ref:n}))}))}))))}function A(e){if(Array.isArray(e))return x(e)}function z(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function H(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function B(e){return A(e)||z(e)||w(e)||H()}var K=n("t23M"),_=n("mBDr"),V=n("wgJM"),U=n("c+Xe"),W={width:0,height:0,left:0,top:0};function G(e,t,n){return Object(l["useMemo"])((function(){for(var n,r=new Map,o=t.get(null===(n=e[0])||void 0===n?void 0:n.key)||W,a=o.left+o.width,i=0;i<e.length;i+=1){var c,l=e[i].key,u=t.get(l);if(!u)u=t.get(null===(c=e[i-1])||void 0===c?void 0:c.key)||W;var s=r.get(l)||g({},u);s.right=a-s.left-s.width,r.set(l,s)}return r}),[e.map((function(e){return e.key})).join("_"),t,n])}function q(e,t){var n=l["useRef"](e),r=l["useState"]({}),o=S(r,2),a=o[1];function i(e){var r="function"===typeof e?e(n.current):e;r!==n.current&&t(r,n.current),n.current=r,a({})}return[n.current,i]}var Y=.1,X=.01,Z=20,Q=Math.pow(.995,Z);function $(e,t){var n=Object(l["useState"])(),r=S(n,2),o=r[0],a=r[1],i=Object(l["useState"])(0),c=S(i,2),u=c[0],s=c[1],d=Object(l["useState"])(0),f=S(d,2),p=f[0],m=f[1],h=Object(l["useState"])(),v=S(h,2),b=v[0],y=v[1],g=Object(l["useRef"])();function O(e){var t=e.touches[0],n=t.screenX,r=t.screenY;a({x:n,y:r}),window.clearInterval(g.current)}function j(e){if(o){e.preventDefault();var n=e.touches[0],r=n.screenX,i=n.screenY;a({x:r,y:i});var c=r-o.x,l=i-o.y;t(c,l);var d=Date.now();s(d),m(d-u),y({x:c,y:l})}}function x(){if(o&&(a(null),y(null),b)){var e=b.x/p,n=b.y/p,r=Math.abs(e),i=Math.abs(n);if(Math.max(r,i)<Y)return;var c=e,l=n;g.current=window.setInterval((function(){Math.abs(c)<X&&Math.abs(l)<X?window.clearInterval(g.current):(c*=Q,l*=Q,t(c*Z,l*Z))}),Z)}}var w=Object(l["useRef"])();function C(e){var n=e.deltaX,r=e.deltaY,o=0,a=Math.abs(n),i=Math.abs(r);a===i?o="x"===w.current?n:r:a>i?(o=n,w.current="x"):(o=r,w.current="y"),t(-o,-o)&&e.preventDefault()}var E=Object(l["useRef"])(null);E.current={onTouchStart:O,onTouchMove:j,onTouchEnd:x,onWheel:C},l["useEffect"]((function(){function t(e){E.current.onTouchStart(e)}function n(e){E.current.onTouchMove(e)}function r(e){E.current.onTouchEnd(e)}function o(e){E.current.onWheel(e)}return document.addEventListener("touchmove",n,{passive:!1}),document.addEventListener("touchend",r,{passive:!1}),e.current.addEventListener("touchstart",t,{passive:!1}),e.current.addEventListener("wheel",o),function(){document.removeEventListener("touchmove",n),document.removeEventListener("touchend",r)}}),[])}var J=n("TNol");function ee(e){var t=Object(l["useState"])(0),n=S(t,2),r=n[0],o=n[1],a=Object(l["useRef"])(0),i=Object(l["useRef"])();return i.current=e,Object(J["b"])((function(){var e;null===(e=i.current)||void 0===e||e.call(i)}),[r]),function(){a.current===r&&(a.current+=1,o(a.current))}}function te(e){var t=Object(l["useRef"])([]),n=Object(l["useState"])({}),r=S(n,2),o=r[1],a=Object(l["useRef"])("function"===typeof e?e():e),i=ee((function(){var e=a.current;t.current.forEach((function(t){e=t(e)})),t.current=[],a.current=e,o({})}));function c(e){t.current.push(e),i()}return[a.current,c]}var ne={width:0,height:0,left:0,top:0,right:0};function re(e,t,n,r,o,a,i){var c,u,s,d=i.tabs,f=i.tabPosition,p=i.rtl;return["top","bottom"].includes(f)?(c="width",u=p?"right":"left",s=Math.abs(n)):(c="height",u="top",s=-n),Object(l["useMemo"])((function(){if(!d.length)return[0,0];for(var n=d.length,r=n,o=0;o<n;o+=1){var a=e.get(d[o].key)||ne;if(a[u]+a[c]>s+t){r=o-1;break}}for(var i=0,l=n-1;l>=0;l-=1){var f=e.get(d[l].key)||ne;if(f[u]<s){i=l+1;break}}return[i,r]}),[e,t,r,o,a,s,f,d.map((function(e){return e.key})).join("_"),p])}function oe(e){var t;return e instanceof Map?(t={},e.forEach((function(e,n){t[n]=e}))):t=e,JSON.stringify(t)}var ae="TABS_DQ";function ie(e){return String(e).replace(/"/g,ae)}function ce(e,t){var n=e.prefixCls,r=e.editable,o=e.locale,a=e.style;return r&&!1!==r.showAdd?l["createElement"]("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:a,"aria-label":(null===o||void 0===o?void 0:o.addAriaLabel)||"Add tab",onClick:function(e){r.onEdit("add",{event:e})}},r.addIcon||"+"):null}var le=l["forwardRef"](ce),ue=l["forwardRef"]((function(e,t){var n,r=e.position,o=e.prefixCls,a=e.extra;if(!a)return null;var i={};return"object"!==E(a)||l["isValidElement"](a)?i.right=a:i=a,"right"===r&&(n=i.right),"left"===r&&(n=i.left),n?l["createElement"]("div",{className:"".concat(o,"-extra-content"),ref:t},n):null}));var se=ue;function de(e){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(e)}function fe(e,t){if("object"!==de(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==de(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function pe(e){var t=fe(e,"string");return"symbol"===de(t)?t:String(t)}function me(e,t,n){return t=pe(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function he(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ve(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?he(Object(n),!0).forEach((function(t){me(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):he(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function be(e){if(Array.isArray(e))return e}function ye(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=n["return"]&&(i=n["return"](),Object(i)!==i))return}finally{if(u)throw o}}return c}}function ge(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Oe(e,t){if(e){if("string"===typeof e)return ge(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ge(e,t):void 0}}function je(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function xe(e,t){return be(e)||ye(e,t)||Oe(e,t)||je()}function we(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}function Ce(e,t){if(null==e)return{};var n,r,o=we(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function Se(e){return Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Se(e)}function Ee(e,t){if("object"!==Se(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==Se(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function Pe(e){var t=Ee(e,"string");return"symbol"===Se(t)?t:String(t)}function ke(e,t,n){return t=Pe(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Me(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Te(Object(n),!0).forEach((function(t){ke(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ne(){return Ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ne.apply(this,arguments)}function Re(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Fe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Pe(r.key),r)}}function De(e,t,n){return t&&Fe(e.prototype,t),n&&Fe(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Le(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ie(e,t){return Ie=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ie(e,t)}function Ae(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ie(e,t)}function ze(e){return ze=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ze(e)}function He(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function Be(e,t){if(t&&("object"===Se(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Le(e)}function Ke(e){var t=He();return function(){var n,r=ze(e);if(t){var o=ze(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return Be(this,n)}}var _e=n("i8i4"),Ve=n.n(_e),Ue=n("l4aY"),We=n("m+aA"),Ge=n("zT1h"),qe=n("QC+M");function Ye(e,t,n){return n?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function Xe(e,t,n){var r=e[t]||{};return Me(Me({},r),n)}function Ze(e,t,n,r){for(var o=n.points,a=Object.keys(e),i=0;i<a.length;i+=1){var c=a[i];if(Ye(e[c].points,o,r))return"".concat(t,"-placement-").concat(c)}return""}function Qe(e){if(Array.isArray(e))return e}function $e(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=n["return"]&&(i=n["return"](),Object(i)!==i))return}finally{if(u)throw o}}return c}}function Je(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function et(e,t){if(e){if("string"===typeof e)return Je(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Je(e,t):void 0}}function tt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function nt(e,t){return Qe(e)||$e(e,t)||et(e,t)||tt()}function rt(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}function ot(e,t){if(null==e)return{};var n,r,o=rt(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function at(e){var t=e.prefixCls,n=e.motion,r=e.animation,o=e.transitionName;return n||(r?{motionName:"".concat(t,"-").concat(r)}:o?{motionName:o}:null)}function it(e){var t=e.prefixCls,n=e.visible,r=e.zIndex,o=e.mask,a=e.maskMotion,i=e.maskAnimation,c=e.maskTransitionName;if(!o)return null;var u={};return(a||c||i)&&(u=Me({motionAppear:!0},at({motion:a,prefixCls:t,transitionName:c,animation:i}))),l["createElement"](N["b"],Ne({},u,{visible:n,removeOnLeave:!0}),(function(e){var n=e.className;return l["createElement"]("div",{style:{zIndex:r},className:h()("".concat(t,"-mask"),n)})}))}var ct=n("9mu1");function lt(){lt=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,i=Object.create(a.prototype),c=new M(r||[]);return o(i,"_invoke",{value:E(e,n,c)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var f="suspendedStart",p="suspendedYield",m="executing",h="completed",v={};function b(){}function y(){}function g(){}var O={};u(O,i,(function(){return this}));var j=Object.getPrototypeOf,x=j&&j(j(N([])));x&&x!==n&&r.call(x,i)&&(O=x);var w=g.prototype=b.prototype=Object.create(O);function C(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function n(o,a,i,c){var l=d(e[o],e,a);if("throw"!==l.type){var u=l.arg,s=u.value;return s&&"object"==Se(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,i,c)}),(function(e){n("throw",e,i,c)})):t.resolve(s).then((function(e){u.value=e,i(u)}),(function(e){return n("throw",e,i,c)}))}c(l.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function E(t,n,r){var o=f;return function(a,i){if(o===m)throw new Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var l=P(c,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=h,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=m;var u=d(t,n,r);if("normal"===u.type){if(o=r.done?h:p,u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=h,r.method="throw",r.arg=u.arg)}}}function P(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator["return"]&&(n.method="return",n.arg=e,P(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=d(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,v;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function N(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(Se(t)+" is not iterable")}return y.prototype=g,o(w,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:y,configurable:!0}),y.displayName=u(g,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,u(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},t.awrap=function(e){return{__await:e}},C(S.prototype),u(S.prototype,c,(function(){return this})),t.AsyncIterator=S,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new S(s(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},C(w),u(w,l,"Generator"),u(w,i,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=N,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;T(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:N(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function ut(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(u){return void n(u)}c.done?t(l):Promise.resolve(l).then(r,o)}function st(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){ut(a,r,o,i,c,"next",e)}function c(e){ut(a,r,o,i,c,"throw",e)}i(void 0)}))}}var dt=n("dm2S"),ft=["measure","alignPre","align",null,"motion"],pt=function(e,t){var n=Object(dt["a"])(null),r=nt(n,2),o=r[0],a=r[1],i=Object(l["useRef"])();function c(e){a(e,!0)}function u(){V["a"].cancel(i.current)}function s(e){u(),i.current=Object(V["a"])((function(){c((function(e){switch(o){case"align":return"motion";case"motion":return"stable";default:}return e})),null===e||void 0===e||e()}))}return Object(l["useEffect"])((function(){c("measure")}),[e]),Object(l["useEffect"])((function(){switch(o){case"measure":t();break;default:}o&&(i.current=Object(V["a"])(st(lt().mark((function e(){var t,n;return lt().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=ft.indexOf(o),n=ft[t+1],n&&-1!==t&&c(n);case 3:case"end":return e.stop()}}),e)})))))}),[o]),Object(l["useEffect"])((function(){return function(){u()}}),[]),[o,s]},mt=function(e){var t=l["useState"]({width:0,height:0}),n=nt(t,2),r=n[0],o=n[1];function a(e){var t=e.offsetWidth,n=e.offsetHeight,r=e.getBoundingClientRect(),a=r.width,i=r.height;Math.abs(t-a)<1&&Math.abs(n-i)<1&&(t=a,n=i),o({width:t,height:n})}var i=l["useMemo"]((function(){var t={};if(e){var n=r.width,o=r.height;-1!==e.indexOf("height")&&o?t.height=o:-1!==e.indexOf("minHeight")&&o&&(t.minHeight=o),-1!==e.indexOf("width")&&n?t.width=n:-1!==e.indexOf("minWidth")&&n&&(t.minWidth=n)}return t}),[e,r]);return[i,a]},ht=l["forwardRef"]((function(e,t){var n=e.visible,r=e.prefixCls,o=e.className,a=e.style,i=e.children,c=e.zIndex,u=e.stretch,s=e.destroyPopupOnHide,d=e.forceRender,f=e.align,p=e.point,m=e.getRootDomNode,v=e.getClassNameFromAlign,b=e.onAlign,y=e.onMouseEnter,g=e.onMouseLeave,O=e.onMouseDown,j=e.onTouchStart,x=e.onClick,w=Object(l["useRef"])(),C=Object(l["useRef"])(),S=Object(l["useState"])(),E=nt(S,2),P=E[0],k=E[1],T=mt(u),M=nt(T,2),R=M[0],F=M[1];function D(){u&&F(m())}var L=pt(n,D),I=nt(L,2),A=I[0],z=I[1],H=Object(l["useState"])(0),B=nt(H,2),K=B[0],_=B[1],V=Object(l["useRef"])();function U(){return p||m}function W(){var e;null===(e=w.current)||void 0===e||e.forceAlign()}function G(e,t){var n=v(t);P!==n&&k(n),_((function(e){return e+1})),"align"===A&&(null===b||void 0===b||b(e,t))}Object(J["a"])((function(){"alignPre"===A&&_(0)}),[A]),Object(J["a"])((function(){"align"===A&&(K<3?W():z((function(){var e;null===(e=V.current)||void 0===e||e.call(V)})))}),[K]);var q=Me({},at(e));function Y(){return new Promise((function(e){V.current=e}))}["onAppearEnd","onEnterEnd","onLeaveEnd"].forEach((function(e){var t=q[e];q[e]=function(e,n){return z(),null===t||void 0===t?void 0:t(e,n)}})),l["useEffect"]((function(){q.motionName||"motion"!==A||z()}),[q.motionName,A]),l["useImperativeHandle"](t,(function(){return{forceAlign:W,getElement:function(){return C.current}}}));var X=Me(Me({},R),{},{zIndex:c,opacity:"motion"!==A&&"stable"!==A&&n?0:void 0,pointerEvents:n||"stable"===A?void 0:"none"},a),Z=!0;null===f||void 0===f||!f.points||"align"!==A&&"stable"!==A||(Z=!1);var Q=i;return l["Children"].count(i)>1&&(Q=l["createElement"]("div",{className:"".concat(r,"-content")},i)),l["createElement"](N["b"],Ne({visible:n,ref:C,leavedClassName:"".concat(r,"-hidden")},q,{onAppearPrepare:Y,onEnterPrepare:Y,removeOnLeave:s,forceRender:d}),(function(e,t){var n=e.className,a=e.style,i=h()(r,o,P,n);return l["createElement"](ct["a"],{target:U(),key:"popup",ref:w,monitorWindowResize:!0,disabled:Z,align:f,onAlign:G},l["createElement"]("div",{ref:t,className:i,onMouseEnter:y,onMouseLeave:g,onMouseDownCapture:O,onTouchStartCapture:j,onClick:x,style:Me(Me({},a),X)},Q))}))}));ht.displayName="PopupInner";var vt=ht,bt=l["forwardRef"]((function(e,t){var n=e.prefixCls,r=e.visible,o=e.zIndex,a=e.children,i=e.mobile;i=void 0===i?{}:i;var c=i.popupClassName,u=i.popupStyle,s=i.popupMotion,d=void 0===s?{}:s,f=i.popupRender,p=e.onClick,m=l["useRef"]();l["useImperativeHandle"](t,(function(){return{forceAlign:function(){},getElement:function(){return m.current}}}));var v=Me({zIndex:o},u),b=a;return l["Children"].count(a)>1&&(b=l["createElement"]("div",{className:"".concat(n,"-content")},a)),f&&(b=f(b)),l["createElement"](N["b"],Ne({visible:r,ref:m,removeOnLeave:!0},d),(function(e,t){var r=e.className,o=e.style,a=h()(n,c,r);return l["createElement"]("div",{ref:t,className:a,onClick:p,style:Me(Me({},o),v)},b)}))}));bt.displayName="MobilePopupInner";var yt=bt,gt=["visible","mobile"],Ot=l["forwardRef"]((function(e,t){var n=e.visible,r=e.mobile,o=ot(e,gt),a=Object(l["useState"])(n),i=nt(a,2),c=i[0],u=i[1],s=Object(l["useState"])(!1),d=nt(s,2),f=d[0],p=d[1],m=Me(Me({},o),{},{visible:c});Object(l["useEffect"])((function(){u(n),n&&r&&p(Object(T["a"])())}),[n,r]);var h=f?l["createElement"](yt,Ne({},m,{mobile:r,ref:t})):l["createElement"](vt,Ne({},m,{ref:t}));return l["createElement"]("div",null,l["createElement"](it,m),h)}));Ot.displayName="Popup";var jt=Ot,xt=l["createContext"](null),wt=xt;function Ct(){}function St(){return""}function Et(e){return e?e.ownerDocument:window.document}var Pt=["onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur","onContextMenu"];function kt(e){var t=function(t){Ae(r,t);var n=Ke(r);function r(e){var t,o;return Re(this,r),t=n.call(this,e),ke(Le(t),"popupRef",l["createRef"]()),ke(Le(t),"triggerRef",l["createRef"]()),ke(Le(t),"portalContainer",void 0),ke(Le(t),"attachId",void 0),ke(Le(t),"clickOutsideHandler",void 0),ke(Le(t),"touchOutsideHandler",void 0),ke(Le(t),"contextMenuOutsideHandler1",void 0),ke(Le(t),"contextMenuOutsideHandler2",void 0),ke(Le(t),"mouseDownTimeout",void 0),ke(Le(t),"focusTime",void 0),ke(Le(t),"preClickTime",void 0),ke(Le(t),"preTouchTime",void 0),ke(Le(t),"delayTimer",void 0),ke(Le(t),"hasPopupMouseDown",void 0),ke(Le(t),"onMouseEnter",(function(e){var n=t.props.mouseEnterDelay;t.fireEvents("onMouseEnter",e),t.delaySetPopupVisible(!0,n,n?null:e)})),ke(Le(t),"onMouseMove",(function(e){t.fireEvents("onMouseMove",e),t.setPoint(e)})),ke(Le(t),"onMouseLeave",(function(e){t.fireEvents("onMouseLeave",e),t.delaySetPopupVisible(!1,t.props.mouseLeaveDelay)})),ke(Le(t),"onPopupMouseEnter",(function(){t.clearDelayTimer()})),ke(Le(t),"onPopupMouseLeave",(function(e){var n;e.relatedTarget&&!e.relatedTarget.setTimeout&&Object(Ue["a"])(null===(n=t.popupRef.current)||void 0===n?void 0:n.getElement(),e.relatedTarget)||t.delaySetPopupVisible(!1,t.props.mouseLeaveDelay)})),ke(Le(t),"onFocus",(function(e){t.fireEvents("onFocus",e),t.clearDelayTimer(),t.isFocusToShow()&&(t.focusTime=Date.now(),t.delaySetPopupVisible(!0,t.props.focusDelay))})),ke(Le(t),"onMouseDown",(function(e){t.fireEvents("onMouseDown",e),t.preClickTime=Date.now()})),ke(Le(t),"onTouchStart",(function(e){t.fireEvents("onTouchStart",e),t.preTouchTime=Date.now()})),ke(Le(t),"onBlur",(function(e){t.fireEvents("onBlur",e),t.clearDelayTimer(),t.isBlurToHide()&&t.delaySetPopupVisible(!1,t.props.blurDelay)})),ke(Le(t),"onContextMenu",(function(e){e.preventDefault(),t.fireEvents("onContextMenu",e),t.setPopupVisible(!0,e)})),ke(Le(t),"onContextMenuClose",(function(){t.isContextMenuToShow()&&t.close()})),ke(Le(t),"onClick",(function(e){if(t.fireEvents("onClick",e),t.focusTime){var n;if(t.preClickTime&&t.preTouchTime?n=Math.min(t.preClickTime,t.preTouchTime):t.preClickTime?n=t.preClickTime:t.preTouchTime&&(n=t.preTouchTime),Math.abs(n-t.focusTime)<20)return;t.focusTime=0}t.preClickTime=0,t.preTouchTime=0,t.isClickToShow()&&(t.isClickToHide()||t.isBlurToHide())&&e&&e.preventDefault&&e.preventDefault();var r=!t.state.popupVisible;(t.isClickToHide()&&!r||r&&t.isClickToShow())&&t.setPopupVisible(!t.state.popupVisible,e)})),ke(Le(t),"onPopupMouseDown",(function(){var e;(t.hasPopupMouseDown=!0,clearTimeout(t.mouseDownTimeout),t.mouseDownTimeout=window.setTimeout((function(){t.hasPopupMouseDown=!1}),0),t.context)&&(e=t.context).onPopupMouseDown.apply(e,arguments)})),ke(Le(t),"onDocumentClick",(function(e){if(!t.props.mask||t.props.maskClosable){var n=e.target,r=t.getRootDomNode(),o=t.getPopupDomNode();Object(Ue["a"])(r,n)&&!t.isContextMenuOnly()||Object(Ue["a"])(o,n)||t.hasPopupMouseDown||t.close()}})),ke(Le(t),"getRootDomNode",(function(){var e=t.props.getTriggerDOMNode;if(e)return e(t.triggerRef.current);try{var n=Object(We["a"])(t.triggerRef.current);if(n)return n}catch(r){}return Ve.a.findDOMNode(Le(t))})),ke(Le(t),"getPopupClassNameFromAlign",(function(e){var n=[],r=t.props,o=r.popupPlacement,a=r.builtinPlacements,i=r.prefixCls,c=r.alignPoint,l=r.getPopupClassNameFromAlign;return o&&a&&n.push(Ze(a,i,e,c)),l&&n.push(l(e)),n.join(" ")})),ke(Le(t),"getComponent",(function(){var e=t.props,n=e.prefixCls,r=e.destroyPopupOnHide,o=e.popupClassName,a=e.onPopupAlign,i=e.popupMotion,c=e.popupAnimation,u=e.popupTransitionName,s=e.popupStyle,d=e.mask,f=e.maskAnimation,p=e.maskTransitionName,m=e.maskMotion,h=e.zIndex,v=e.popup,b=e.stretch,y=e.alignPoint,g=e.mobile,O=e.forceRender,j=e.onPopupClick,x=t.state,w=x.popupVisible,C=x.point,S=t.getPopupAlign(),E={};return t.isMouseEnterToShow()&&(E.onMouseEnter=t.onPopupMouseEnter),t.isMouseLeaveToHide()&&(E.onMouseLeave=t.onPopupMouseLeave),E.onMouseDown=t.onPopupMouseDown,E.onTouchStart=t.onPopupMouseDown,l["createElement"](jt,Ne({prefixCls:n,destroyPopupOnHide:r,visible:w,point:y&&C,className:o,align:S,onAlign:a,animation:c,getClassNameFromAlign:t.getPopupClassNameFromAlign},E,{stretch:b,getRootDomNode:t.getRootDomNode,style:s,mask:d,zIndex:h,transitionName:u,maskAnimation:f,maskTransitionName:p,maskMotion:m,ref:t.popupRef,motion:i,mobile:g,forceRender:O,onClick:j}),"function"===typeof v?v():v)})),ke(Le(t),"attachParent",(function(e){V["a"].cancel(t.attachId);var n,r=t.props,o=r.getPopupContainer,a=r.getDocument,i=t.getRootDomNode();o?(i||0===o.length)&&(n=o(i)):n=a(t.getRootDomNode()).body,n?n.appendChild(e):t.attachId=Object(V["a"])((function(){t.attachParent(e)}))})),ke(Le(t),"getContainer",(function(){if(!t.portalContainer){var e=t.props.getDocument,n=e(t.getRootDomNode()).createElement("div");n.style.position="absolute",n.style.top="0",n.style.left="0",n.style.width="100%",t.portalContainer=n}return t.attachParent(t.portalContainer),t.portalContainer})),ke(Le(t),"setPoint",(function(e){var n=t.props.alignPoint;n&&e&&t.setState({point:{pageX:e.pageX,pageY:e.pageY}})})),ke(Le(t),"handlePortalUpdate",(function(){t.state.prevPopupVisible!==t.state.popupVisible&&t.props.afterPopupVisibleChange(t.state.popupVisible)})),ke(Le(t),"triggerContextValue",{onPopupMouseDown:t.onPopupMouseDown}),o="popupVisible"in e?!!e.popupVisible:!!e.defaultPopupVisible,t.state={prevPopupVisible:o,popupVisible:o},Pt.forEach((function(e){t["fire".concat(e)]=function(n){t.fireEvents(e,n)}})),t}return De(r,[{key:"componentDidMount",value:function(){this.componentDidUpdate()}},{key:"componentDidUpdate",value:function(){var e,t=this.props,n=this.state;if(n.popupVisible)return this.clickOutsideHandler||!this.isClickToHide()&&!this.isContextMenuToShow()||(e=t.getDocument(this.getRootDomNode()),this.clickOutsideHandler=Object(Ge["a"])(e,"mousedown",this.onDocumentClick)),this.touchOutsideHandler||(e=e||t.getDocument(this.getRootDomNode()),this.touchOutsideHandler=Object(Ge["a"])(e,"touchstart",this.onDocumentClick)),!this.contextMenuOutsideHandler1&&this.isContextMenuToShow()&&(e=e||t.getDocument(this.getRootDomNode()),this.contextMenuOutsideHandler1=Object(Ge["a"])(e,"scroll",this.onContextMenuClose)),void(!this.contextMenuOutsideHandler2&&this.isContextMenuToShow()&&(this.contextMenuOutsideHandler2=Object(Ge["a"])(window,"blur",this.onContextMenuClose)));this.clearOutsideHandler()}},{key:"componentWillUnmount",value:function(){this.clearDelayTimer(),this.clearOutsideHandler(),clearTimeout(this.mouseDownTimeout),V["a"].cancel(this.attachId)}},{key:"getPopupDomNode",value:function(){var e;return(null===(e=this.popupRef.current)||void 0===e?void 0:e.getElement())||null}},{key:"getPopupAlign",value:function(){var e=this.props,t=e.popupPlacement,n=e.popupAlign,r=e.builtinPlacements;return t&&r?Xe(r,t,n):n}},{key:"setPopupVisible",value:function(e,t){var n=this.props.alignPoint,r=this.state.popupVisible;this.clearDelayTimer(),r!==e&&("popupVisible"in this.props||this.setState({popupVisible:e,prevPopupVisible:r}),this.props.onPopupVisibleChange(e)),n&&t&&e&&this.setPoint(t)}},{key:"delaySetPopupVisible",value:function(e,t,n){var r=this,o=1e3*t;if(this.clearDelayTimer(),o){var a=n?{pageX:n.pageX,pageY:n.pageY}:null;this.delayTimer=window.setTimeout((function(){r.setPopupVisible(e,a),r.clearDelayTimer()}),o)}else this.setPopupVisible(e,n)}},{key:"clearDelayTimer",value:function(){this.delayTimer&&(clearTimeout(this.delayTimer),this.delayTimer=null)}},{key:"clearOutsideHandler",value:function(){this.clickOutsideHandler&&(this.clickOutsideHandler.remove(),this.clickOutsideHandler=null),this.contextMenuOutsideHandler1&&(this.contextMenuOutsideHandler1.remove(),this.contextMenuOutsideHandler1=null),this.contextMenuOutsideHandler2&&(this.contextMenuOutsideHandler2.remove(),this.contextMenuOutsideHandler2=null),this.touchOutsideHandler&&(this.touchOutsideHandler.remove(),this.touchOutsideHandler=null)}},{key:"createTwoChains",value:function(e){var t=this.props.children.props,n=this.props;return t[e]&&n[e]?this["fire".concat(e)]:t[e]||n[e]}},{key:"isClickToShow",value:function(){var e=this.props,t=e.action,n=e.showAction;return-1!==t.indexOf("click")||-1!==n.indexOf("click")}},{key:"isContextMenuOnly",value:function(){var e=this.props.action;return"contextMenu"===e||1===e.length&&"contextMenu"===e[0]}},{key:"isContextMenuToShow",value:function(){var e=this.props,t=e.action,n=e.showAction;return-1!==t.indexOf("contextMenu")||-1!==n.indexOf("contextMenu")}},{key:"isClickToHide",value:function(){var e=this.props,t=e.action,n=e.hideAction;return-1!==t.indexOf("click")||-1!==n.indexOf("click")}},{key:"isMouseEnterToShow",value:function(){var e=this.props,t=e.action,n=e.showAction;return-1!==t.indexOf("hover")||-1!==n.indexOf("mouseEnter")}},{key:"isMouseLeaveToHide",value:function(){var e=this.props,t=e.action,n=e.hideAction;return-1!==t.indexOf("hover")||-1!==n.indexOf("mouseLeave")}},{key:"isFocusToShow",value:function(){var e=this.props,t=e.action,n=e.showAction;return-1!==t.indexOf("focus")||-1!==n.indexOf("focus")}},{key:"isBlurToHide",value:function(){var e=this.props,t=e.action,n=e.hideAction;return-1!==t.indexOf("focus")||-1!==n.indexOf("blur")}},{key:"forcePopupAlign",value:function(){var e;this.state.popupVisible&&(null===(e=this.popupRef.current)||void 0===e||e.forceAlign())}},{key:"fireEvents",value:function(e,t){var n=this.props.children.props[e];n&&n(t);var r=this.props[e];r&&r(t)}},{key:"close",value:function(){this.setPopupVisible(!1)}},{key:"render",value:function(){var t=this.state.popupVisible,n=this.props,r=n.children,o=n.forceRender,a=n.alignPoint,i=n.className,c=n.autoDestroy,u=l["Children"].only(r),s={key:"trigger"};this.isContextMenuToShow()?s.onContextMenu=this.onContextMenu:s.onContextMenu=this.createTwoChains("onContextMenu"),this.isClickToHide()||this.isClickToShow()?(s.onClick=this.onClick,s.onMouseDown=this.onMouseDown,s.onTouchStart=this.onTouchStart):(s.onClick=this.createTwoChains("onClick"),s.onMouseDown=this.createTwoChains("onMouseDown"),s.onTouchStart=this.createTwoChains("onTouchStart")),this.isMouseEnterToShow()?(s.onMouseEnter=this.onMouseEnter,a&&(s.onMouseMove=this.onMouseMove)):s.onMouseEnter=this.createTwoChains("onMouseEnter"),this.isMouseLeaveToHide()?s.onMouseLeave=this.onMouseLeave:s.onMouseLeave=this.createTwoChains("onMouseLeave"),this.isFocusToShow()||this.isBlurToHide()?(s.onFocus=this.onFocus,s.onBlur=this.onBlur):(s.onFocus=this.createTwoChains("onFocus"),s.onBlur=this.createTwoChains("onBlur"));var d=h()(u&&u.props&&u.props.className,i);d&&(s.className=d);var f=Me({},s);Object(U["c"])(u)&&(f.ref=Object(U["a"])(this.triggerRef,u.ref));var p,m=l["cloneElement"](u,f);return(t||this.popupRef.current||o)&&(p=l["createElement"](e,{key:"portal",getContainer:this.getContainer,didUpdate:this.handlePortalUpdate},this.getComponent())),!t&&c&&(p=null),l["createElement"](wt.Provider,{value:this.triggerContextValue},m,p)}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.popupVisible,r={};return void 0!==n&&t.popupVisible!==n&&(r.popupVisible=n,r.prevPopupVisible=t.popupVisible),r}}]),r}(l["Component"]);return ke(t,"contextType",wt),ke(t,"defaultProps",{prefixCls:"rc-trigger-popup",getPopupClassNameFromAlign:St,getDocument:Et,onPopupVisibleChange:Ct,afterPopupVisibleChange:Ct,onPopupAlign:Ct,popupClassName:"",mouseEnterDelay:0,mouseLeaveDelay:.1,focusDelay:0,blurDelay:.15,popupStyle:{},destroyPopupOnHide:!1,popupAlign:{},defaultPopupVisible:!1,mask:!1,maskClosable:!0,action:[],showAction:[],hideAction:[],autoDestroy:!1}),t}var Tt=kt(qe["a"]),Mt={adjustX:1,adjustY:1},Nt=[0,0],Rt={topLeft:{points:["bl","tl"],overflow:Mt,offset:[0,-4],targetOffset:Nt},topCenter:{points:["bc","tc"],overflow:Mt,offset:[0,-4],targetOffset:Nt},topRight:{points:["br","tr"],overflow:Mt,offset:[0,-4],targetOffset:Nt},bottomLeft:{points:["tl","bl"],overflow:Mt,offset:[0,4],targetOffset:Nt},bottomCenter:{points:["tc","bc"],overflow:Mt,offset:[0,4],targetOffset:Nt},bottomRight:{points:["tr","br"],overflow:Mt,offset:[0,4],targetOffset:Nt}},Ft=Rt,Dt=n("4IlW"),Lt=n("qE6j"),It=Dt["a"].ESC,At=Dt["a"].TAB;function zt(e){var t=e.visible,n=e.setTriggerVisible,r=e.triggerRef,o=e.onVisibleChange,a=e.autoFocus,i=l["useRef"](!1),c=function(){var e,a,i,c;t&&r.current&&(null===(e=r.current)||void 0===e||null===(a=e.triggerRef)||void 0===a||null===(i=a.current)||void 0===i||null===(c=i.focus)||void 0===c||c.call(i),n(!1),"function"===typeof o&&o(!1))},u=function(){var e,t,n,o,a=Object(Lt["a"])(null===(e=r.current)||void 0===e||null===(t=e.popupRef)||void 0===t||null===(n=t.current)||void 0===n||null===(o=n.getElement)||void 0===o?void 0:o.call(n)),c=a[0];return!!(null===c||void 0===c?void 0:c.focus)&&(c.focus(),i.current=!0,!0)},s=function(e){switch(e.keyCode){case It:c();break;case At:var t=!1;i.current||(t=u()),t?e.preventDefault():c();break}};l["useEffect"]((function(){return t?(window.addEventListener("keydown",s),a&&Object(V["a"])(u,3),function(){window.removeEventListener("keydown",s),i.current=!1}):function(){i.current=!1}}),[t])}var Ht=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus"];function Bt(e,t){var n=e.arrow,r=void 0!==n&&n,o=e.prefixCls,a=void 0===o?"rc-dropdown":o,i=e.transitionName,c=e.animation,u=e.align,s=e.placement,d=void 0===s?"bottomLeft":s,f=e.placements,p=void 0===f?Ft:f,m=e.getPopupContainer,v=e.showAction,b=e.hideAction,y=e.overlayClassName,g=e.overlayStyle,O=e.visible,j=e.trigger,x=void 0===j?["hover"]:j,w=e.autoFocus,C=Ce(e,Ht),S=l["useState"](),E=xe(S,2),P=E[0],k=E[1],T="visible"in e?O:P,M=l["useRef"](null);l["useImperativeHandle"](t,(function(){return M.current})),zt({visible:T,setTriggerVisible:k,triggerRef:M,onVisibleChange:e.onVisibleChange,autoFocus:w});var N=function(){var t,n=e.overlay;return t="function"===typeof n?n():n,t},R=function(t){var n=e.onOverlayClick;k(!1),n&&n(t)},F=function(t){var n=e.onVisibleChange;k(t),"function"===typeof n&&n(t)},D=function(){var e=N();return l["createElement"](l["Fragment"],null,r&&l["createElement"]("div",{className:"".concat(a,"-arrow")}),e)},L=function(){var t=e.overlay;return"function"===typeof t?D:D()},I=function(){var t=e.minOverlayWidthMatchTrigger,n=e.alignPoint;return"minOverlayWidthMatchTrigger"in e?t:!n},A=function(){var t=e.openClassName;return void 0!==t?t:"".concat(a,"-open")},z=function(){var t=e.children,n=t.props?t.props:{},r=h()(n.className,A());return T&&t?l["cloneElement"](t,{className:r}):t},H=b;return H||-1===x.indexOf("contextMenu")||(H=["click"]),l["createElement"](Tt,ve(ve({builtinPlacements:p},C),{},{prefixCls:a,ref:M,popupClassName:h()(y,me({},"".concat(a,"-show-arrow"),r)),popupStyle:g,action:x,showAction:v,hideAction:H||[],popupPlacement:d,popupAlign:u,popupTransitionName:i,popupAnimation:c,popupVisible:T,stretch:I()?"minWidth":"",popup:L(),onPopupVisibleChange:F,onPopupClick:R,getPopupContainer:m}),z())}var Kt=l["forwardRef"](Bt),_t=Kt;function Vt(){return Vt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Vt.apply(this,arguments)}function Ut(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Wt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Gt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wt(Object(n),!0).forEach((function(t){Ut(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function qt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Yt(e){if(Array.isArray(e))return qt(e)}function Xt(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function Zt(e,t){if(e){if("string"===typeof e)return qt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?qt(e,t):void 0}}function Qt(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function $t(e){return Yt(e)||Xt(e)||Zt(e)||Qt()}function Jt(e){if(Array.isArray(e))return e}function en(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,a=void 0;try{for(var i,c=e[Symbol.iterator]();!(r=(i=c.next()).done);r=!0)if(n.push(i.value),t&&n.length===t)break}catch(l){o=!0,a=l}finally{try{r||null==c["return"]||c["return"]()}finally{if(o)throw a}}return n}}function tn(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function nn(e,t){return Jt(e)||en(e,t)||Zt(e,t)||tn()}function rn(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}function on(e,t){if(null==e)return{};var n,r,o=rn(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var an=n("8z13"),cn=n("Kwbf"),ln=n("Wfw6"),un=l["createContext"](null);function sn(e,t){return void 0===e?null:"".concat(e,"-").concat(t)}function dn(e){var t=l["useContext"](un);return sn(t,e)}var fn=n("YrtM"),pn=["children","locked"],mn=l["createContext"](null);function hn(e,t){var n=Gt({},e);return Object.keys(t).forEach((function(e){var r=t[e];void 0!==r&&(n[e]=r)})),n}function vn(e){var t=e.children,n=e.locked,r=on(e,pn),o=l["useContext"](mn),a=Object(fn["a"])((function(){return hn(o,r)}),[o,r],(function(e,t){return!n&&(e[0]!==t[0]||!Object(ln["a"])(e[1],t[1],!0))}));return l["createElement"](mn.Provider,{value:a},t)}var bn=[],yn=l["createContext"](null);function gn(){return l["useContext"](yn)}var On=l["createContext"](bn);function jn(e){var t=l["useContext"](On);return l["useMemo"]((function(){return void 0!==e?[].concat($t(t),[e]):t}),[t,e])}var xn=l["createContext"](null),wn=l["createContext"]({}),Cn=wn,Sn=Dt["a"].LEFT,En=Dt["a"].RIGHT,Pn=Dt["a"].UP,kn=Dt["a"].DOWN,Tn=Dt["a"].ENTER,Mn=Dt["a"].ESC,Nn=Dt["a"].HOME,Rn=Dt["a"].END,Fn=[Pn,kn,Sn,En];function Dn(e,t,n,r){var o,a,i,c,l="prev",u="next",s="children",d="parent";if("inline"===e&&r===Tn)return{inlineTrigger:!0};var f=(o={},Ut(o,Pn,l),Ut(o,kn,u),o),p=(a={},Ut(a,Sn,n?u:l),Ut(a,En,n?l:u),Ut(a,kn,s),Ut(a,Tn,s),a),m=(i={},Ut(i,Pn,l),Ut(i,kn,u),Ut(i,Tn,s),Ut(i,Mn,d),Ut(i,Sn,n?s:d),Ut(i,En,n?d:s),i),h={inline:f,horizontal:p,vertical:m,inlineSub:f,horizontalSub:m,verticalSub:m},v=null===(c=h["".concat(e).concat(t?"":"Sub")])||void 0===c?void 0:c[r];switch(v){case l:return{offset:-1,sibling:!0};case u:return{offset:1,sibling:!0};case d:return{offset:-1,sibling:!1};case s:return{offset:1,sibling:!1};default:return null}}function Ln(e){var t=e;while(t){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}function In(e,t){var n=e||document.activeElement;while(n){if(t.has(n))return n;n=n.parentElement}return null}function An(e,t){var n=Object(Lt["a"])(e,!0);return n.filter((function(e){return t.has(e)}))}function zn(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(!e)return null;var o=An(e,t),a=o.length,i=o.findIndex((function(e){return n===e}));return r<0?-1===i?i=a-1:i-=1:r>0&&(i+=1),i=(i+a)%a,o[i]}function Hn(e,t,n,r,o,a,i,c,u,s){var d=l["useRef"](),f=l["useRef"]();f.current=t;var p=function(){V["a"].cancel(d.current)};return l["useEffect"]((function(){return function(){p()}}),[]),function(l){var m=l.which;if([].concat(Fn,[Tn,Mn,Nn,Rn]).includes(m)){var h,v,b,y=function(){h=new Set,v=new Map,b=new Map;var e=a();return e.forEach((function(e){var t=document.querySelector("[data-menu-id='".concat(sn(r,e),"']"));t&&(h.add(t),b.set(t,e),v.set(e,t))})),h};y();var g=v.get(t),O=In(g,h),j=b.get(O),x=Dn(e,1===i(j,!0).length,n,m);if(!x&&m!==Nn&&m!==Rn)return;(Fn.includes(m)||[Nn,Rn].includes(m))&&l.preventDefault();var w=function(e){if(e){var t=e,n=e.querySelector("a");null!==n&&void 0!==n&&n.getAttribute("href")&&(t=n);var r=b.get(e);c(r),p(),d.current=Object(V["a"])((function(){f.current===r&&t.focus()}))}};if([Nn,Rn].includes(m)||x.sibling||!O){var C,S;C=O&&"inline"!==e?Ln(O):o.current;var E=An(C,h);S=m===Nn?E[0]:m===Rn?E[E.length-1]:zn(C,h,O,x.offset),w(S)}else if(x.inlineTrigger)u(j);else if(x.offset>0)u(j,!0),p(),d.current=Object(V["a"])((function(){y();var e=O.getAttribute("aria-controls"),t=document.getElementById(e),n=zn(t,h);w(n)}),5);else if(x.offset<0){var P=i(j,!0),k=P[P.length-2],T=v.get(k);u(k,!1),w(T)}}null===s||void 0===s||s(l)}}function Bn(e){Promise.resolve().then(e)}var Kn="__RC_UTIL_PATH_SPLIT__",_n=function(e){return e.join(Kn)},Vn=function(e){return e.split(Kn)},Un="rc-menu-more";function Wn(){var e=l["useState"]({}),t=nn(e,2),n=t[1],r=Object(l["useRef"])(new Map),o=Object(l["useRef"])(new Map),a=l["useState"]([]),i=nn(a,2),c=i[0],u=i[1],s=Object(l["useRef"])(0),d=Object(l["useRef"])(!1),f=function(){d.current||n({})},p=Object(l["useCallback"])((function(e,t){var n=_n(t);o.current.set(n,e),r.current.set(e,n),s.current+=1;var a=s.current;Bn((function(){a===s.current&&f()}))}),[]),m=Object(l["useCallback"])((function(e,t){var n=_n(t);o.current.delete(n),r.current.delete(e)}),[]),h=Object(l["useCallback"])((function(e){u(e)}),[]),v=Object(l["useCallback"])((function(e,t){var n=r.current.get(e)||"",o=Vn(n);return t&&c.includes(o[0])&&o.unshift(Un),o}),[c]),b=Object(l["useCallback"])((function(e,t){return e.some((function(e){var n=v(e,!0);return n.includes(t)}))}),[v]),y=function(){var e=$t(r.current.keys());return c.length&&e.push(Un),e},g=Object(l["useCallback"])((function(e){var t="".concat(r.current.get(e)).concat(Kn),n=new Set;return $t(o.current.keys()).forEach((function(e){e.startsWith(t)&&n.add(o.current.get(e))})),n}),[]);return l["useEffect"]((function(){return function(){d.current=!0}}),[]),{registerPath:p,unregisterPath:m,refreshOverflowKeys:h,isSubPathKey:b,getKeyPath:v,getKeys:y,getSubPathKeys:g}}function Gn(e){var t=l["useRef"](e);t.current=e;var n=l["useCallback"]((function(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(r))}),[]);return e?n:void 0}var qn=Math.random().toFixed(5).toString().slice(2),Yn=0;function Xn(e){var t=Object(M["a"])(e,{value:e}),n=nn(t,2),r=n[0],o=n[1];return l["useEffect"]((function(){Yn+=1;var e="".concat(qn,"-").concat(Yn);o("rc-menu-uuid-".concat(e))}),[]),r}function Zn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function $n(e,t,n){return t&&Qn(e.prototype,t),n&&Qn(e,n),e}function Jn(e,t){return Jn=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},Jn(e,t)}function er(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Jn(e,t)}function tr(e){return tr=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},tr(e)}function nr(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function rr(e){return rr="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rr(e)}function or(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ar(e,t){return!t||"object"!==rr(t)&&"function"!==typeof t?or(e):t}function ir(e){var t=nr();return function(){var n,r=tr(e);if(t){var o=tr(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return ar(this,n)}}var cr=n("bT9E");function lr(e,t,n,r){var o=l["useContext"](mn),a=o.activeKey,i=o.onActive,c=o.onInactive,u={active:a===e};return t||(u.onMouseEnter=function(t){null===n||void 0===n||n({key:e,domEvent:t}),i(e)},u.onMouseLeave=function(t){null===r||void 0===r||r({key:e,domEvent:t}),c(e)}),u}function ur(e){var t=l["useContext"](mn),n=t.mode,r=t.rtl,o=t.inlineIndent;if("inline"!==n)return null;var a=e;return r?{paddingRight:a*o}:{paddingLeft:a*o}}function sr(e){var t,n=e.icon,r=e.props,o=e.children;return t="function"===typeof n?l["createElement"](n,Gt({},r)):n,t||o||null}var dr=["item"];function fr(e){var t=e.item,n=on(e,dr);return Object.defineProperty(n,"item",{get:function(){return Object(cn["a"])(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),n}var pr=["title","attribute","elementRef"],mr=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],hr=["active"],vr=function(e){er(n,e);var t=ir(n);function n(){return Zn(this,n),t.apply(this,arguments)}return $n(n,[{key:"render",value:function(){var e=this.props,t=e.title,n=e.attribute,r=e.elementRef,o=on(e,pr),a=Object(cr["a"])(o,["eventKey","popupClassName","popupOffset","onTitleClick"]);return Object(cn["a"])(!n,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),l["createElement"](an["a"].Item,Vt({},n,{title:"string"===typeof t?t:void 0},a,{ref:r}))}}]),n}(l["Component"]),br=l["forwardRef"]((function(e,t){var n,r=e.style,o=e.className,a=e.eventKey,i=(e.warnKey,e.disabled),c=e.itemIcon,u=e.children,s=e.role,d=e.onMouseEnter,f=e.onMouseLeave,p=e.onClick,m=e.onKeyDown,v=e.onFocus,b=on(e,mr),y=dn(a),g=l["useContext"](mn),O=g.prefixCls,j=g.onItemClick,x=g.disabled,w=g.overflowDisabled,C=g.itemIcon,S=g.selectedKeys,E=g.onActive,P=l["useContext"](Cn),k=P._internalRenderMenuItem,T="".concat(O,"-item"),M=l["useRef"](),N=l["useRef"](),R=x||i,F=Object(U["d"])(t,N),D=jn(a);var L=function(e){return{key:a,keyPath:$t(D).reverse(),item:M.current,domEvent:e}},I=c||C,A=lr(a,R,d,f),z=A.active,H=on(A,hr),B=S.includes(a),K=ur(D.length),_=function(e){if(!R){var t=L(e);null===p||void 0===p||p(fr(t)),j(t)}},V=function(e){if(null===m||void 0===m||m(e),e.which===Dt["a"].ENTER){var t=L(e);null===p||void 0===p||p(fr(t)),j(t)}},W=function(e){E(a),null===v||void 0===v||v(e)},G={};"option"===e.role&&(G["aria-selected"]=B);var q=l["createElement"](vr,Vt({ref:M,elementRef:F,role:null===s?"none":s||"menuitem",tabIndex:i?null:-1,"data-menu-id":w&&y?null:y},b,H,G,{component:"li","aria-disabled":i,style:Gt(Gt({},K),r),className:h()(T,(n={},Ut(n,"".concat(T,"-active"),z),Ut(n,"".concat(T,"-selected"),B),Ut(n,"".concat(T,"-disabled"),R),n),o),onClick:_,onKeyDown:V,onFocus:W}),u,l["createElement"](sr,{props:Gt(Gt({},e),{},{isSelected:B}),icon:I}));return k&&(q=k(q,e,{selected:B})),q}));function yr(e,t){var n=e.eventKey,r=gn(),o=jn(n);return l["useEffect"]((function(){if(r)return r.registerPath(n,o),function(){r.unregisterPath(n,o)}}),[o]),r?null:l["createElement"](br,Vt({},e,{ref:t}))}var gr=l["forwardRef"](yr),Or=["className","children"],jr=function(e,t){var n=e.className,r=e.children,o=on(e,Or),a=l["useContext"](mn),i=a.prefixCls,c=a.mode,u=a.rtl;return l["createElement"]("ul",Vt({className:h()(i,u&&"".concat(i,"-rtl"),"".concat(i,"-sub"),"".concat(i,"-").concat("inline"===c?"inline":"vertical"),n),role:"menu"},o,{"data-menu-list":!0,ref:t}),r)},xr=l["forwardRef"](jr);xr.displayName="SubMenuList";var wr=xr,Cr=n("Zm9Q"),Sr=["label","children","key","type"];function Er(e,t){return Object(Cr["a"])(e).map((function(e,n){if(l["isValidElement"](e)){var r,o,a=e.key,i=null!==(r=null===(o=e.props)||void 0===o?void 0:o.eventKey)&&void 0!==r?r:a,c=null===i||void 0===i;c&&(i="tmp_key-".concat([].concat($t(t),[n]).join("-")));var u={key:i,eventKey:i};return l["cloneElement"](e,u)}return e}))}function Pr(e){return(e||[]).map((function(e,t){if(e&&"object"===rr(e)){var n=e,r=n.label,o=n.children,a=n.key,i=n.type,c=on(n,Sr),u=null!==a&&void 0!==a?a:"tmp-".concat(t);return o||"group"===i?"group"===i?l["createElement"](qr,Vt({key:u},c,{title:r}),Pr(o)):l["createElement"](Hr,Vt({key:u},c,{title:r}),Pr(o)):"divider"===i?l["createElement"](Yr,Vt({key:u},c)):l["createElement"](gr,Vt({key:u},c),r)}return null})).filter((function(e){return e}))}function kr(e,t,n){var r=e;return t&&(r=Pr(t)),Er(r,n)}var Tr={adjustX:1,adjustY:1},Mr={topLeft:{points:["bl","tl"],overflow:Tr,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:Tr,offset:[0,7]},leftTop:{points:["tr","tl"],overflow:Tr,offset:[-4,0]},rightTop:{points:["tl","tr"],overflow:Tr,offset:[4,0]}},Nr={topLeft:{points:["bl","tl"],overflow:Tr,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:Tr,offset:[0,7]},rightTop:{points:["tr","tl"],overflow:Tr,offset:[-4,0]},leftTop:{points:["tl","tr"],overflow:Tr,offset:[4,0]}};function Rr(e,t,n){return t||(n?n[e]||n.other:void 0)}var Fr={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function Dr(e){var t=e.prefixCls,n=e.visible,r=e.children,o=e.popup,a=e.popupClassName,i=e.popupOffset,c=e.disabled,u=e.mode,s=e.onVisibleChange,d=l["useContext"](mn),f=d.getPopupContainer,p=d.rtl,m=d.subMenuOpenDelay,v=d.subMenuCloseDelay,b=d.builtinPlacements,y=d.triggerSubMenuAction,g=d.forceSubMenuRender,O=d.rootClassName,j=d.motion,x=d.defaultMotions,w=l["useState"](!1),C=nn(w,2),S=C[0],E=C[1],P=Gt(Gt({},p?Nr:Mr),b),k=Fr[u],T=Rr(u,j,x),M=l["useRef"](T);"inline"!==u&&(M.current=T);var N=Gt(Gt({},M.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),R=l["useRef"]();return l["useEffect"]((function(){return R.current=Object(V["a"])((function(){E(n)})),function(){V["a"].cancel(R.current)}}),[n]),l["createElement"](Tt,{prefixCls:t,popupClassName:h()("".concat(t,"-popup"),Ut({},"".concat(t,"-rtl"),p),a,O),stretch:"horizontal"===u?"minWidth":null,getPopupContainer:f,builtinPlacements:P,popupPlacement:k,popupVisible:S,popup:o,popupAlign:i&&{offset:i},action:c?[]:[y],mouseEnterDelay:m,mouseLeaveDelay:v,onPopupVisibleChange:s,forceRender:g,popupMotion:N},r)}function Lr(e){var t=e.id,n=e.open,r=e.keyPath,o=e.children,a="inline",i=l["useContext"](mn),c=i.prefixCls,u=i.forceSubMenuRender,s=i.motion,d=i.defaultMotions,f=i.mode,p=l["useRef"](!1);p.current=f===a;var m=l["useState"](!p.current),h=nn(m,2),v=h[0],b=h[1],y=!!p.current&&n;l["useEffect"]((function(){p.current&&b(!1)}),[f]);var g=Gt({},Rr(a,s,d));r.length>1&&(g.motionAppear=!1);var O=g.onVisibleChanged;return g.onVisibleChanged=function(e){return p.current||e||b(!0),null===O||void 0===O?void 0:O(e)},v?null:l["createElement"](vn,{mode:a,locked:!p.current},l["createElement"](N["b"],Vt({visible:y},g,{forceRender:u,removeOnLeave:!1,leavedClassName:"".concat(c,"-hidden")}),(function(e){var n=e.className,r=e.style;return l["createElement"](wr,{id:t,className:n,style:r},o)})))}var Ir=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],Ar=["active"],zr=function(e){var t,n=e.style,r=e.className,o=e.title,a=e.eventKey,i=(e.warnKey,e.disabled),c=e.internalPopupClose,u=e.children,s=e.itemIcon,d=e.expandIcon,f=e.popupClassName,p=e.popupOffset,m=e.onClick,v=e.onMouseEnter,b=e.onMouseLeave,y=e.onTitleClick,g=e.onTitleMouseEnter,O=e.onTitleMouseLeave,j=on(e,Ir),x=dn(a),w=l["useContext"](mn),C=w.prefixCls,S=w.mode,E=w.openKeys,P=w.disabled,k=w.overflowDisabled,T=w.activeKey,M=w.selectedKeys,N=w.itemIcon,R=w.expandIcon,F=w.onItemClick,D=w.onOpenChange,L=w.onActive,I=l["useContext"](Cn),A=I._internalRenderSubMenuItem,z=l["useContext"](xn),H=z.isSubPathKey,B=jn(),K="".concat(C,"-submenu"),_=P||i,V=l["useRef"](),U=l["useRef"]();var W=s||N,G=d||R,q=E.includes(a),Y=!k&&q,X=H(M,a),Z=lr(a,_,g,O),Q=Z.active,$=on(Z,Ar),J=l["useState"](!1),ee=nn(J,2),te=ee[0],ne=ee[1],re=function(e){_||ne(e)},oe=function(e){re(!0),null===v||void 0===v||v({key:a,domEvent:e})},ae=function(e){re(!1),null===b||void 0===b||b({key:a,domEvent:e})},ie=l["useMemo"]((function(){return Q||"inline"!==S&&(te||H([T],a))}),[S,Q,T,te,a,H]),ce=ur(B.length),le=function(e){_||(null===y||void 0===y||y({key:a,domEvent:e}),"inline"===S&&D(a,!q))},ue=Gn((function(e){null===m||void 0===m||m(fr(e)),F(e)})),se=function(e){"inline"!==S&&D(a,e)},de=function(){L(a)},fe=x&&"".concat(x,"-popup"),pe=l["createElement"]("div",Vt({role:"menuitem",style:ce,className:"".concat(K,"-title"),tabIndex:_?null:-1,ref:V,title:"string"===typeof o?o:null,"data-menu-id":k&&x?null:x,"aria-expanded":Y,"aria-haspopup":!0,"aria-controls":fe,"aria-disabled":_,onClick:le,onFocus:de},$),o,l["createElement"](sr,{icon:"horizontal"!==S?G:null,props:Gt(Gt({},e),{},{isOpen:Y,isSubMenu:!0})},l["createElement"]("i",{className:"".concat(K,"-arrow")}))),me=l["useRef"](S);if("inline"!==S&&B.length>1?me.current="vertical":me.current=S,!k){var he=me.current;pe=l["createElement"](Dr,{mode:he,prefixCls:K,visible:!c&&Y&&"inline"!==S,popupClassName:f,popupOffset:p,popup:l["createElement"](vn,{mode:"horizontal"===he?"vertical":he},l["createElement"](wr,{id:fe,ref:U},u)),disabled:_,onVisibleChange:se},pe)}var ve=l["createElement"](an["a"].Item,Vt({role:"none"},j,{component:"li",style:n,className:h()(K,"".concat(K,"-").concat(S),r,(t={},Ut(t,"".concat(K,"-open"),Y),Ut(t,"".concat(K,"-active"),ie),Ut(t,"".concat(K,"-selected"),X),Ut(t,"".concat(K,"-disabled"),_),t)),onMouseEnter:oe,onMouseLeave:ae}),pe,!k&&l["createElement"](Lr,{id:fe,open:Y,keyPath:B},u));return A&&(ve=A(ve,e,{selected:X,active:ie,open:Y,disabled:_})),l["createElement"](vn,{onItemClick:ue,mode:"horizontal"===S?"vertical":S,itemIcon:W,expandIcon:G},ve)};function Hr(e){var t,n=e.eventKey,r=e.children,o=jn(n),a=Er(r,o),i=gn();return l["useEffect"]((function(){if(i)return i.registerPath(n,o),function(){i.unregisterPath(n,o)}}),[o]),t=i?a:l["createElement"](zr,e,a),l["createElement"](On.Provider,{value:o},t)}var Br=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem"],Kr=[],_r=l["forwardRef"]((function(e,t){var n,r,o=e,a=o.prefixCls,i=void 0===a?"rc-menu":a,c=o.rootClassName,u=o.style,s=o.className,d=o.tabIndex,f=void 0===d?0:d,p=o.items,m=o.children,v=o.direction,b=o.id,y=o.mode,g=void 0===y?"vertical":y,O=o.inlineCollapsed,j=o.disabled,x=o.disabledOverflow,w=o.subMenuOpenDelay,C=void 0===w?.1:w,S=o.subMenuCloseDelay,E=void 0===S?.1:S,P=o.forceSubMenuRender,k=o.defaultOpenKeys,T=o.openKeys,N=o.activeKey,R=o.defaultActiveFirst,F=o.selectable,D=void 0===F||F,L=o.multiple,I=void 0!==L&&L,A=o.defaultSelectedKeys,z=o.selectedKeys,H=o.onSelect,B=o.onDeselect,K=o.inlineIndent,_=void 0===K?24:K,V=o.motion,U=o.defaultMotions,W=o.triggerSubMenuAction,G=void 0===W?"hover":W,q=o.builtinPlacements,Y=o.itemIcon,X=o.expandIcon,Z=o.overflowedIndicator,Q=void 0===Z?"...":Z,$=o.overflowedIndicatorPopupClassName,J=o.getPopupContainer,ee=o.onClick,te=o.onOpenChange,ne=o.onKeyDown,re=(o.openAnimation,o.openTransitionName,o._internalRenderMenuItem),oe=o._internalRenderSubMenuItem,ae=on(o,Br),ie=l["useMemo"]((function(){return kr(m,p,Kr)}),[m,p]),ce=l["useState"](!1),le=nn(ce,2),ue=le[0],se=le[1],de=l["useRef"](),fe=Xn(b),pe="rtl"===v;var me=Object(M["a"])(k,{value:T,postState:function(e){return e||Kr}}),he=nn(me,2),ve=he[0],be=he[1],ye=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];function n(){be(e),null===te||void 0===te||te(e)}t?Object(_e["flushSync"])(n):n()},ge=l["useState"](ve),Oe=nn(ge,2),je=Oe[0],xe=Oe[1],we=l["useRef"](!1),Ce=l["useMemo"]((function(){return"inline"!==g&&"vertical"!==g||!O?[g,!1]:["vertical",O]}),[g,O]),Se=nn(Ce,2),Ee=Se[0],Pe=Se[1],ke="inline"===Ee,Te=l["useState"](Ee),Me=nn(Te,2),Ne=Me[0],Re=Me[1],Fe=l["useState"](Pe),De=nn(Fe,2),Le=De[0],Ie=De[1];l["useEffect"]((function(){Re(Ee),Ie(Pe),we.current&&(ke?be(je):ye(Kr))}),[Ee,Pe]);var Ae=l["useState"](0),ze=nn(Ae,2),He=ze[0],Be=ze[1],Ke=He>=ie.length-1||"horizontal"!==Ne||x;l["useEffect"]((function(){ke&&xe(ve)}),[ve]),l["useEffect"]((function(){return we.current=!0,function(){we.current=!1}}),[]);var Ve=Wn(),Ue=Ve.registerPath,We=Ve.unregisterPath,Ge=Ve.refreshOverflowKeys,qe=Ve.isSubPathKey,Ye=Ve.getKeyPath,Xe=Ve.getKeys,Ze=Ve.getSubPathKeys,Qe=l["useMemo"]((function(){return{registerPath:Ue,unregisterPath:We}}),[Ue,We]),$e=l["useMemo"]((function(){return{isSubPathKey:qe}}),[qe]);l["useEffect"]((function(){Ge(Ke?Kr:ie.slice(He+1).map((function(e){return e.key})))}),[He,Ke]);var Je=Object(M["a"])(N||R&&(null===(n=ie[0])||void 0===n?void 0:n.key),{value:N}),et=nn(Je,2),tt=et[0],nt=et[1],rt=Gn((function(e){nt(e)})),ot=Gn((function(){nt(void 0)}));Object(l["useImperativeHandle"])(t,(function(){return{list:de.current,focus:function(e){var t,n,r,o,a=null!==tt&&void 0!==tt?tt:null===(t=ie.find((function(e){return!e.props.disabled})))||void 0===t?void 0:t.key;a&&(null===(n=de.current)||void 0===n||null===(r=n.querySelector("li[data-menu-id='".concat(sn(fe,a),"']")))||void 0===r||null===(o=r.focus)||void 0===o||o.call(r,e))}}}));var at=Object(M["a"])(A||[],{value:z,postState:function(e){return Array.isArray(e)?e:null===e||void 0===e?Kr:[e]}}),it=nn(at,2),ct=it[0],lt=it[1],ut=function(e){if(D){var t,n=e.key,r=ct.includes(n);t=I?r?ct.filter((function(e){return e!==n})):[].concat($t(ct),[n]):[n],lt(t);var o=Gt(Gt({},e),{},{selectedKeys:t});r?null===B||void 0===B||B(o):null===H||void 0===H||H(o)}!I&&ve.length&&"inline"!==Ne&&ye(Kr)},st=Gn((function(e){null===ee||void 0===ee||ee(fr(e)),ut(e)})),dt=Gn((function(e,t){var n=ve.filter((function(t){return t!==e}));if(t)n.push(e);else if("inline"!==Ne){var r=Ze(e);n=n.filter((function(e){return!r.has(e)}))}Object(ln["a"])(ve,n,!0)||ye(n,!0)})),ft=Gn(J),pt=function(e,t){var n=null!==t&&void 0!==t?t:!ve.includes(e);dt(e,n)},mt=Hn(Ne,tt,pe,fe,de,Xe,Ye,nt,pt,ne);l["useEffect"]((function(){se(!0)}),[]);var ht=l["useMemo"]((function(){return{_internalRenderMenuItem:re,_internalRenderSubMenuItem:oe}}),[re,oe]),vt="horizontal"!==Ne||x?ie:ie.map((function(e,t){return l["createElement"](vn,{key:e.key,overflowDisabled:t>He},e)})),bt=l["createElement"](an["a"],Vt({id:b,ref:de,prefixCls:"".concat(i,"-overflow"),component:"ul",itemComponent:gr,className:h()(i,"".concat(i,"-root"),"".concat(i,"-").concat(Ne),s,(r={},Ut(r,"".concat(i,"-inline-collapsed"),Le),Ut(r,"".concat(i,"-rtl"),pe),r),c),dir:v,style:u,role:"menu",tabIndex:f,data:vt,renderRawItem:function(e){return e},renderRawRest:function(e){var t=e.length,n=t?ie.slice(-t):null;return l["createElement"](Hr,{eventKey:Un,title:Q,disabled:Ke,internalPopupClose:0===t,popupClassName:$},n)},maxCount:"horizontal"!==Ne||x?an["a"].INVALIDATE:an["a"].RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(e){Be(e)},onKeyDown:mt},ae));return l["createElement"](Cn.Provider,{value:ht},l["createElement"](un.Provider,{value:fe},l["createElement"](vn,{prefixCls:i,rootClassName:c,mode:Ne,openKeys:ve,rtl:pe,disabled:j,motion:ue?V:null,defaultMotions:ue?U:null,activeKey:tt,onActive:rt,onInactive:ot,selectedKeys:ct,inlineIndent:_,subMenuOpenDelay:C,subMenuCloseDelay:E,forceSubMenuRender:P,builtinPlacements:q,triggerSubMenuAction:G,getPopupContainer:ft,itemIcon:Y,expandIcon:X,onItemClick:st,onOpenChange:dt},l["createElement"](xn.Provider,{value:$e},bt),l["createElement"]("div",{style:{display:"none"},"aria-hidden":!0},l["createElement"](yn.Provider,{value:Qe},ie)))))})),Vr=_r,Ur=["className","title","eventKey","children"],Wr=["children"],Gr=function(e){var t=e.className,n=e.title,r=(e.eventKey,e.children),o=on(e,Ur),a=l["useContext"](mn),i=a.prefixCls,c="".concat(i,"-item-group");return l["createElement"]("li",Vt({role:"presentation"},o,{onClick:function(e){return e.stopPropagation()},className:h()(c,t)}),l["createElement"]("div",{role:"presentation",className:"".concat(c,"-title"),title:"string"===typeof n?n:void 0},n),l["createElement"]("ul",{role:"group",className:"".concat(c,"-list")},r))};function qr(e){var t=e.children,n=on(e,Wr),r=jn(n.eventKey),o=Er(t,r),a=gn();return a?o:l["createElement"](Gr,Object(cr["a"])(n,["warnKey"]),o)}function Yr(e){var t=e.className,n=e.style,r=l["useContext"](mn),o=r.prefixCls,a=gn();return a?null:l["createElement"]("li",{className:h()("".concat(o,"-item-divider"),t),style:n})}var Xr=Vr;Xr.Item=gr,Xr.SubMenu=Hr,Xr.ItemGroup=qr,Xr.Divider=Yr;var Zr=Xr;function Qr(e,t){var n=e.prefixCls,r=e.id,o=e.tabs,a=e.locale,i=e.mobile,c=e.moreIcon,u=void 0===c?"More":c,s=e.moreTransitionName,d=e.style,f=e.className,p=e.editable,m=e.tabBarGutter,v=e.rtl,y=e.removeAriaLabel,g=e.onTabClick,O=e.getPopupContainer,j=e.popupClassName,x=Object(l["useState"])(!1),w=S(x,2),C=w[0],E=w[1],P=Object(l["useState"])(null),k=S(P,2),T=k[0],M=k[1],N="".concat(r,"-more-popup"),R="".concat(n,"-dropdown"),F=null!==T?"".concat(N,"-").concat(T):null,D=null===a||void 0===a?void 0:a.dropdownAriaLabel;function L(e,t){e.preventDefault(),e.stopPropagation(),p.onEdit("remove",{key:t,event:e})}var I=l["createElement"](Zr,{onClick:function(e){var t=e.key,n=e.domEvent;g(t,n),E(!1)},prefixCls:"".concat(R,"-menu"),id:N,tabIndex:-1,role:"listbox","aria-activedescendant":F,selectedKeys:[T],"aria-label":void 0!==D?D:"expanded dropdown"},o.map((function(e){var t=p&&!1!==e.closable&&!e.disabled;return l["createElement"](gr,{key:e.key,id:"".concat(N,"-").concat(e.key),role:"option","aria-controls":r&&"".concat(r,"-panel-").concat(e.key),disabled:e.disabled},l["createElement"]("span",null,e.label),t&&l["createElement"]("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(R,"-menu-item-remove"),onClick:function(t){t.stopPropagation(),L(t,e.key)}},e.closeIcon||p.removeIcon||"\xd7"))})));function A(e){for(var t=o.filter((function(e){return!e.disabled})),n=t.findIndex((function(e){return e.key===T}))||0,r=t.length,a=0;a<r;a+=1){n=(n+e+r)%r;var i=t[n];if(!i.disabled)return void M(i.key)}}function z(e){var t=e.which;if(C)switch(t){case Dt["a"].UP:A(-1),e.preventDefault();break;case Dt["a"].DOWN:A(1),e.preventDefault();break;case Dt["a"].ESC:E(!1);break;case Dt["a"].SPACE:case Dt["a"].ENTER:null!==T&&g(T,e);break}else[Dt["a"].DOWN,Dt["a"].SPACE,Dt["a"].ENTER].includes(t)&&(E(!0),e.preventDefault())}Object(l["useEffect"])((function(){var e=document.getElementById(F);e&&e.scrollIntoView&&e.scrollIntoView(!1)}),[T]),Object(l["useEffect"])((function(){C||M(null)}),[C]);var H=b({},v?"marginRight":"marginLeft",m);o.length||(H.visibility="hidden",H.order=1);var B=h()(b({},"".concat(R,"-rtl"),v)),K=i?null:l["createElement"](_t,{prefixCls:R,overlay:I,trigger:["hover"],visible:!!o.length&&C,transitionName:s,onVisibleChange:E,overlayClassName:h()(B,j),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:O},l["createElement"]("button",{type:"button",className:"".concat(n,"-nav-more"),style:H,tabIndex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":N,id:"".concat(r,"-more"),"aria-expanded":C,onKeyDown:z},u));return l["createElement"]("div",{className:h()("".concat(n,"-nav-operations"),f),style:d,ref:t},K,l["createElement"](le,{prefixCls:n,locale:a,editable:p}))}var $r=l["memo"](l["forwardRef"](Qr),(function(e,t){return t.tabMoving}));function Jr(e){var t,n=e.prefixCls,r=e.id,o=e.active,a=e.tab,i=a.key,c=a.label,u=a.disabled,s=a.closeIcon,d=e.closable,f=e.renderWrapper,p=e.removeAriaLabel,m=e.editable,v=e.onClick,y=e.onFocus,g=e.style,O="".concat(n,"-tab"),j=m&&!1!==d&&!u;function x(e){u||v(e)}function w(e){e.preventDefault(),e.stopPropagation(),m.onEdit("remove",{key:i,event:e})}var C=l["createElement"]("div",{key:i,"data-node-key":ie(i),className:h()(O,(t={},b(t,"".concat(O,"-with-remove"),j),b(t,"".concat(O,"-active"),o),b(t,"".concat(O,"-disabled"),u),t)),style:g,onClick:x},l["createElement"]("div",{role:"tab","aria-selected":o,id:r&&"".concat(r,"-tab-").concat(i),className:"".concat(O,"-btn"),"aria-controls":r&&"".concat(r,"-panel-").concat(i),"aria-disabled":u,tabIndex:u?null:0,onClick:function(e){e.stopPropagation(),x(e)},onKeyDown:function(e){[Dt["a"].SPACE,Dt["a"].ENTER].includes(e.which)&&(e.preventDefault(),x(e))},onFocus:y},c),j&&l["createElement"]("button",{type:"button","aria-label":p||"remove",tabIndex:0,className:"".concat(O,"-remove"),onClick:function(e){e.stopPropagation(),w(e)}},s||m.removeIcon||"\xd7"));return f?f(C):C}var eo=Jr,to=function(e){var t=e.current||{},n=t.offsetWidth,r=void 0===n?0:n,o=t.offsetHeight,a=void 0===o?0:o;return[r,a]},no=function(e,t){return e[t?0:1]};function ro(e,t){var n,r=l["useContext"](R),o=r.prefixCls,a=r.tabs,i=e.className,c=e.style,u=e.id,s=e.animated,d=e.activeKey,f=e.rtl,p=e.extra,m=e.editable,y=e.locale,O=e.tabPosition,j=e.tabBarGutter,x=e.children,w=e.onTabClick,C=e.onTabScroll,E=Object(l["useRef"])(),P=Object(l["useRef"])(),k=Object(l["useRef"])(),T=Object(l["useRef"])(),M=Object(l["useRef"])(),N=Object(l["useRef"])(),F=Object(l["useRef"])(),D="top"===O||"bottom"===O,L=q(0,(function(e,t){D&&C&&C({direction:e>t?"left":"right"})})),I=S(L,2),A=I[0],z=I[1],H=q(0,(function(e,t){!D&&C&&C({direction:e>t?"top":"bottom"})})),W=S(H,2),Y=W[0],X=W[1],Z=Object(l["useState"])([0,0]),Q=S(Z,2),J=Q[0],ne=Q[1],ae=Object(l["useState"])([0,0]),ce=S(ae,2),ue=ce[0],de=ce[1],fe=Object(l["useState"])([0,0]),pe=S(fe,2),me=pe[0],he=pe[1],ve=Object(l["useState"])([0,0]),be=S(ve,2),ye=be[0],ge=be[1],Oe=te(new Map),je=S(Oe,2),xe=je[0],we=je[1],Ce=G(a,xe,ue[0]),Se=no(J,D),Ee=no(ue,D),Pe=no(me,D),ke=no(ye,D),Te=Se<Ee+Pe,Me=Te?Se-ke:Se-Pe,Ne="".concat(o,"-nav-operations-hidden"),Re=0,Fe=0;function De(e){return e<Re?Re:e>Fe?Fe:e}D&&f?(Re=0,Fe=Math.max(0,Ee-Me)):(Re=Math.min(0,Me-Ee),Fe=0);var Le=Object(l["useRef"])(),Ie=Object(l["useState"])(),Ae=S(Ie,2),ze=Ae[0],He=Ae[1];function Be(){He(Date.now())}function Ke(){window.clearTimeout(Le.current)}$(T,(function(e,t){function n(e,t){e((function(e){var n=De(e+t);return n}))}return!!Te&&(D?n(z,e):n(X,t),Ke(),Be(),!0)})),Object(l["useEffect"])((function(){return Ke(),ze&&(Le.current=window.setTimeout((function(){He(0)}),100)),Ke}),[ze]);var _e=re(Ce,Me,D?A:Y,Ee,Pe,ke,g(g({},e),{},{tabs:a})),Ve=S(_e,2),Ue=Ve[0],We=Ve[1],Ge=Object(_["a"])((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:d,t=Ce.get(e)||{width:0,height:0,left:0,right:0,top:0};if(D){var n=A;f?t.right<A?n=t.right:t.right+t.width>A+Me&&(n=t.right+t.width-Me):t.left<-A?n=-t.left:t.left+t.width>-A+Me&&(n=-(t.left+t.width-Me)),X(0),z(De(n))}else{var r=Y;t.top<-Y?r=-t.top:t.top+t.height>-Y+Me&&(r=-(t.top+t.height-Me)),z(0),X(De(r))}})),qe={};"top"===O||"bottom"===O?qe[f?"marginRight":"marginLeft"]=j:qe.marginTop=j;var Ye=a.map((function(e,t){var n=e.key;return l["createElement"](eo,{id:u,prefixCls:o,key:n,tab:e,style:0===t?void 0:qe,closable:e.closable,editable:m,active:n===d,renderWrapper:x,removeAriaLabel:null===y||void 0===y?void 0:y.removeAriaLabel,onClick:function(e){w(n,e)},onFocus:function(){Ge(n),Be(),T.current&&(f||(T.current.scrollLeft=0),T.current.scrollTop=0)}})})),Xe=function(){return we((function(){var e=new Map;return a.forEach((function(t){var n,r=t.key,o=null===(n=M.current)||void 0===n?void 0:n.querySelector('[data-node-key="'.concat(ie(r),'"]'));o&&e.set(r,{width:o.offsetWidth,height:o.offsetHeight,left:o.offsetLeft,top:o.offsetTop})})),e}))};Object(l["useEffect"])((function(){Xe()}),[a.map((function(e){return e.key})).join("_")]);var Ze=ee((function(){var e=to(E),t=to(P),n=to(k);ne([e[0]-t[0]-n[0],e[1]-t[1]-n[1]]);var r=to(F);he(r);var o=to(N);ge(o);var a=to(M);de([a[0]-r[0],a[1]-r[1]]),Xe()})),Qe=a.slice(0,Ue),$e=a.slice(We+1),Je=[].concat(B(Qe),B($e)),et=Object(l["useState"])(),tt=S(et,2),nt=tt[0],rt=tt[1],ot=Ce.get(d),at=Object(l["useRef"])();function it(){V["a"].cancel(at.current)}Object(l["useEffect"])((function(){var e={};return ot&&(D?(f?e.right=ot.right:e.left=ot.left,e.width=ot.width):(e.top=ot.top,e.height=ot.height)),it(),at.current=Object(V["a"])((function(){rt(e)})),it}),[ot,D,f]),Object(l["useEffect"])((function(){Ge()}),[d,Re,Fe,oe(ot),oe(Ce),D]),Object(l["useEffect"])((function(){Ze()}),[f]);var ct,lt,ut,st,dt=!!Je.length,ft="".concat(o,"-nav-wrap");return D?f?(lt=A>0,ct=A!==Fe):(ct=A<0,lt=A!==Re):(ut=Y<0,st=Y!==Re),l["createElement"](K["a"],{onResize:Ze},l["createElement"]("div",{ref:Object(U["d"])(t,E),role:"tablist",className:h()("".concat(o,"-nav"),i),style:c,onKeyDown:function(){Be()}},l["createElement"](se,{ref:P,position:"left",extra:p,prefixCls:o}),l["createElement"]("div",{className:h()(ft,(n={},b(n,"".concat(ft,"-ping-left"),ct),b(n,"".concat(ft,"-ping-right"),lt),b(n,"".concat(ft,"-ping-top"),ut),b(n,"".concat(ft,"-ping-bottom"),st),n)),ref:T},l["createElement"](K["a"],{onResize:Ze},l["createElement"]("div",{ref:M,className:"".concat(o,"-nav-list"),style:{transform:"translate(".concat(A,"px, ").concat(Y,"px)"),transition:ze?"none":void 0}},Ye,l["createElement"](le,{ref:F,prefixCls:o,locale:y,editable:m,style:g(g({},0===Ye.length?void 0:qe),{},{visibility:dt?"hidden":null})}),l["createElement"]("div",{className:h()("".concat(o,"-ink-bar"),b({},"".concat(o,"-ink-bar-animated"),s.inkBar)),style:nt})))),l["createElement"]($r,v({},e,{removeAriaLabel:null===y||void 0===y?void 0:y.removeAriaLabel,ref:N,prefixCls:o,tabs:Je,className:!dt&&Ne,tabMoving:!!ze})),l["createElement"](se,{ref:k,position:"right",extra:p,prefixCls:o})))}var oo=l["forwardRef"](ro),ao=["renderTabBar"],io=["label","key"];function co(e){var t=e.renderTabBar,n=k(e,ao),r=l["useContext"](R),o=r.tabs;if(t){var a=g(g({},n),{},{panes:o.map((function(e){var t=e.label,n=e.key,r=k(e,io);return l["createElement"](D,v({tab:t,key:n,tabKey:n},r))}))});return t(a,oo)}return l["createElement"](oo,n)}function lo(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return e=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!1}:g({inkBar:!0},"object"===E(t)?t:{}),e.tabPaneMotion&&void 0===e.tabPane&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}var uo=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","moreIcon","moreTransitionName","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName"],so=0;function fo(e,t){var n,r=e.id,o=e.prefixCls,a=void 0===o?"rc-tabs":o,i=e.className,c=e.items,u=e.direction,s=e.activeKey,d=e.defaultActiveKey,f=e.editable,p=e.animated,m=e.tabPosition,y=void 0===m?"top":m,O=e.tabBarGutter,j=e.tabBarStyle,x=e.tabBarExtraContent,w=e.locale,C=e.moreIcon,P=e.moreTransitionName,N=e.destroyInactiveTabPane,F=e.renderTabBar,D=e.onChange,L=e.onTabClick,A=e.onTabScroll,z=e.getPopupContainer,H=e.popupClassName,B=k(e,uo),K=l["useMemo"]((function(){return(c||[]).filter((function(e){return e&&"object"===E(e)&&"key"in e}))}),[c]),_="rtl"===u,V=lo(p),U=Object(l["useState"])(!1),W=S(U,2),G=W[0],q=W[1];Object(l["useEffect"])((function(){q(Object(T["a"])())}),[]);var Y=Object(M["a"])((function(){var e;return null===(e=K[0])||void 0===e?void 0:e.key}),{value:s,defaultValue:d}),X=S(Y,2),Z=X[0],Q=X[1],$=Object(l["useState"])((function(){return K.findIndex((function(e){return e.key===Z}))})),J=S($,2),ee=J[0],te=J[1];Object(l["useEffect"])((function(){var e,t=K.findIndex((function(e){return e.key===Z}));-1===t&&(t=Math.max(0,Math.min(ee,K.length-1)),Q(null===(e=K[t])||void 0===e?void 0:e.key));te(t)}),[K.map((function(e){return e.key})).join("_"),Z,ee]);var ne=Object(M["a"])(null,{value:r}),re=S(ne,2),oe=re[0],ae=re[1];function ie(e,t){null===L||void 0===L||L(e,t);var n=e!==Z;Q(e),n&&(null===D||void 0===D||D(e))}Object(l["useEffect"])((function(){r||(ae("rc-tabs-".concat(so)),so+=1)}),[]);var ce,le={id:oe,activeKey:Z,animated:V,tabPosition:y,rtl:_,mobile:G},ue=g(g({},le),{},{editable:f,locale:w,moreIcon:C,moreTransitionName:P,tabBarGutter:O,onTabClick:ie,onTabScroll:A,extra:x,style:j,panes:null,getPopupContainer:z,popupClassName:H});return l["createElement"](R.Provider,{value:{tabs:K,prefixCls:a}},l["createElement"]("div",v({ref:t,id:r,className:h()(a,"".concat(a,"-").concat(y),(n={},b(n,"".concat(a,"-mobile"),G),b(n,"".concat(a,"-editable"),f),b(n,"".concat(a,"-rtl"),_),n),i)},B),ce,l["createElement"](co,v({},ue,{renderTabBar:F})),l["createElement"](I,v({destroyInactiveTabPane:N},le,{animated:V}))))}var po=l["forwardRef"](fo);var mo=po,ho=mo,vo=n("H84U"),bo=n("3Nzz"),yo=n("rTrx"),go=n("EXcs"),Oo={motionAppear:!1,motionEnter:!0,motionLeave:!0};function jo(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return t=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object(o["a"])({inkBar:!0},"object"===Object(yo["a"])(n)?n:{}),t.tabPane&&(t.tabPaneMotion=Object(o["a"])(Object(o["a"])({},Oo),{motionName:Object(go["c"])(e,"switch")})),t}var xo=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function wo(e){return e.filter((function(e){return e}))}function Co(e,t){if(e)return e;var n=Object(Cr["a"])(t).map((function(e){if(l["isValidElement"](e)){var t=e.key,n=e.props,r=n||{},a=r.tab,i=xo(r,["tab"]),c=Object(o["a"])(Object(o["a"])({key:String(t)},i),{label:a});return c}return null}));return wo(n)}var So=function(){return null};var Eo=So,Po=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function ko(e){var t,n=e.type,c=e.className,u=e.size,s=e.onEdit,d=e.hideAdd,f=e.centered,m=e.addIcon,v=e.children,b=e.items,y=e.animated,g=Po(e,["type","className","size","onEdit","hideAdd","centered","addIcon","children","items","animated"]),O=g.prefixCls,j=g.moreIcon,x=void 0===j?l["createElement"](i["a"],null):j,w=l["useContext"](vo["b"]),C=w.getPrefixCls,S=w.direction,E=w.getPopupContainer,P=C("tabs",O);"editable-card"===n&&(t={onEdit:function(e,t){var n=t.key,r=t.event;null===s||void 0===s||s("add"===e?r:n,e)},removeIcon:l["createElement"](a["a"],null),addIcon:m||l["createElement"](p,null),showAdd:!0!==d});var k=C(),T=Co(b,v),M=jo(P,y);return l["createElement"](bo["b"].Consumer,null,(function(e){var a,i=void 0!==u?u:e;return l["createElement"](ho,Object(o["a"])({direction:S,getPopupContainer:E,moreTransitionName:"".concat(k,"-slide-up")},g,{items:T,className:h()((a={},Object(r["a"])(a,"".concat(P,"-").concat(i),i),Object(r["a"])(a,"".concat(P,"-card"),["card","editable-card"].includes(n)),Object(r["a"])(a,"".concat(P,"-editable-card"),"editable-card"===n),Object(r["a"])(a,"".concat(P,"-centered"),f),a),c),editable:t,moreIcon:x,prefixCls:P,animated:M}))}))}ko.TabPane=Eo;t["a"]=ko},"Znn+":function(e,t,n){"use strict";n("EFp3"),n("9ama")},bTyn:function(e,t,n){"use strict";function r(e){if(Array.isArray(e))return e}function o(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=n["return"]&&(i=n["return"](),Object(i)!==i))return}finally{if(u)throw o}}return c}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t){if(e){if("string"===typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,t):void 0}}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){return r(e)||o(e,t)||i(e,t)||c()}var u=n("q1tI"),s=n("i8i4"),d=n("MNnm"),f=(n("Kwbf"),n("c+Xe")),p=u["createContext"](null),m=p;function h(e){if(Array.isArray(e))return a(e)}function v(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function b(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(e){return h(e)||v(e)||i(e)||b()}var g=n("TNol"),O=[];function j(e,t){var n=u["useState"]((function(){if(!Object(d["a"])())return null;var e=document.createElement("div");return e})),r=l(n,1),o=r[0],a=u["useRef"](!1),i=u["useContext"](m),c=u["useState"](O),s=l(c,2),f=s[0],p=s[1],h=i||(a.current?void 0:function(e){p((function(t){var n=[e].concat(y(t));return n}))});function v(){o.parentElement||document.body.appendChild(o),a.current=!0}function b(){var e;null===(e=o.parentElement)||void 0===e||e.removeChild(o),a.current=!1}return Object(g["a"])((function(){return e?i?i(v):v():b(),b}),[e]),Object(g["a"])((function(){f.length&&(f.forEach((function(e){return e()})),p(O))}),[f]),[o,h]}var x=n("BU3w"),w=n("qx4F");function C(){return document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight)&&window.innerWidth>document.body.offsetWidth}var S="rc-util-locker-".concat(Date.now()),E=0;function P(e){var t=!!e,n=u["useState"]((function(){return E+=1,"".concat(S,"_").concat(E)})),r=l(n,1),o=r[0];Object(g["a"])((function(){if(t){var e=Object(w["b"])(document.body).width,n=C();Object(x["b"])("\nhtml body {\n  overflow-y: hidden;\n  ".concat(n?"width: calc(100% - ".concat(e,"px);"):"","\n}"),o)}else Object(x["a"])(o);return function(){Object(x["a"])(o)}}),[t,o])}var k=!1;function T(e){return"boolean"===typeof e&&(k=e),k}var M=function(e){return!1!==e&&(Object(d["a"])()&&e?"string"===typeof e?document.querySelector(e):"function"===typeof e?e():e:null)},N=u["forwardRef"]((function(e,t){var n=e.open,r=e.autoLock,o=e.getContainer,a=e.debug,i=e.autoDestroy,c=void 0===i||i,p=e.children,h=u["useState"](n),v=l(h,2),b=v[0],y=v[1],g=b||n;u["useEffect"]((function(){(c||n)&&y(n)}),[n,c]);var O=u["useState"]((function(){return M(o)})),x=l(O,2),w=x[0],C=x[1];u["useEffect"]((function(){var e=M(o);C(null!==e&&void 0!==e?e:null)}));var S=j(g&&!w,a),E=l(S,2),k=E[0],N=E[1],R=null!==w&&void 0!==w?w:k;P(r&&n&&Object(d["a"])()&&(R===k||R===document.body));var F=null;if(p&&Object(f["c"])(p)&&t){var D=p;F=D.ref}var L=Object(f["d"])(F,t);if(!g||!Object(d["a"])()||void 0===w)return null;var I=!1===R||T(),A=p;return t&&(A=u["cloneElement"](p,{ref:L})),u["createElement"](m.Provider,{value:N},I?A:Object(s["createPortal"])(A,R))}));var R=N;t["a"]=R},bX4T:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n("vdM+"),o="accept acceptCharset accessKey action allowFullScreen allowTransparency\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\n    charSet checked classID className colSpan cols content contentEditable contextMenu\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\n    mediaGroup method min minLength multiple muted name noValidate nonce open\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\n    summary tabIndex target title type useMap value width wmode wrap",a="onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError",i="".concat(o," ").concat(a).split(/[\s\n]+/),c="aria-",l="data-";function u(e,t){return 0===e.indexOf(t)}function s(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=!1===n?{aria:!0,data:!0,attr:!0}:!0===n?{aria:!0}:Object(r["a"])({},n);var o={};return Object.keys(e).forEach((function(n){(t.aria&&("role"===n||u(n,c))||t.data&&u(n,l)||t.attr&&i.includes(n))&&(o[n]=e[n])})),o}},bbsP:function(e,t,n){"use strict";n("EFp3"),n("CWI+")},bogI:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return e?"function"===typeof e?e():e:null}},cWXX:function(e,t,n){"use strict";n("EFp3"),n("oIFs")},diRs:function(e,t,n){"use strict";var r=n("jiTG"),o=n("q1tI"),a=n("bogI"),i=n("EXcs"),c=n("H84U"),l=n("3S7+"),u=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},s=function(e){var t=e.title,n=e.content,r=e.prefixCls;return o["createElement"](o["Fragment"],null,t&&o["createElement"]("div",{className:"".concat(r,"-title")},Object(a["a"])(t)),o["createElement"]("div",{className:"".concat(r,"-inner-content")},Object(a["a"])(n)))},d=o["forwardRef"]((function(e,t){var n=e.prefixCls,a=e.title,d=e.content,f=e._overlay,p=e.placement,m=void 0===p?"top":p,h=e.trigger,v=void 0===h?"hover":h,b=e.mouseEnterDelay,y=void 0===b?.1:b,g=e.mouseLeaveDelay,O=void 0===g?.1:g,j=e.overlayStyle,x=void 0===j?{}:j,w=u(e,["prefixCls","title","content","_overlay","placement","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle"]),C=o["useContext"](c["b"]),S=C.getPrefixCls,E=S("popover",n),P=S(),k=o["useMemo"]((function(){return f||(a||d?o["createElement"](s,{prefixCls:E,title:a,content:d}):null)}),[f,a,d,E]);return o["createElement"](l["a"],Object(r["a"])({placement:m,trigger:v,mouseEnterDelay:y,mouseLeaveDelay:O,overlayStyle:x},w,{prefixCls:E,ref:t,overlay:k,transitionName:Object(i["c"])(P,"zoom-big",w.transitionName)}))}));t["a"]=d},gDlH:function(e,t,n){"use strict";var r=n("jiTG"),o=n("4IlW"),a=n("q1tI"),i=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},c={border:0,background:"transparent",padding:0,lineHeight:"inherit",display:"inline-block"},l=a["forwardRef"]((function(e,t){var n=function(e){var t=e.keyCode;t===o["a"].ENTER&&e.preventDefault()},l=function(t){var n=t.keyCode,r=e.onClick;n===o["a"].ENTER&&r&&r()},u=e.style,s=e.noStyle,d=e.disabled,f=i(e,["style","noStyle","disabled"]),p={};return s||(p=Object(r["a"])({},c)),d&&(p.pointerEvents="none"),p=Object(r["a"])(Object(r["a"])({},p),u),a["createElement"]("div",Object(r["a"])({role:"button",tabIndex:0,ref:t},f,{onKeyDown:n,onKeyUp:l,style:p}))}));t["a"]=l},hkKa:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n("Z97s"),o=n("q1tI");function a(){var e=o["useReducer"]((function(e){return e+1}),0),t=Object(r["a"])(e,2),n=t[1];return n}},"i//w":function(e,t,n){"use strict";function r(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}n.d(t,"a",(function(){return r}))},ifDB:function(e,t,n){},iqvk:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var r=n("tW8y");function o(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=n["return"]&&(i=n["return"](),Object(i)!==i))return}finally{if(u)throw o}}return c}}var a=n("7Dcv"),i=n("i//w");function c(e,t){return Object(r["a"])(e)||o(e,t)||Object(a["a"])(e,t)||Object(i["a"])()}},mQwV:function(e,t,n){},oIFs:function(e,t,n){},qx4F:function(e,t,n){"use strict";var r;function o(e){if("undefined"===typeof document)return 0;if(e||void 0===r){var t=document.createElement("div");t.style.width="100%",t.style.height="200px";var n=document.createElement("div"),o=n.style;o.position="absolute",o.top="0",o.left="0",o.pointerEvents="none",o.visibility="hidden",o.width="200px",o.height="150px",o.overflow="hidden",n.appendChild(t),document.body.appendChild(n);var a=t.offsetWidth;n.style.overflow="scroll";var i=t.offsetWidth;a===i&&(i=n.clientWidth),document.body.removeChild(n),r=a-i}return r}function a(e){var t=e.match(/^(.*)px$/),n=Number(null===t||void 0===t?void 0:t[1]);return Number.isNaN(n)?o():n}function i(e){if("undefined"===typeof document||!e||!(e instanceof Element))return{width:0,height:0};var t=getComputedStyle(e,"::-webkit-scrollbar"),n=t.width,r=t.height;return{width:a(n),height:a(r)}}n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return i}))},qzPX:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n("A3Fk");function o(e,t){if("object"!==Object(r["a"])(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==Object(r["a"])(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function a(e){var t=o(e,"string");return"symbol"===Object(r["a"])(t)?t:String(t)}},rsCp:function(e,t,n){},sqw5:function(e,t,n){"use strict";var r=n("2s+V"),o=n("q1tI"),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},i=a,c=n("/1Lp"),l=function(e,t){return o["createElement"](c["a"],Object(r["a"])(Object(r["a"])({},e),{},{ref:t,icon:i}))};l.displayName="DownOutlined";t["a"]=o["forwardRef"](l)},tW8y:function(e,t,n){"use strict";function r(e){if(Array.isArray(e))return e}n.d(t,"a",(function(){return r}))},"u/V1":function(e,t,n){},wE4A:function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,"a",(function(){return r}))},zP5H:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n("q1tI");function o(e){var t=r.createContext(null);function n(n){var o=e(n.initialState);return r.createElement(t.Provider,{value:o},n.children)}function o(){var e=r.useContext(t);if(null===e)throw new Error("Component must be wrapped with <Container.Provider>");return e}return{Provider:n,useContainer:o}}}}]);