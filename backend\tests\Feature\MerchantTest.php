<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Merchant;
use App\Models\MerchantOilPrice;
use App\Models\MerchantSettlement;
use App\Models\MerchantQualification;
use App\Models\MerchantAcceptance;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MerchantTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    /**
     * 测试获取商户列表
     */
    public function test_can_get_merchant_list()
    {
        $merchants = Merchant::factory()->count(3)->create([
            'market_manager_id' => $this->user->id
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/merchants');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'merchant_no',
                            'merchant_name',
                            'type',
                            'status'
                        ]
                    ],
                    'current_page',
                    'per_page',
                    'total'
                ]
            ]);
    }

    /**
     * 测试获取商户详情
     */
    public function test_can_get_merchant_detail()
    {
        $merchant = Merchant::factory()->create([
            'market_manager_id' => $this->user->id
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/merchants/{$merchant->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'id',
                    'merchant_no',
                    'merchant_name',
                    'type',
                    'status',
                    'user',
                    'qualification',
                    'settlement',
                    'acceptance'
                ]
            ]);
    }

    /**
     * 测试创建商户
     */
    public function test_can_create_merchant()
    {
        $merchantData = [
            'merchant' => [
                'merchant_name' => '测试商户',
                'type' => 1,
                'business_license' => '*********',
                'cooperation_mode' => 'prepaid',
                'region_id' => 1,
                'region_name' => '北京市',
                'address' => '北京市朝阳区xxx',
                'contact_person' => '张三',
                'contact_phone' => '***********',
                'market_manager_id' => $this->user->id
            ],
            'shop' => [
                [
                    'oil_type' => '92#',
                    'price' => 7.23
                ]
            ],
            'settlement' => [
                'settlement_type' => 'bank',
                'bank_name' => '中国银行',
                'bank_account' => '622202*********0123',
                'account_holder' => '张三',
                'bank_phone' => '***********'
            ],
            'qualification' => [
                'business_license_photo' => 'url1',
                'legal_person_photo' => 'url2',
                'id_card_front' => 'url3',
                'id_card_back' => 'url4'
            ]
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/merchants', $merchantData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'merchant_no',
                    'merchant_name',
                    'type',
                    'status',
                    'oil_prices',
                    'settlement',
                    'qualification'
                ]
            ]);

        $this->assertDatabaseHas('merchant', [
            'merchant_name' => '测试商户',
            'business_license' => '*********'
        ]);
    }

    /**
     * 测试取消审核
     */
    public function test_can_cancel_audit()
    {
        $merchant = Merchant::factory()->create([
            'market_manager_id' => $this->user->id,
            'status' => 1 // 待审核状态
        ]);

        $response = $this->actingAs($this->user)
            ->postJson("/api/merchants/{$merchant->id}/cancel-audit");

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => '取消审核成功'
            ]);

        $this->assertDatabaseHas('merchant', [
            'id' => $merchant->id,
            'status' => 0 // 待提交状态
        ]);
    }

    /**
     * 测试提交验收
     */
    public function test_can_submit_acceptance()
    {
        $merchant = Merchant::factory()->create([
            'market_manager_id' => $this->user->id,
            'status' => 3 // 待验收状态
        ]);

        $acceptanceData = [
            'store_photos' => ['url1', 'url2'],
            'oil_station_photos' => ['url3', 'url4'],
            'inspection_notes' => '验收通过',
            'inspection_result' => 1
        ];

        $response = $this->actingAs($this->user)
            ->postJson("/api/merchants/{$merchant->id}/acceptance", $acceptanceData);

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => '验收提交成功'
            ]);

        $this->assertDatabaseHas('merchant', [
            'id' => $merchant->id,
            'status' => 4 // 待接单状态
        ]);

        $this->assertDatabaseHas('merchant_acceptance', [
            'merchant_id' => $merchant->id,
            'inspection_result' => 1
        ]);
    }

    /**
     * 测试更新商户资料
     */
    public function test_can_update_merchant()
    {
        $merchant = Merchant::factory()->create([
            'market_manager_id' => $this->user->id,
            'status' => 0 // 待提交状态
        ]);

        $updateData = [
            'merchant' => [
                'merchant_name' => '更新后的商户名称',
                'type' => 1,
                'business_license' => '*********',
                'cooperation_mode' => 'prepaid',
                'region_id' => 1,
                'region_name' => '北京市',
                'address' => '北京市朝阳区xxx',
                'contact_person' => '张三',
                'contact_phone' => '***********',
                'market_manager_id' => $this->user->id
            ],
            'shop' => [
                [
                    'oil_type' => '92#',
                    'price' => 7.23
                ]
            ],
            'settlement' => [
                'settlement_type' => 'bank',
                'bank_name' => '中国银行',
                'bank_account' => '622202*********0123',
                'account_holder' => '张三',
                'bank_phone' => '***********'
            ],
            'qualification' => [
                'business_license_photo' => 'url1',
                'legal_person_photo' => 'url2',
                'id_card_front' => 'url3',
                'id_card_back' => 'url4'
            ]
        ];

        $response = $this->actingAs($this->user)
            ->putJson("/api/merchants/{$merchant->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'status' => true,
                'message' => '商户资料更新成功'
            ]);

        $this->assertDatabaseHas('merchant', [
            'id' => $merchant->id,
            'merchant_name' => '更新后的商户名称'
        ]);
    }

    /**
     * 测试OCR验证
     */
    public function test_can_check_ocr()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/merchants/ocr-check', [
                'ocr_type' => 1,
                'image_url' => 'http://example.com/image.jpg'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'business_license'
            ]);
    }
} 