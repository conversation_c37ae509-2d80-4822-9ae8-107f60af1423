<?php

namespace App\Models;

class AdminLog extends Model
{
    protected $table = 'admin_log';

    protected $dateFormat = 'U';

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    public function admin()
    {
        return $this->belongsTo('App\Models\Admin', 'admin_id', 'id');
    }

    public function scopeOfAdmin($query, $value)
    {
        return $query->where('admin_id', $value);
    }

    public function scopeOfAction($query, $value)
    {
        return $query->where('action', 'like', '%' . $value . '%');
    }

    public function scopeOfCreatedAt($query, $value)
    {
        if (is_array($value)) {
            return $query->where('created_at', '>=', strtotime($value[0]))->where('created_at', '<', strtotime($value[1]));
        } else {
            return $query->where('created_at', '>=', $value);
        }
    }
}
