(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[5],{L9NX:function(t,e,n){"use strict";n.r(e);var a=n("fWQN"),r=n("mtLc"),c=n("yKVA"),i=n("879j"),o=n("q1tI"),s=n.n(o),u=n("95SA"),l=n("7AqN"),d=n("9kvl"),h=n("s4NR"),p=function(t){Object(c["a"])(n,t);var e=Object(i["a"])(n);function n(){var t;Object(a["a"])(this,n);for(var r=arguments.length,c=new Array(r),i=0;i<r;i++)c[i]=arguments[i];return t=e.call.apply(e,[this].concat(c)),t.state={isReady:!1},t}return Object(r["a"])(n,[{key:"componentDidMount",value:function(){this.setState({isReady:!0});var t=this.props.dispatch;t&&t({type:"auth/fetchCurrent"})}},{key:"render",value:function(){var t=this.state.isReady,e=this.props,n=e.children,a=e.loading,r=e.currentUser,c=r&&r.id,i=Object(h["stringify"])({redirect:window.location.href});return!c&&a||!t?s.a.createElement(u["a"],null):c||"/login"===window.location.pathname?n:s.a.createElement(l["c"],{to:"/login?".concat(i)})}}]),n}(s.a.Component);e["default"]=Object(d["a"])((function(t){var e=t.auth,n=t.loading;return{currentUser:e.currentUser,loading:n.models.auth}}))(p)}}]);