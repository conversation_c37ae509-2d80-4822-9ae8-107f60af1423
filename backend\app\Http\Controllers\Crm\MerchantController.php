<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\Controller;
use App\Models\Merchant;
use App\Models\MerchantOilPrice;
use App\Models\MerchantAcceptance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Service\OCR\OCRService;
use App\Models\MerchantPlatform;
use OSS\OssClient;
use App\Models\Order;
use Carbon\Carbon;
use App\Models\BankList;

class MerchantController extends Controller
{
    /**
     * 获取商户列表（市场负责人）
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $model = Merchant::select('id', 'merchant_name', 'type', 'cooperation_mode', 'progress_status', 'audit_status', 'province_code', 'city_code', 'area_code', 'address', 'updated_at', 'online_at', 'market_manager_id', 'created_at')->with(['province', 'city', 'area', 'audits']);

        if ($request->has('progress_status')) {
            $model = $model->where('progress_status', $request->get('progress_status'));
        }

        if ($request->has('audit_status')) {
            $model = $model->where('audit_status', $request->get('audit_status'));
        }

        $merchants =  $model->where('market_manager_id', $user->id)->orderBy('created_at', 'desc')->paginate(10);

        foreach ($merchants as $k => $v) {
            // 始终使用 getRawOriginal 获取原始数据库值
            if ($v->progress_status != 4) {
                // 获取数据库中原始的 updated_at 时间戳
                $rawUpdatedAt = $v->getRawOriginal('updated_at');
                // 使用 createFromTimestamp 解析时间戳
                $days = Carbon::createFromTimestamp($rawUpdatedAt)->diffInDays(now());
                if ($days == 0) {
                    $minutes = Carbon::createFromTimestamp($rawUpdatedAt)->diffInMinutes(now());
                    if ($minutes == 0) {
                        $merchants[$k]->time_at = '刚刚更新';
                    } elseif ($minutes < 60) {
                        $merchants[$k]->time_at = $minutes . '分钟前更新';
                    } else {
                        $hours = floor($minutes / 60);
                        $merchants[$k]->time_at = $hours . '小时前更新';
                    }
                } else {
                    $merchants[$k]->time_at = $days . '天前更新';
                }
            } else {
                // 直接使用原始值进行日期解析，避免解析已经格式化的字符串
                $originalOnlineAt = $v->getRawOriginal('online_at');
                $online_at = Carbon::parse($originalOnlineAt)->format('Y-m-d');
                $merchants[$k]->time_at = $online_at . ' 上线';
            }
        }

        return ['status' => true, 'data' => $merchants];
    }

    /**
     * 获取商户详情（市场负责人）
     */
    public function detail(Request $request, $merchant)
    {
        $user = $request->user();
        $data = Merchant::with('marketManager', 'qualification', 'settlement.bank', 'acceptance', 'oilPrices')->find($merchant);
        if($data->cooperation_platform && count($data->cooperation_platform) > 0) {
            $data->platform = MerchantPlatform::select('id', 'name')->whereIn('id', $data->cooperation_platform)->get();
        }
        
        // 绩效目标：30日目标、实际30天交易额、上线天数

        $onlineDays = $data->progress_status == 4 ? now()->diffInDays($data->online_at) : 0;

        $performanceGoal = [
            'target_30day_transaction_amount' => $data->progress_status == 4 ? (float)$data->daily_sales_target : 0,
            'actual_30day_transaction_amount' => $data->progress_status == 4 ? $data->getDailyAvgAmount($onlineDays) : 0,
            'online_days' => $onlineDays
        ];

        $businessData = [
            'day' => $this->statics($data, 0),
            'week' => $this->statics($data, 1),
            'month' => $this->statics($data, 2),
        ];

        $data->performance_goal = $performanceGoal;
        $data->businessData = $businessData;

        return [
            'status' => true,
            'data' => $data,
        ];
    }

    public function statics($merchant, $type)
    {
        $result = [];

        switch ($type) {
            case 0: // 按小时统计今日数据
                $start = now()->startOfDay();
                $end = now()->endOfDay();

                $hourlyData = Order::where('store_id', $merchant->store_id)
                    ->whereBetween('created_at', [$start, $end])
                    ->selectRaw('HOUR(created_at) as hour, 
                                COUNT(*) as count, 
                                SUM(original_price) as amount')
                    ->groupBy('hour')
                    ->orderBy('hour')
                    ->get()
                    ->keyBy('hour');

                // 生成24小时完整数据
                for ($hour = 0; $hour < 24; $hour++) {
                    $result[] = [
                        'time' => str_pad($hour, 2, '0', STR_PAD_LEFT) . ':00',
                        'count' => $hourlyData[$hour]->count ?? 0,
                        'amount' => number_format($hourlyData[$hour]->amount ?? 0, 2, '.', '')
                    ];
                }
                break;

            case 1: // 本周数据（统计本周周一到周日）
                $start = now()->startOfWeek(Carbon::MONDAY);
                $end = now()->endOfWeek(Carbon::SUNDAY);

                $dailyData = Order::where('store_id', $merchant->store_id)
                    ->whereBetween('created_at', [$start, $end])
                    ->selectRaw('DATE(created_at) as date, 
                                COUNT(*) as count, 
                                SUM(original_price) as amount')
                    ->whereIn('status', ['payment', 'refunding', 'refund_failed', 'request_refund', 'refund_rejected'])
                    ->groupBy('date')
                    ->get()
                    ->keyBy(function($item) {
                        return Carbon::parse($item->date)->format('Y-m-d');
                    });

                // 生成完整日期序列
                $currentDay = $start->copy();
                while ($currentDay <= $end) {
                    $dateStr = $currentDay->format('l');
                    switch ($dateStr) {
                        case 'Monday': $dateStr = '星期一'; break;
                        case 'Tuesday': $dateStr = '星期二'; break;
                        case 'Wednesday': $dateStr = '星期三'; break;
                        case 'Thursday': $dateStr = '星期四'; break;
                        case 'Friday': $dateStr = '星期五'; break;
                        case 'Saturday': $dateStr = '星期六'; break;
                        case 'Sunday': $dateStr = '星期日'; break;
                    }
                    
                    $dateKey = $currentDay->format('Y-m-d');
                    $result[] = [
                        'time' => $dateStr,
                        'count' => (int)($dailyData[$dateKey]->count ?? 0),
                        'amount' => number_format($dailyData[$dateKey]->amount ?? 0, 2, '.', '')
                    ];
                    $currentDay->addDay();
                }
                break;

            case 2: // 本月数据（统计本月1号到当月最后一天）
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();

                $dailyData = Order::where('store_id', $merchant->store_id)
                    ->whereBetween('created_at', [$start, $end])
                    ->selectRaw('DATE(created_at) as date, 
                                COUNT(*) as count, 
                                SUM(original_price) as amount')
                    ->whereIn('status', ['payment', 'refunding', 'refund_failed', 'request_refund', 'refund_rejected'])
                    ->groupBy('date')
                    ->get()
                    ->keyBy(function($item) {
                        return Carbon::parse($item->date)->format('Y-m-d');
                    });

                // 生成完整日期序列
                $currentDay = $start->copy();
                while ($currentDay <= $end) {
                    $dateKey = $currentDay->format('Y-m-d');
                    $result[] = [
                        'time' => $currentDay->format('m-d'),
                        'count' => (int)($dailyData[$dateKey]->count ?? 0),
                        'amount' => number_format($dailyData[$dateKey]->amount ?? 0, 2, '.', '')
                    ];
                    $currentDay->addDay();
                }
                break;
        }

        return $result;
    }

    /**
     * 保存商户信息（新增或编辑）
     */
    public function saveMerchant(Request $request)
    {
        $user = $request->user();
        if(!$user) {
            return ['status' => false, 'message' => '用户未登录'];
        }

        if($user->is_verified == 0) {
            return ['status' => false, 'message' => '用户未认证'];
        }

        if($user->status == 0) {
            return ['status' => false, 'message' => '用户已禁用'];
        }

        $id = $request->input('id', 0);

        $merchant = $request->input('merchant', []);
        $merchant = array_filter($merchant, function($value) {
            return $value !== '' && $value !== null;
        });
        $shop = $request->input('shop', false);
        $settlement = $request->input('settlement', false);
        $qualification = $request->input('qualification', false);
        
        // 判断是新增还是编辑
        if ($id > 0) {
            $model = Merchant::find($id);
            if (!$model) {
                return ['status' => false, 'message' => '商户不存在'];
            }

            if($model->progress_status > 0) {
                return ['status' => false, 'message' => '该商户不是待审核状态，禁止修改'];
            }
        } else {
            $model = new Merchant();
            // 生成商户编号
            $merchantNo = 'M' . date('YmdHis') . rand(1000, 9999);
            $merchant['merchant_no'] = $merchantNo;
        }

        $merchant['market_manager_id'] = $user->id;
        $isDraft = $merchant['is_draft'] == 0 ? false : true;

        // 验证商户基本信息
        $validator = Validator::make($merchant, $model->rule($model), $model->message);
        
        if ($validator->fails()) {
            return ['status' => false, 'message' => $validator->errors()->all()[0]];
        }

        DB::beginTransaction();
        try {
            // 更新或创建商户
            if ($id > 0) {
                $model->update($merchant);
            } else {
                $model = $model->create($merchant);
            }

            // 保存油价
            if ($shop) {
                $model->updateRelations($model, 'oilPrices', $shop);
            }

            // 如果不是草稿，则保存其他信息
            if (!$isDraft) {
                // 保存结算信息
                if ($settlement) {
                    $model->updateRelations($model, 'settlement', $settlement);
                }
                
                // 保存资质
                if ($qualification) {
                    $model->updateRelations($model, 'qualification', $qualification);
                }
            }

            DB::commit();

            return [
                'status' => true, 
                'message' => $isDraft ? '草稿保存成功' : '商户信息保存成功'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return ['status' => false, 'message' => $e->getMessage()];
        }
    }

    // 获取单个商户
    public function getMerchant(Request $request)
    {
        $id = $request->get('id');

        $merchant = Merchant::with('qualification', 'settlement', 'acceptance', 'oilPrices')->find($id);

        return ['status' => true, 'data' => $merchant];
    }

    // 提交审核/验收
    public function submitMerchant(Request $request, $merchant)
    {
        $type = $request->get('type', 0);

        $merchantData = Merchant::with('qualification', 'settlement', 'oilPrices', 'acceptance')->find($merchant);

        if(!$merchantData) {
            return ['status' => false, 'message' => '该商户不存在'];
        }

        if($type == 0) {        
            // 确保 $merchant 是 Merchant 模型实例
            if (!is_object($merchant)) {
                $merchant = Merchant::find($merchant);
                if (!$merchant) {
                    return ['status' => false, 'message' => '指定的商户不存在'];
                }
            }

            $validator = Validator::make($merchantData->toArray(), $merchant->rule($merchant), $merchant->message);

            if ($validator->fails()) {
                return ['status' => false, 'message' => $validator->errors()->all()[0]];
            }

            // 验证油价
            $shopData = $merchantData->oilPrices;
            $shopData = array_filter($shopData->toArray(), function($value) {
                return $value['price'] > 0;
            });
            
            if (!$shopData || count($shopData) == 0) {
                return ['status' => false,'message' => '油价信息不能为空'];
            }
            
            // 验证资质
            $qualificationData = $merchantData->qualification;
            if (!$qualificationData) {
                return ['status' => false,'message' => '资质信息不能为空']; 
            }
            $validator = Validator::make($qualificationData->toArray(), $qualificationData->rule(), $qualificationData->message);
            if ($validator->fails()) {
                return ['status' => false, 'message' => $validator->errors()->all()[0]];
            }

            // 验证结算
            // $settlementData = $merchantData->settlement;
            // if (!$settlementData) {
            //     return ['status' => false,'message' => '结算信息不能为空']; 
            // }
            // $validator = Validator::make($settlementData->toArray(), $settlementData->rule(), $settlementData->message);
            // if ($validator->fails()) {
            //     return ['status' => false,'message' => $validator->errors()->all()[0]]; 
            // }
            $progress_status = 1;
        } else {
            $acceptance = $merchantData->acceptance;
            $acceptanceModel = MerchantAcceptance::find($acceptance->id);
            if(!$acceptanceModel) {
                return ['status' => false, 'message' => '请编辑验收资料'];
            }
            $validator = Validator::make($acceptance->toArray(), $acceptanceModel->rule($acceptanceModel), $acceptanceModel->message);

            if ($validator->fails()) {
                return ['status' => false, 'message' => $validator->errors()->all()[0]];
            }

            $progress_status = 3;
            $acceptanceModel->update(['is_draft' => 0]);
        }
        

        $merchantData->update(['progress_status' => $progress_status, 'is_draft' => 0, 'audit_status' => 0]);

        return ['status' => true, 'message' => $type == 0 ? '提交审核成功' : '提交验收成功'];

    }

    /**
     * 取消审核
     */
    public function cancelMerchant(Request $request, Merchant $merchant)
    {
        $type = $request->get('type', 0);
        // 检查商户状态是否为待审核
        if ($type == 0 && $merchant->progress_status !== 1) {
            return ['status' => false, 'message' => '只有待审核状态的商户才能取消审核'];
        }

        // 检查商户状态是否为待验收
        if ($type == 1 && $merchant->progress_status !== 3) {
            return ['status' => false, 'message' => '只有待验收状态的商户才能取消验收'];
        }

        // 更新商户状态为待提交
        $merchant->progress_status = $type == 0 ? 0 : 2;
        $merchant->save();

        return ['status' => true, 'message' => $type == 0 ? '取消审核成功' : '取消验收成功'];
    }

    /**
     * 保存验收
     */
    public function acceptance(Request $request, Merchant $merchant)
    {
        if($merchant->progress_status == 4) {
            return ['status' => false, 'message' => '该商户已上线，禁止修改'];
        }
        $acceptance = new MerchantAcceptance();
        $isDraft = $request->input('is_draft', 0);
        
        if($isDraft == 0) {
            // 验证请求数据
            $validator = Validator::make($request->all(), $acceptance->rule(), $acceptance->message);
            if ($validator->fails()) {
                return ['status' => false, 'message' => $validator->errors()->all()[0]];
            }
        }

        DB::beginTransaction();
        try {
            // 创建或更新验收记录
            $acceptance = MerchantAcceptance::updateOrCreate(
                ['merchant_id' => $merchant->id],
                $request->all()
            );

            DB::commit();

            return [
                'status' => true,
                'message' => $isDraft == 1 ? '草稿保存成功' : '验收提交成功',
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    // ocr验证
    public function ocrCheck(Request $request)
    {
        $user = $request->user();
        $params = $request->all();
        $data = null;
        $result = [
            'status' => true
        ];

        $file = $request->file('image_file');

        $bucket = config('filesystems.disks.oss.bucket');
        $endpoint = config('filesystems.disks.oss.endpoint');

        $ossClient = new OssClient(
            config('filesystems.disks.oss.key'),
            config('filesystems.disks.oss.secret'),
            $endpoint,
        );
        // 生成文件名
        $extension = $file->getClientOriginalExtension();
        $fileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
        $path = 'uploads/' . date('Ym') . '/' . $fileName;

        // 上传到OSS
        $ossClient->putObject(config('filesystems.disks.oss.bucket'), $path, file_get_contents($file->getPathname()));

        // 获取文件URL（不带有效期）
        $fileUrl = 'https://' . $bucket . '.' . $endpoint . '/' . $path;


        if (isset($params['ocr_type']) && isset($fileUrl)) {
            switch ($params['ocr_type']) {
                case 1:
                    $data = OCRService::Use('baidu')->getBusinessLicenseInfo($fileUrl);
                    break;
                default:
                    $data = null;
                    break;
            }
        }

        if (!$data) {
            return ['status' => false, 'message' => '无法识别营业执照，请重新上传'];
        }

        // 营业执照验证
        if (isset($data['code']) && $data['code']) {

            // 查询code 是否存在
            $business_license = Merchant::where('business_license', $data['code'])->first();

            if ($business_license) {
                return ['status' => false, 'message' => '当前商户已存在，请更换营业执照'];
            }

            // 删除OSS文件
            $ossClient->deleteObject($bucket, $path);
        }

        $result['business_license'] = $data['code'] ?? '';

        return $result;
    }

    public function getPlatform(Request $request)
    {
        $data = MerchantPlatform::get();

        return ['status' => true, 'data' => $data];
    }

    public function getBank(Request $request)
    {
        $model = BankList::query();

        if($request->has('name')) {
            $model = $model->where('bank_type', 'like', '%' . $request->get('name') . '%');
        }

        $data = $model->get();

        return ['status' => true, 'data' => $data];
    }
}