<?php

namespace App\Models;

use Illuminate\Validation\Rule;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\City;

class Province extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $connection = 'mysql_ysh';
    protected $table = 'provinces';

    public function children()
    {
        return $this->hasMany(City::class, 'ci_provinceid', 'pr_provinceid');
    }
}