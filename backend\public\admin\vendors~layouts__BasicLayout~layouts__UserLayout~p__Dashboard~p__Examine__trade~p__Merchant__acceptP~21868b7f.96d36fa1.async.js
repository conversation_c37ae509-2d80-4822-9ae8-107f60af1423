(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[0],{"7Tuj":function(e,r,t){"use strict";var n=t("bfL6").default;e.exports=function e(r,t){if(r===t)return!0;if(r&&t&&"object"==typeof r&&"object"==typeof t){if(r.constructor!==t.constructor)return!1;var o,i,a;if(Array.isArray(r)){if(o=r.length,o!=t.length)return!1;for(i=o;0!==i--;)if(!e(r[i],t[i]))return!1;return!0}if(r instanceof Map&&t instanceof Map){if(r.size!==t.size)return!1;var u,f=n(r.entries());try{for(f.s();!(u=f.n()).done;)if(i=u.value,!t.has(i[0]))return!1}catch(v){f.e(v)}finally{f.f()}var c,l=n(r.entries());try{for(l.s();!(c=l.n()).done;)if(i=c.value,!e(i[1],t.get(i[0])))return!1}catch(v){l.e(v)}finally{l.f()}return!0}if(r instanceof Set&&t instanceof Set){if(r.size!==t.size)return!1;var s,p=n(r.entries());try{for(p.s();!(s=p.n()).done;)if(i=s.value,!t.has(i[0]))return!1}catch(v){p.e(v)}finally{p.f()}return!0}if(ArrayBuffer.isView(r)&&ArrayBuffer.isView(t)){if(o=r.length,o!=t.length)return!1;for(i=o;0!==i--;)if(r[i]!==t[i])return!1;return!0}if(r.constructor===RegExp)return r.source===t.source&&r.flags===t.flags;if(r.valueOf!==Object.prototype.valueOf)return r.valueOf()===t.valueOf();if(r.toString!==Object.prototype.toString)return r.toString()===t.toString();if(a=Object.keys(r),o=a.length,o!==Object.keys(t).length)return!1;for(i=o;0!==i--;)if(!Object.prototype.hasOwnProperty.call(t,a[i]))return!1;for(i=o;0!==i--;){var y=a[i];if(("_owner"!==y||!r.$$typeof)&&!e(r[y],t[y]))return!1}return!0}return r!==r&&t!==t}},GOef:function(e,r,t){"use strict";t.d(r,"c",(function(){return Ie})),t.d(r,"a",(function(){return Ne})),t.d(r,"b",(function(){return Fe}));var n=t("7Tuj"),o=t.n(n);function i(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(e[t]!==r[t])return!1;return!0}function a(e,r){var t;void 0===r&&(r=i);var n,o=[],a=!1;function u(){for(var i=[],u=0;u<arguments.length;u++)i[u]=arguments[u];return a&&t===this&&r(i,o)||(n=e.apply(this,i),a=!0,t=this,o=i),n}return u}var u=a;function f(e){var r=[],t=0;while(t<e.length){var n=e[t];if("*"!==n&&"+"!==n&&"?"!==n)if("\\"!==n)if("{"!==n)if("}"!==n)if(":"!==n)if("("!==n)r.push({type:"CHAR",index:t,value:e[t++]});else{var o=1,i="";u=t+1;if("?"===e[u])throw new TypeError('Pattern cannot start with "?" at '+u);while(u<e.length)if("\\"!==e[u]){if(")"===e[u]){if(o--,0===o){u++;break}}else if("("===e[u]&&(o++,"?"!==e[u+1]))throw new TypeError("Capturing groups are not allowed at "+u);i+=e[u++]}else i+=e[u++]+e[u++];if(o)throw new TypeError("Unbalanced pattern at "+t);if(!i)throw new TypeError("Missing pattern at "+t);r.push({type:"PATTERN",index:t,value:i}),t=u}else{var a="",u=t+1;while(u<e.length){var f=e.charCodeAt(u);if(!(f>=48&&f<=57||f>=65&&f<=90||f>=97&&f<=122||95===f))break;a+=e[u++]}if(!a)throw new TypeError("Missing parameter name at "+t);r.push({type:"NAME",index:t,value:a}),t=u}else r.push({type:"CLOSE",index:t,value:e[t++]});else r.push({type:"OPEN",index:t,value:e[t++]});else r.push({type:"ESCAPED_CHAR",index:t++,value:e[t++]});else r.push({type:"MODIFIER",index:t,value:e[t++]})}return r.push({type:"END",index:t,value:""}),r}function c(e,r){void 0===r&&(r={});var t=f(e),n=r.prefixes,o=void 0===n?"./":n,i="[^"+l(r.delimiter||"/#?")+"]+?",a=[],u=0,c=0,s="",p=function(e){if(c<t.length&&t[c].type===e)return t[c++].value},y=function(e){var r=p(e);if(void 0!==r)return r;var n=t[c],o=n.type,i=n.index;throw new TypeError("Unexpected "+o+" at "+i+", expected "+e)},v=function(){var e,r="";while(e=p("CHAR")||p("ESCAPED_CHAR"))r+=e;return r};while(c<t.length){var d=p("CHAR"),h=p("NAME"),b=p("PATTERN");if(h||b){var m=d||"";-1===o.indexOf(m)&&(s+=m,m=""),s&&(a.push(s),s=""),a.push({name:h||u++,prefix:m,suffix:"",pattern:b||i,modifier:p("MODIFIER")||""})}else{var g=d||p("ESCAPED_CHAR");if(g)s+=g;else{s&&(a.push(s),s="");var O=p("OPEN");if(O){m=v();var w=p("NAME")||"",j=p("PATTERN")||"",A=v();y("CLOSE"),a.push({name:w||(j?u++:""),pattern:w&&!j?i:j,prefix:m,suffix:A,modifier:p("MODIFIER")||""})}else y("END")}}}return a}function l(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function p(e,r){if(!r)return e;var t=e.source.match(/\((?!\?)/g);if(t)for(var n=0;n<t.length;n++)r.push({name:n,prefix:"",suffix:"",modifier:"",pattern:""});return e}function y(e,r,t){var n=e.map((function(e){return h(e,r,t).source}));return new RegExp("(?:"+n.join("|")+")",s(t))}function v(e,r,t){return d(c(e,t),r,t)}function d(e,r,t){void 0===t&&(t={});for(var n=t.strict,o=void 0!==n&&n,i=t.start,a=void 0===i||i,u=t.end,f=void 0===u||u,c=t.encode,p=void 0===c?function(e){return e}:c,y="["+l(t.endsWith||"")+"]|$",v="["+l(t.delimiter||"/#?")+"]",d=a?"^":"",h=0,b=e;h<b.length;h++){var m=b[h];if("string"===typeof m)d+=l(p(m));else{var g=l(p(m.prefix)),O=l(p(m.suffix));if(m.pattern)if(r&&r.push(m),g||O)if("+"===m.modifier||"*"===m.modifier){var w="*"===m.modifier?"?":"";d+="(?:"+g+"((?:"+m.pattern+")(?:"+O+g+"(?:"+m.pattern+"))*)"+O+")"+w}else d+="(?:"+g+"("+m.pattern+")"+O+")"+m.modifier;else d+="("+m.pattern+")"+m.modifier;else d+="(?:"+g+O+")"+m.modifier}}if(f)o||(d+=v+"?"),d+=t.endsWith?"(?="+y+")":"$";else{var j=e[e.length-1],A="string"===typeof j?v.indexOf(j[j.length-1])>-1:void 0===j;o||(d+="(?:"+v+"(?="+y+"))?"),A||(d+="(?="+v+"|"+y+")")}return new RegExp(d,s(t))}function h(e,r,t){return e instanceof RegExp?p(e,r):Array.isArray(e)?y(e,r,t):v(e,r,t)}function b(e,r){return r>>>e|r<<32-e}function m(e,r,t){return e&r^~e&t}function g(e,r,t){return e&r^e&t^r&t}function O(e){return b(2,e)^b(13,e)^b(22,e)}function w(e){return b(6,e)^b(11,e)^b(25,e)}function j(e){return b(7,e)^b(18,e)^e>>>3}function A(e){return b(17,e)^b(19,e)^e>>>10}function E(e,r){return e[15&r]+=A(e[r+14&15])+e[r+9&15]+j(e[r+1&15])}var S,P,x,R=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],M="0123456789abcdef";function k(e,r){var t=(65535&e)+(65535&r),n=(e>>16)+(r>>16)+(t>>16);return n<<16|65535&t}function I(){S=new Array(8),P=new Array(2),x=new Array(64),P[0]=P[1]=0,S[0]=1779033703,S[1]=3144134277,S[2]=1013904242,S[3]=2773480762,S[4]=1359893119,S[5]=2600822924,S[6]=528734635,S[7]=1541459225}function T(){var e,r,t,n,o,i,a,u,f,c,l=new Array(16);e=S[0],r=S[1],t=S[2],n=S[3],o=S[4],i=S[5],a=S[6],u=S[7];for(var s=0;s<16;s++)l[s]=x[3+(s<<2)]|x[2+(s<<2)]<<8|x[1+(s<<2)]<<16|x[s<<2]<<24;for(var p=0;p<64;p++)f=u+w(o)+m(o,i,a)+R[p],f+=p<16?l[p]:E(l,p),c=O(e)+g(e,r,t),u=a,a=i,i=o,o=k(n,f),n=t,t=r,r=e,e=k(f,c);S[0]+=e,S[1]+=r,S[2]+=t,S[3]+=n,S[4]+=o,S[5]+=i,S[6]+=a,S[7]+=u}function _(e,r){var t,n,o=0;n=P[0]>>3&63;var i=63&r;for((P[0]+=r<<3)<r<<3&&P[1]++,P[1]+=r>>29,t=0;t+63<r;t+=64){for(var a=n;a<64;a++)x[a]=e.charCodeAt(o++);T(),n=0}for(var u=0;u<i;u++)x[u]=e.charCodeAt(o++)}function C(){var e=P[0]>>3&63;if(x[e++]=128,e<=56)for(var r=e;r<56;r++)x[r]=0;else{for(var t=e;t<64;t++)x[t]=0;T();for(var n=0;n<56;n++)x[n]=0}x[56]=P[1]>>>24&255,x[57]=P[1]>>>16&255,x[58]=P[1]>>>8&255,x[59]=255&P[1],x[60]=P[0]>>>24&255,x[61]=P[0]>>>16&255,x[62]=P[0]>>>8&255,x[63]=255&P[0],T()}function D(){for(var e=new String,r=0;r<8;r++)for(var t=28;t>=0;t-=4)e+=M.charAt(S[r]>>>t&15);return e}function N(e){return I(),_(e,e.length),C(),D()}var H=N;function $(e){return $="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$(e)}var F=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function K(e,r){return z(e)||U(e,r)||ae(e,r)||W()}function W(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function U(e,r){var t=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,o,i=[],a=!0,u=!1;try{for(t=t.call(e);!(a=(n=t.next()).done);a=!0)if(i.push(n.value),r&&i.length===r)break}catch(f){u=!0,o=f}finally{try{a||null==t["return"]||t["return"]()}finally{if(u)throw o}}return i}}function z(e){if(Array.isArray(e))return e}function B(e,r){var t="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=ae(e))||r&&e&&"number"===typeof e.length){t&&(e=t);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==t.return||t.return()}finally{if(u)throw i}}}}function J(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function L(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function G(e,r,t){return r&&L(e.prototype,r),t&&L(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function V(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),r&&te(e,r)}function Q(e){var r=ee();return function(){var t,n=ne(e);if(r){var o=ne(this).constructor;t=Reflect.construct(n,arguments,o)}else t=n.apply(this,arguments);return q(this,t)}}function q(e,r){if(r&&("object"===$(r)||"function"===typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return X(e)}function X(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Y(e){var r="function"===typeof Map?new Map:void 0;return Y=function(e){if(null===e||!re(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return Z(e,arguments,ne(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),te(t,e)},Y(e)}function Z(e,r,t){return Z=ee()?Reflect.construct.bind():function(e,r,t){var n=[null];n.push.apply(n,r);var o=Function.bind.apply(e,n),i=new o;return t&&te(i,t.prototype),i},Z.apply(null,arguments)}function ee(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function re(e){return-1!==Function.toString.call(e).indexOf("[native code]")}function te(e,r){return te=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,r){return e.__proto__=r,e},te(e,r)}function ne(e){return ne=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ne(e)}function oe(e){return fe(e)||ue(e)||ae(e)||ie()}function ie(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ae(e,r){if(e){if("string"===typeof e)return ce(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?ce(e,r):void 0}}function ue(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function fe(e){if(Array.isArray(e))return ce(e)}function ce(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function le(e,r){if(null==e)return{};var t,n,o=se(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],r.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(e,t)&&(o[t]=e[t])}return o}function se(e,r){if(null==e)return{};var t,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)t=i[n],r.indexOf(t)>=0||(o[t]=e[t]);return o}function pe(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function ye(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?pe(Object(t),!0).forEach((function(r){ve(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):pe(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function ve(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var de="routes";function he(e){return e.split("?")[0].split("#")[0]}var be=function(e){if(!e.startsWith("http"))return!1;try{var r=new URL(e);return!!r}catch(t){return!1}},me=function(e){var r=e.path;if(!r||"/"===r)try{return"/".concat(H(JSON.stringify(e)))}catch(t){}return r?he(r):r},ge=function(e,r){var t=e.name,n=e.locale;return!("locale"in e&&!1===n||!t)&&(e.locale||"".concat(r,".").concat(t))},Oe=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||r).startsWith("/")||be(e)?e:"/".concat(r,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},we=function(e,r){var t=e.menu,n=void 0===t?{}:t,o=e.indexRoute,i=e.path,a=void 0===i?"":i,u=e.children||e[de],f=n.name,c=void 0===f?e.name:f,l=n.icon,s=void 0===l?e.icon:l,p=n.hideChildren,y=void 0===p?e.hideChildren:p,v=n.flatMenu,d=void 0===v?e.flatMenu:v,h=o&&"redirect"!==Object.keys(o).join(",")?[ye({path:a,menu:n},o)].concat(u||[]):u,b=ye({},e);if(c&&(b.name=c),s&&(b.icon=s),h&&h.length){if(y)return delete b[de],delete b.children,b;var m=Ae(ye(ye({},r),{},{data:h}),e);if(d)return m;b[de]=m}return b},je=function(e){return Array.isArray(e)&&e.length>0};function Ae(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{path:"/"},t=e.data,n=e.formatMessage,o=e.parentName,i=e.locale;return t&&Array.isArray(t)?t.filter((function(e){return!!e&&(!!je(e[de])||(!!je(e.children)||(!!e.path||(!!e.originPath||(!!e.layout||(e.redirect||e.unaccessible,!1))))))})).filter((function(e){var r,t;return!!((null===e||void 0===e||null===(r=e.menu)||void 0===r?void 0:r.name)||(null===e||void 0===e?void 0:e.flatMenu)||(null===e||void 0===e||null===(t=e.menu)||void 0===t?void 0:t.flatMenu))||!1!==e.menu})).map((function(e){var r=ye({},e);return r.unaccessible&&delete r.name,"*"===r.path&&(r.path="."),"/*"===r.path&&(r.path="."),!r.path&&r.originPath&&(r.path=r.originPath),r})).map((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{path:"/"},a=t.children||t[de],u=Oe(t.path,r?r.path:"/"),f=t.name,c=ge(t,o||"menu"),l=!1!==c&&!1!==i&&n&&c?n({id:c,defaultMessage:f}):f,s=r.pro_layout_parentKeys,p=void 0===s?[]:s,y=(r.children,r.icon,r.flatMenu,r.indexRoute,r.routes,le(r,F)),v=new Set([].concat(oe(p),oe(t.parentKeys||[])));r.key&&v.add(r.key);var d=ye(ye(ye({},y),{},{menu:void 0},t),{},{path:u,locale:c,key:t.key||me(ye(ye({},t),{},{path:u})),pro_layout_parentKeys:Array.from(v).filter((function(e){return e&&"/"!==e}))});if(l?d.name=l:delete d.name,void 0===d.menu&&delete d.menu,je(a)){var h=Ae(ye(ye({},e),{},{data:a,parentName:c||""}),d);je(h)&&(d[de]=h,d.children=h)}return we(d,e)})).flat(1):[]}var Ee=u(Ae,o.a),Se=function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return r.filter((function(e){return e&&(e.name||je(e[de])||je(e.children))&&!e.hideInMenu&&!e.redirect})).map((function(r){var t=ye({},r),n=t.children||t[de];if(je(n)&&!t.hideChildrenInMenu&&n.some((function(e){return e&&!!e.name}))){var o,i=e(n);if(i.length)return ye(ye({},t),{},(o={},ve(o,de,i),ve(o,"children",i),o))}return ye(ye({},r),{},ve({},de,void 0))})).filter((function(e){return e}))},Pe=function(e){V(t,e);var r=Q(t);function t(){return J(this,t),r.apply(this,arguments)}return G(t,[{key:"get",value:function(e){var r;try{var t,n=B(this.entries());try{for(n.s();!(t=n.n()).done;){var o=K(t.value,2),i=o[0],a=o[1],u=he(i);if(!be(i)&&h(u,[]).test(e)){r=a;break}}}catch(f){n.e(f)}finally{n.f()}}catch(c){r=void 0}return r}}]),t}(Y(Map)),xe=function(e){var r=new Pe,t=function e(t,n){t.forEach((function(t){var o=t.children||t[de];je(o)&&e(o,t);var i=Oe(t.path,n?n.path:"/");r.set(he(i),t)}))};return t(e),r},Re=u(xe,o.a),Me=function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return r.map((function(r){var t=r.children||r[de];if(je(t)){var n=e(t);if(n.length)return ye(ye({},r),{},ve({},de,n))}var o=ye({},r);return delete o[de],delete o.children,o})).filter((function(e){return e}))},ke=function(e,r,t,n){var o=Ee({data:e,formatMessage:t,locale:r}),i=n?Me(o):Se(o),a=Re(o);return{breadcrumb:a,menuData:i}},Ie=ke;function Te(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function _e(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?Te(Object(t),!0).forEach((function(r){Ce(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Te(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function Ce(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var De=function e(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t={};return r.forEach((function(r){if(r&&r.key){var n=r.children||r[de];t[he(r.path||r.key||"/")]=_e({},r),t[r.key||r.path||"/"]=_e({},r),n&&(t=_e(_e({},t),e(n)))}})),t},Ne=De,He=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0;return e.filter((function(e){if("/"===e&&"/"===r)return!0;if("/"!==e&&"/*"!==e&&e&&!be(e)){var n=he(e);try{if(t&&h("".concat(n)).test(r))return!0;if(h("".concat(n),[]).test(r))return!0;if(h("".concat(n,"/(.*)")).test(r))return!0}catch(o){}}return!1})).sort((function(e,t){return e===r?10:t===r?-10:e.substr(1).split("/").length-t.substr(1).split("/").length}))},$e=function(e,r,t,n){var o=Ne(r),i=Object.keys(o),a=He(i,e||"/",n);return!a||a.length<1?[]:(t||(a=[a[a.length-1]]),a.map((function(e){var r=o[e]||{pro_layout_parentKeys:"",key:""},t=new Map,n=(r.pro_layout_parentKeys||[]).map((function(e){return t.has(e)?null:(t.set(e,!0),o[e])})).filter((function(e){return e}));return r.key&&n.push(r),n})).flat(1))},Fe=$e},Ict0:function(e,r,t){"use strict";function n(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}t.d(r,"a",(function(){return n}))},ORfw:function(e,r,t){"use strict";t.d(r,"a",(function(){return f}));var n=t("Ict0");function o(e){if(Array.isArray(e))return Object(n["a"])(e)}function i(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}var a=t("f/H8");function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e){return o(e)||i(e)||Object(a["a"])(e)||u()}},Qv07:function(e,r,t){"use strict";var n=t("kF4R"),o=t("ORfw"),i=t("GOef");function a(e){return Object(o["a"])(e).reduce((function(e,r){var t=Object(n["a"])(r,2),o=t[0],i=t[1];return e[o]=i,e}),{})}var u=function e(r,t,n,o){var u=Object(i["c"])(r,(null===t||void 0===t?void 0:t.locale)||!1,n,!0),f=u.menuData,c=u.breadcrumb;return o?e(o(f),t,n,void 0):{breadcrumb:a(c),breadcrumbMap:c,menuData:f}};r["a"]=u},"f/H8":function(e,r,t){"use strict";t.d(r,"a",(function(){return o}));var n=t("Ict0");function o(e,r){if(e){if("string"===typeof e)return Object(n["a"])(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Object(n["a"])(e,r):void 0}}},kF4R:function(e,r,t){"use strict";function n(e){if(Array.isArray(e))return e}function o(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,o,i,a,u=[],f=!0,c=!1;try{if(i=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;f=!1}else for(;!(f=(n=i.call(t)).done)&&(u.push(n.value),u.length!==r);f=!0);}catch(e){c=!0,o=e}finally{try{if(!f&&null!=t["return"]&&(a=t["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}t.d(r,"a",(function(){return u}));var i=t("f/H8");function a(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,r){return n(e)||o(e,r)||Object(i["a"])(e,r)||a()}},su3W:function(e,r,t){"use strict";t.d(r,"b",(function(){return u}));var n=t("ORfw"),o=t("vRGJ"),i=t.n(o),a=function(e,r,t){if(t){var o=Object(n["a"])(t.keys()).find((function(r){return i()(r).test(e)}));if(o)return t.get(o)}if(r){var a=Object.keys(r).find((function(r){return i()(r).test(e)}));if(a)return r[a]}return{path:""}},u=function(e,r){var t=e.pathname,n=void 0===t?"/":t,o=e.breadcrumb,i=e.breadcrumbMap,u=e.formatMessage,f=e.title,c=e.menu,l=void 0===c?{locale:!1}:c,s=r?"":f||"",p=a(n,o,i);if(!p)return{title:s,id:"",pageName:s};var y=p.name;return!1!==l.locale&&p.locale&&u&&(y=u({id:p.locale||"",defaultMessage:p.name})),y?r||!f?{title:y,id:p.locale||"",pageName:y}:{title:"".concat(y," - ").concat(f),id:p.locale||"",pageName:y}:{title:s,id:p.locale||"",pageName:s}},f=function(e,r){return u(e,r).title};r["a"]=f}}]);