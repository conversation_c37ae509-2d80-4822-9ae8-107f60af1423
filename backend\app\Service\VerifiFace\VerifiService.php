<?php

namespace App\Service\VerifiFace;

use App\Service\VerifiFace\Driver\Alipay;
use App\Service\VerifiFace\Driver\Wechat;

class VerifiService
{

    public static function driver($type)
    {
        switch ($type) {
            case 'alipay':
                return new Alipay(config('veriface.alipay'));
            case 'wechat':
                return new Wechat(config('veriface.wechat'));
            default:
                throw new VerifiException('不支持的验证平台', 500);
        }
    }
}
