<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Merchant;
use App\Models\MerchantQualification;
use App\Models\MerchantAcceptance;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HomeTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    /**
     * 测试获取首页数据
     */
    public function test_can_get_home_data()
    {
        // 创建不同状态的商户
        Merchant::factory()->count(2)->create([
            'market_manager_id' => $this->user->id,
            'status' => 0 // 待提交
        ]);

        Merchant::factory()->count(1)->create([
            'market_manager_id' => $this->user->id,
            'status' => 1 // 待审核
        ]);

        Merchant::factory()->count(3)->create([
            'market_manager_id' => $this->user->id,
            'status' => 4 // 待接单
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/home');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'core_data' => [
                        'pending_submission',
                        'pending_review',
                        'pending_acceptance',
                        'online',
                        'offline',
                        'rejected'
                    ],
                    'todo_reminders' => [
                        '*' => [
                            'type',
                            'count',
                            'items' => [
                                '*' => [
                                    'id',
                                    'merchant_name',
                                    'status',
                                    'issue'
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
    }

    /**
     * 测试获取待办提醒
     */
    public function test_can_get_todo_reminders()
    {
        // 创建需要提醒的商户
        Merchant::factory()->count(2)->create([
            'market_manager_id' => $this->user->id,
            'status' => 0, // 待提交
            'business_license' => null // 缺少营业执照
        ]);

        Merchant::factory()->count(1)->create([
            'market_manager_id' => $this->user->id,
            'status' => 3, // 待验收
            'store_photos' => null // 缺少门店照片
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/home/<USER>');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    '*' => [
                        'type',
                        'count',
                        'items' => [
                            '*' => [
                                'id',
                                'merchant_name',
                                'status',
                                'issue'
                            ]
                        ]
                    ]
                ]
            ]);
    }

    /**
     * 测试获取核心数据
     */
    public function test_can_get_core_data()
    {
        // 创建不同状态的商户
        Merchant::factory()->count(2)->create([
            'market_manager_id' => $this->user->id,
            'status' => 0 // 待提交
        ]);

        Merchant::factory()->count(1)->create([
            'market_manager_id' => $this->user->id,
            'status' => 1 // 待审核
        ]);

        Merchant::factory()->count(3)->create([
            'market_manager_id' => $this->user->id,
            'status' => 4 // 待接单
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/home/<USER>');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'status',
                'data' => [
                    'pending_submission',
                    'pending_review',
                    'pending_acceptance',
                    'online',
                    'offline',
                    'rejected'
                ]
            ]);
    }
} 