<?php

namespace App\Service\OCR\Devices;

use App\Service\OCR\OCRService;
use App\Service\OCR\OCRInterface;
use App\Service\OCR\OCRException;
use Illuminate\Support\Facades\Cache;
use GuzzleHttp\Client as HttpClient;

class <PERSON><PERSON> extends OCRService implements OCRInterface
{
    private $api_url = 'https://aip.baidubce.com';

    private $grant_type = 'client_credentials';

    private $client_id;

    private $client_secret;

    public function __construct()
    {
        $this->client_id = env('BAIDU_OCR_CLIENT_ID', '');
        $this->client_secret = env('BAIDU_OCR_CLIENT_SECRET', '');
    }

    /**
     * 是否为URL
     */
    private function isUrl($str)
    {
        $regex = '@(?i)\b((?:[a-z][\w-]+:(?:/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:\'".,<>?«»“”‘’]))@';

        return preg_match($regex, $str);
    }

    /**
     * 获取百度云AccessToken
     */
    public function getToken()
    {
        return Cache::get('baidu_cloud_token', function () {
            $token = '';
            Cache::lock('baidu_cloud_token')->get(function () use (&$token) {
                try {
                    $client = new HttpClient();
                    $respoonse = $client->request('POST', $this->api_url . '/oauth/2.0/token', [
                        'headers' => [
                            'Content-Type' => 'application/x-www-form-urlencoded',
                        ],
                        'form_params' => [
                            'grant_type' => $this->grant_type,
                            'client_id' => $this->client_id,
                            'client_secret' => $this->client_secret
                        ]
                    ]);
                    $res = json_decode($respoonse->getBody());
                    $token = $res->access_token;
                    Cache::put('baidu_cloud_token', $res->access_token, $res->expires_in - 864000);
                } catch (\Exception $e) {
                    throw new OCRException('获取Access Token失败', 500);
                }
            });
            return $token;
        });
    }

    /**
     * 请求接口
     */
    private function exec($command, $params)
    {
        $token = $this->getToken();
        $url = $this->api_url . '/rest/2.0/ocr/v1/' . $command . '?access_token=' . $token;

        try {
            $client = new HttpClient();
            $respoonse = $client->request('POST', $url, [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => $params
            ]);
            return json_decode($respoonse->getBody(), true);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage(), 500);
        }
    }

    /**
     * 识别身份证
     */
    public function getIdCardInfo($image)
    {
        $params = [
            'id_card_side' => 'front',
        ];
        if ($this->isUrl($image)) {
            $params['url'] = $image;
        } else {
            $params['image'] = $image;
        }

        $res = $this->exec('idcard', $params);

        if (isset($res['image_status']) && ($res['image_status'] === 'normal' || $res['image_status'] === 'reversed_side')) {
            $id_card_side = isset($res['words_result']['姓名']['words']) ? 'front' : 'back';
            return [
                'id_card_side' => $id_card_side,
                'name' => $res['words_result']['姓名']['words'] ?? '',
                'nation' => $res['words_result']['民族']['words'] ?? '',
                'address' => $res['words_result']['住址']['words'] ?? '',
                'number' => $res['words_result']['公民身份号码']['words'] ?? '',
                'birthday' => $res['words_result']['出生']['words'] ?? '',
                'sex' => $res['words_result']['性别']['words'] ?? '',
                'start_date' => $res['words_result']['签发日期']['words'] ?? '',
                'expire_date' => $res['words_result']['失效日期']['words'] ?? '',
                'issuing' => $res['words_result']['签发机关']['words'] ?? '',
            ];
        }
        
        return false;
    }

    
    /**
     * 识别营业执照
     */
    public function getBusinessLicenseInfo($image)
    {
        $params = [];
        if ($this->isUrl($image)) {
            $params['url'] = $image;
        } else {
            $params['image'] = $image;
        }

        $res = $this->exec('business_license', $params);

        if (isset($res['words_result_num']) && sizeof($res['words_result']) > 0) {
            return [
                'scope' => $res['words_result']['经营范围']['words'] ?? '',
                'formation' => $res['words_result']['组成形式']['words'] ?? '',
                'legal_name' => $res['words_result']['法人']['words'] ?? '',
                'number' => $res['words_result']['证件编号']['words'] ?? '',
                'reg_capital' => $res['words_result']['注册资本']['words'] ?? '',
                'name' => $res['words_result']['单位名称']['words'] ?? '',
                'expired_date' => $res['words_result']['有效期']['words'] ?? '',
                'code' => $res['words_result']['社会信用代码']['words'] ?? '',
                'paid_capital' => $res['words_result']['实收资本']['words'] ?? '',
                'check_date' => $res['words_result']['核准日期']['words'] ?? '',
                'start_date' => $res['words_result']['成立日期']['words'] ?? '',
                'tax_number' => $res['words_result']['税务登记号']['words'] ?? '',
                'address' => $res['words_result']['地址']['words'] ?? '',
                'reg_agency' => $res['words_result']['登记机关']['words'] ?? '',
                'type' => $res['words_result']['类型']['words'] ?? '',
            ];
        }

        return false;
    }

    /**
     * 识别银行卡
     */
    public function getBankCardInfo($image)
    {
        $params = [];
        if ($this->isUrl($image)) {
            $params['url'] = $image;
        } else {
            $params['image'] = $image;
        }

        $res = $this->exec('bankcard', $params);

        if (isset($res['result']) && !empty($res['result']['bank_card_number'])) {
            return [
                'valid_date' => $res['result']['valid_date'] ?? '',
                'bank_card_number' => $res['result']['bank_card_number'] ?? '',
                'bank_name' => $res['result']['bank_name'] ?? '',
                'bank_card_type' => $res['result']['bank_card_type'] ?? '',
                'holder_name' => $res['result']['holder_name'] ?? '',
            ];
        }

        return false;
    }
}
