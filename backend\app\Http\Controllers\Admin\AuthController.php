<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Models\Admin;
use App\Models\Province;
use App\Http\Resources\AreaCollection;

class AuthController extends Controller
{
    // 检测是否屏蔽
    private function checkBlock($ip)
    {
        $block_count = Cache::get('admin_login:' . $ip, 0);
        return $block_count >= 5 ? true : false;
    }

    // 失败计次
    private function addBlockCount($ip)
    {
        $block_count = Cache::get('admin_login:' . $ip, 0);
        Cache::put('admin_login:' . $ip, $block_count + 1, now()->addMinutes(30));
    }

    /**
     * 管理员登录
     */
    public function login(Request $request)
    {
        $login_ip = $request->ip();

        $is_block = $this->checkBlock($login_ip);
        if ($is_block) {
            return ['status' => false, 'message' => '失败次数过多，请稍后再试'];
        }

        $name = $request->input('name', '');
        $password = $request->input('password', '');

        $admin = Admin::where('name', $name)->first();
        if (!$admin) {
            $this->addBlockCount($login_ip);
            return ['status' => false, 'message' => '管理员账号不存在'];
        }
        if (!$admin->isAvailable()) {
            $this->addBlockCount($login_ip);
            return ['status' => false, 'message' => '管理员账号不可用'];
        }
        if (!Hash::check($password, $admin->password)) {
            $this->addBlockCount($login_ip);
            return ['status' => false, 'message' => '管理员账号密码错误'];
        }

        $admin->last_login_ip = $admin->login_ip;
        $admin->last_login_time = $admin->login_time ?? 0;
        $admin->login_ip = $request->ip();
        $admin->login_time = time();
        if (empty($admin->api_token)) {
            $admin->api_token = Hash::make(time() . $request->ip() . $admin->id . $admin->created_at);
        }

        $admin->save();

        return ['status' => true, 'data' => $admin, 'api_token' => $admin->api_token, 'amap_key' => env('AMAP_KEY')];
    }

    public function profile(Request $request)
    {
        return $user = Auth::guard('admin')->user();
    }

    public function editProfile(Request $request)
    {
        $user = Auth::guard('admin')->user();
        $user->name = $request->input('name', $user->name);
        $user->password = $request->input('password', '123456');
        $user->save();

        return ['status' => true, 'data' => $user, 'message' => '修改账号成功'];
    }

    public function areas(Request $request)
    {
        $data = Province::get();
        return new AreaCollection($data);
    }
}
