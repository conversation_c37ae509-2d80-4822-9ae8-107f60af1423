<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;
    
    /**
     * 加载筛选条件
     *
     * @param  Illuminate\Database\Eloquent\Builder  $builder   模型构造器
     * @param  array|string  $data                              筛选条件数组
     * @return Illuminate\Database\Eloquent\Model
     */
    protected function addScope(Builder $builder, array $data)
    {
        foreach ($data as $k => $v) {
            if ((is_array($v) && sizeof($v) > 0) || trim((string) $v) != '') {
                $scope = 'of' . Str::studly($k);
                $scope_func = 'scopeOf' . Str::studly($k);
                if (method_exists($builder->getModel(), $scope_func)) {
                    $builder = $builder->$scope($v);
                }
            }
        }

        return $builder;
    }
}
