<?php

namespace App\Utils;

use setasign\Fpdi\Tcpdf\Fpdi;

/**
 * PDF合同签章
 */
class SignatureStamp
{
    /**
     * 添加签章
     * 注意: 生成好的PDF合同另行保存
     * 
     * @param string $pdf_file_path PDF文件路径
     * @return string 生成的PDF文件路径
     */
    public function AddSignatureStamp($pdf_file_path)
    {
        if (!file_exists($pdf_file_path)) {
            throw new Exception("PDF文件不存在");
        }

        $pdf = new Fpdi();
        try {
            $pageCount = $pdf->setSourceFile($pdf_file_path);
            for ($i = 1; $i <= $pageCount; $i++) {
                $this->processPage($pdf, $i);
                if ($i == $pageCount) {
                    $this->addSignatureToPage($pdf, $i);
                }
            }
            $new_pdf = $this->savePdf($pdf);
            return $new_pdf;
        } catch (Exception $e) {
            throw new Exception("PDF处理错误: ".$e->getMessage());
        }
    }

    private function processPage(&$pdf, $pageNo) {
        $templateId = $pdf->importPage($pageNo);
        $size = $pdf->getTemplateSize($templateId);
        
        $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
        $pdf->useTemplate($templateId);
    }

    private function addSignatureToPage(&$pdf, $pageNo)
    {
        $templateId = $pdf->importPage($pageNo);
        $size = $pdf->getTemplateSize($templateId);

        $stamp_image = storage_path('static/seal.png');

        $stampWidth = 50;

        $x = 40;
        $y = $size['height'] - 75;
        
        $pdf->Image($stamp_image, $x, $y, $stampWidth, 0, 'PNG');
    }

    private function savePdf(&$pdf) {
        $file_name = date('YmdHis') .time() . rand(1000, 9999).'.pdf';
        $save_path = storage_path('static/tmp/contract' . $file_name);
        $pdf->Output($save_path, 'F');
        return $save_path;
    }
}
