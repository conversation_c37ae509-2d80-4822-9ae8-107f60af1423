<?php

namespace App\Models;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\Hash;

class Admin extends Authenticatable
{
    protected $table = 'admin';

    protected $dateFormat = 'U';

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'last_login_time' => 'datetime:Y-m-d H:i:s',
    ];

    protected $fillable = [
        'name',
        'password',
        'true_name',
        'status',
    ];

    protected $hidden = [
        'api_token',
        'password',
    ];

    public $message = [
        'true_name.required' => '管理员姓名不能为空',
        'true_name.unique' => '管理员姓名已存在',
        'name.required' => '管理用户名不能为空',
        'name.unique' => '管理用户名已存在',
        'password.required_without' => '管理密码不能为空',
    ];

    public function rule(Admin $model = null)
    {
        return [
            'true_name' => 'required|unique:admin' . ($model ? ',true_name,' . $model->id : ''),
            'name' => 'required|unique:admin' . ($model ? ',name,' . $model->id : ''),
            'password' => 'required_without:id',
        ];
    }

    public function isAvailable()
    {
        return $this->status == 1;
    }

    public function getLastLoginTimeAttribute($value)
    {
        return $value == 0 ? 0 : $this->asDateTime($value)->format('Y-m-d H:i:s');
    }

    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = Hash::make($value);
    }

    public function scopeOfName($query, $name)
    {
        return $query->where('name', 'like', '%' . $name . '%')->orWhere('true_name', 'like', '%' . $name . '%');
    }

    public function scopeOfStatus($query, $value)
    {
        return $query->where('status', $value);
    }
}
