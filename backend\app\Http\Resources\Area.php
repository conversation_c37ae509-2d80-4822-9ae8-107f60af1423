<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class Area extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        if($this->pr_id) {
            $data = [
                'label' => $this->pr_province,
                'value' => $this->pr_provinceid,
            ];
        }

        if($this->ci_id) {
            $data = [
                'label' => $this->ci_city,
                'value' => $this->ci_cityid,
            ];
        }

        if($this->ar_id) {
            $data = [
                'label' => $this->ar_area,
                'value' => $this->ar_areaid,
            ];
        }

        if ($this->children && sizeof($this->children) > 0) {
            $data['children'] = new AreaCollection($this->children);
        }
        

        return $data;
    }
}
