package com.alipay.mobile.android.verify.demo;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import com.alibaba.fastjson.JSON;
import com.alipay.mobile.android.verify.OCRType;
import com.alipay.mobile.android.verify.logger.Logger;
import com.alipay.mobile.android.verify.sdk.BizCode;
import com.alipay.mobile.android.verify.sdk.MPVerifyService;
import com.alipay.mobile.android.verify.sdk.ServiceFactory;
import com.alipay.mobile.android.verify.sdk.interfaces.ICallback;
import com.alipay.mobile.android.verify.sdk.interfaces.IService;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class MainActivity extends Activity implements View.OnClickListener {

    private String certifyId;
    private boolean waitForResult = false;

    private RadioGroup mLaunchBizRadioGroup;
    private EditText mCertifyIdText;
    private EditText mUrlText;
    private IService mService;

    private final static int REQUEST_PICK_ID_FRONT = 0x1;
    private final static int REQUEST_PICK_ID_BACK = 0x2;
    private final static int REQUEST_PICK_BANK = 0x3;

    private static final int REQUEST_PERMISSION_READ_PHONE_STATE = 0x05;

    private Button mButtonFace;
    private Button mButtonIDCardFront;
    private Button mButtonIDCardBack;
    private Button mButtonBankCard;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 初始化函数涉及获取设备信息，因此需要在隐私弹框之后调用，建议尽早初始化
        mService = ServiceFactory.create(this).build();

        setContentView(R.layout.activity_main);

        // 如果activity非single，通过schema跳转会启动一个新的activity，这个时候可以在oncreate中处理查询结果，注意要拿到发起请求的certifyid来请求查询
        queryCertifyResultIfNeed(getIntent());
    }


    @Override
    public void onContentChanged() {

        mCertifyIdText = findViewById(R.id.certifyId);
        mUrlText = findViewById(R.id.url);
        mLaunchBizRadioGroup = findViewById(R.id.radio_group_biz);
        mButtonFace = findViewById(R.id.btn_start_face);
        mButtonIDCardFront = findViewById(R.id.btn_idcard_front);
        mButtonIDCardBack = findViewById(R.id.btn_idcard_back);
        mButtonBankCard = findViewById(R.id.btn_bankcard);

        mButtonFace.setOnClickListener(this);
        mButtonIDCardFront.setOnClickListener(this);
        mButtonIDCardBack.setOnClickListener(this);
        mButtonBankCard.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if(v == mButtonFace) {
            startFaceVerify();
        } else if (v == mButtonIDCardFront) {
            Intent intent = new Intent();
            intent.setType("image/*");
            intent.setAction(Intent.ACTION_GET_CONTENT);
            startActivityForResult(Intent.createChooser(intent, "Select Picture"), REQUEST_PICK_ID_FRONT);
        } else if (v == mButtonIDCardBack) {
            Intent intent = new Intent();
            intent.setType("image/*");
            intent.setAction(Intent.ACTION_GET_CONTENT);
            startActivityForResult(Intent.createChooser(intent, "Select Picture"), REQUEST_PICK_ID_BACK);
        } else if (v == mButtonBankCard) {
            Intent intent = new Intent();
            intent.setType("image/*");
            intent.setAction(Intent.ACTION_GET_CONTENT);
            startActivityForResult(Intent.createChooser(intent, "Select Picture"), REQUEST_PICK_BANK);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        try {
            Uri currentUri = data.getData();
            Bitmap bitmap = MediaStore.Images.Media.getBitmap(this.getContentResolver(), currentUri);
            String certifyId = this.mCertifyIdText.getText().toString();

            ProgressDialog dialog = ProgressDialog.show(this, "请稍后", "识别中");

            ICallback callback = response -> {
                dialog.dismiss();
                String responseCode = response.get("resultStatus");
                if ("9001".equals(responseCode)) {
                    waitForResult = true;
                }
                Logger.i("认证数据："+ JSON.toJSONString(response));
                // 回调处理
                Toast.makeText(MainActivity.this, "调用者获得的数据: " +
                        JSON.toJSONString(response), Toast.LENGTH_SHORT).show();
            };

            Map<String, Object> options = new HashMap<>();
            options.put("certifyId",certifyId);
            options.put("bizCode", BizCode.Value.FACE_APP);
            options.put("image", bitmap);

            if (requestCode == REQUEST_PICK_ID_FRONT) {
                options.put("ocrType", OCRType.ID_CARD_FRONT);
            } else if (requestCode == REQUEST_PICK_ID_BACK) {
                options.put("ocrType", OCRType.ID_CARD_BACK);
            } else {
                options.put("ocrType", OCRType.BANK_CARD);
            }
            mService.startService(options, callback);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    private void startFaceVerify() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            int res = checkSelfPermission(android.Manifest.permission.READ_PHONE_STATE);
            if (res != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(new String[]{android.Manifest.permission.READ_PHONE_STATE}, REQUEST_PERMISSION_READ_PHONE_STATE);
            } else {
                realStartFaceVerify();
            }
        } else {
            realStartFaceVerify();
        }
    }

    private void realStartFaceVerify() {
        int bizId = mLaunchBizRadioGroup.getCheckedRadioButtonId();

        /**
         * 取值有3个，需要和 url 组合使用
         * {@link BizCode.Value.FACE_APP}           强制使用 App 进行扫脸
         * {@link BizCode.Value.FACE_ALIPAY_SDK}    强制使用支付宝 App 进行扫脸，需要确保支付宝 App 已经安装
         * {@link BizCode.Value.LIVINGNESS_SDK}     启动活体认证
         **/
        String bizCode;
        if (R.id.radio_face_alipay_sdk == bizId) {
            bizCode = BizCode.Value.FACE_ALIPAY_SDK;
        } else if (R.id.radio_face_app == bizId) {
            bizCode = BizCode.Value.FACE_APP;
        } else if (R.id.radio_livingness_sdk == bizId) {
            bizCode = BizCode.Value.LIVINGNESS_SDK;
        } else {
            throw new IllegalArgumentException("不存在的分支");
        }

        certifyId = mCertifyIdText.getText().toString();

        // 封装认证数据
        Map<String, Object> requestInfo = new HashMap<>();
        requestInfo.put("certifyId", certifyId);
        requestInfo.put("bizCode", bizCode);

        // 发起认证
        mService.startService(requestInfo, response -> {
            String responseCode = response.get("resultStatus");
            if ("9001".equals(responseCode)) {
                // 9001需要等待回调/回前台查询认证结果
                waitForResult = true;
            }
            Logger.i("认证数据："+ JSON.toJSONString(response));
            // 回调处理
            Toast.makeText(MainActivity.this, "调用者获得的数据: " +
                    JSON.toJSONString(response), Toast.LENGTH_SHORT).show();
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case REQUEST_PERMISSION_READ_PHONE_STATE: {
                if (grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(getApplicationContext(), "READ_PHONE_STATE Denied", Toast.LENGTH_SHORT)
                            .show();
                } else {
                    realStartFaceVerify();
                }
            }
        }
    }

    /**
     * 处理通过schema跳转过来的结果查询
     *
     * @param intent
     */
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        queryCertifyResultIfNeed(intent);
    }

    /**
     * 处理回前台触发结果查询
     */
    @Override
    protected void onResume() {
        super.onResume();
        if (waitForResult) {
            // 查询认证结果
            waitForResult = false;
            Toast.makeText(MainActivity.this, "业务查询认证结果, certifyId : " + certifyId, Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 业务方查询结果
     *
     * @param intent
     */
    protected void queryCertifyResultIfNeed(Intent intent) {
        if (intent == null) {
            return;
        }
        Uri data = intent.getData();
        if (data == null) {
            return;
        }
        // 如果有很多场景会通过schema调用到当前页面，建议在传给认证回跳的 schema 中，增加参数识别出需要查询认证结果的场景
        String param = data.getQueryParameter("queryResult");
        if ("true".equals(param)) {
            // 查询认证结果
            waitForResult = false;// 防止走到 onResume 中再次查询结果
            Toast.makeText(MainActivity.this, "结果页调期，业务查询认证结果, certifyId : " + certifyId, Toast.LENGTH_SHORT).show();
        }
    }
}
