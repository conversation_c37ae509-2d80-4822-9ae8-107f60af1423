<?php

namespace App\Service\VerifiFace\Driver;

use App\Service\VerifiFace\VerifiInterface;
use Cache;
use Log;
use GuzzleHttp\Client as HttpClient;
use GuzzleHttp\Exception\RequestException;

class Wechat implements VerifiInterface
{
    private $appId;
    private $mchId;
    private $apiKey;
    private $certPath;
    private $keyPath;
    private $gateway = 'https://api.mch.weixin.qq.com/secapi/pay/facepay';

    public function __construct($config)
    {
        $this->appId = $config['app_id'];
        $this->mchId = $config['mch_id'];
        $this->apiKey = $config['api_key'];
        $this->certPath = $config['cert_path'];
        $this->keyPath = $config['key_path'];
    }

    public function getInfo($params)
    {
        try {
            $data = [
                'appid' => $this->appId,
                'mch_id' => $this->mchId,
                'nonce_str' => $this->generateNonce(),
                'sign_type' => 'MD5',
                'real_name' => $params['name'],
                'idcard_number' => $params['id_card'],
                'auth_id' => $params['auth_id'],
                'face_image' => base64_encode($params['face_image'])
            ];

            $data['sign'] = $this->generateSign($data);

            $client = new HttpClient([
                'cert' => $this->certPath,
                'ssl_key' => $this->keyPath
            ]);

            $response = $client->post($this->gateway, [
                'form_params' => $data
            ]);

            $result = json_decode(json_encode(simplexml_load_string($response->getBody())), true);

            if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
                return [
                    'status' => true,
                    'message' => '认证成功',
                    'data' => $result
                ];
            }

            return [
                'status' => false,
                'message' => $result['err_code_des'] ?? '认证失败'
            ];

        } catch (\Exception $e) {
            Log::error('微信扫脸认证异常: '.$e->getMessage());
            return ['status' => false, 'message' => '系统繁忙请稍后再试'];
        }
    }

    private function generateNonce()
    {
        return md5(uniqid());
    }

    private function generateSign($data)
    {
        ksort($data);
        $string = '';
        foreach ($data as $k => $v) {
            if ($v !== '' && $k !== 'sign') {
                $string .= $k . '=' . $v . '&';
            }
        }
        $string .= 'key=' . $this->apiKey;
        return strtoupper(md5($string));
    }
}