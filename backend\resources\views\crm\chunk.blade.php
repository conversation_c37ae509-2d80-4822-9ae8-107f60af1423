<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试分块上传接口</title>
</head>
<body>
    <input type="file" id="fileInput">
    <button onclick="uploadFile()">上传文件</button>
    <div id="status"></div>

    <script>
        const CHUNK_SIZE = 20 * 1024 * 1024; // 每个分块大小为 20MB
        const UPLOAD_CHUNK_URL = '/api/crm/upload/chunk'; // 替换为实际的分块上传接口 URL
        const MERGE_CHUNKS_URL = '/api/crm/upload/merge'; // 替换为实际的合并分块接口 URL
        const TOKEN = '$2y$10$SS9pxbrl5ElPfP2/fH3NZuw.WSIiw3PDlq/.ntMZDnDP0yl7iVlTS'; // 替换为实际的 Token

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            if (!file) {
                alert('请选择一个文件');
                return;
            }

            const uploadId = generateUploadId();
            const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
            const statusDiv = document.getElementById('status');

            statusDiv.textContent = '开始上传文件...';

            try {
                // 上传所有分块
                for (let i = 0; i < totalChunks; i++) {
                    const start = i * CHUNK_SIZE;
                    const end = Math.min(file.size, start + CHUNK_SIZE);
                    const chunk = file.slice(start, end);
                    const chunkData = await readFileAsBase64(chunk);

                    const formData = new FormData();
                    formData.append('uploadId', uploadId);
                    formData.append('chunkIndex', i);
                    formData.append('totalchunks', totalChunks);
                    formData.append('chunkData', chunkData);

                    const response = await fetch(UPLOAD_CHUNK_URL, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'Authorization': `Bearer ${TOKEN}`
                        }
                    });

                    const result = await response.json();
                    if (!result.status) {
                        throw new Error(result.message);
                    }

                    statusDiv.textContent = `已上传 ${i + 1}/${totalChunks} 个分块`;
                }

                // 合并分块
                const mergeFormData = new FormData();
                mergeFormData.append('uploadId', uploadId);
                mergeFormData.append('totalChunks', totalChunks);
                mergeFormData.append('fileIdentifier', uploadId);
                mergeFormData.append('fileName', file.name);
                mergeFormData.append('mimeType', file.type);

                const mergeResponse = await fetch(MERGE_CHUNKS_URL, {
                    method: 'POST',
                    body: mergeFormData,
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`
                    }
                });

                const mergeResult = await mergeResponse.json();
                if (mergeResult.status) {
                    statusDiv.textContent = '文件上传并合并成功';
                } else {
                    throw new Error(mergeResult.message);
                }
            } catch (error) {
                statusDiv.textContent = `上传失败: ${error.message}`;
            }
        }

        function generateUploadId() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        function readFileAsBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result.split(',')[1]);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }
    </script>
</body>
</html>