<?php

namespace App\Models;

class MerchantPlatform extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'merchant_platform';

    protected $dateFormat = 'U';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
    ];

    public $message = [
        'name.required' => '平台名称不能为空',
    ];

    /**
     * 获取验证规则
     */
    public function rule(MerchantPlatform $model = null)
    {
        $rules = [
            'name' => 'required',
        ];

        return $rules;
    }

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [];

    public function getCreatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getUpdatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }
    
} 