<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\SystemController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\MerchantController;
use App\Http\Controllers\Admin\StoreController;
use App\Http\Controllers\Admin\ExamineController;

Route::post('login', [AuthController::class, 'login']);

Route::middleware(['auth:admin'])->group(function () {
    // 账号设置
    Route::get('profile', [AuthController::class, 'profile']);
    Route::put('profile', [AuthController::class, 'editProfile']);
    Route::get('areas', [AuthController::class, 'areas']);

    // 管理员管理
    Route::get('system/user', [SystemController::class, 'user']);
    Route::post('system/user', [SystemController::class, 'addUser']);
    Route::put('system/user/{admin}', [SystemController::class, 'editUser'])->where('admin', '[0-9]+');
    Route::put('system/user/{admin}/status', [SystemController::class, 'editUserStatus'])->where('admin', '[0-9]+');
    Route::delete('system/user', [SystemController::class, 'deleteUser']);
    Route::get('system/log', [SystemController::class, 'log']);

    // 用户管理
    Route::get('user', [UserController::class, 'user']);
    Route::post('user', [UserController::class, 'addUser']);
    Route::put('user/{user}', [UserController::class, 'editUser'])->where('user', '[0-9]+');
    Route::put('user/{user}/status', [UserController::class, 'editStatus'])->where('user', '[0-9]+');

    // 商户管理
    Route::get('merchant', [MerchantController::class, 'index']);
    Route::get('merchant/{merchant}', [MerchantController::class,'detail'])->where('merchant', '[0-9]+');
    Route::get('merchant/export', [MerchantController::class,'export']);
    Route::put('merchant/{merchant}/progress/status', [MerchantController::class,'editProgressStatus'])->where('merchant', '[0-9]+');

    // 合作平台管理
    Route::get('merchant/platform', [MerchantController::class, 'platform']);
    Route::post('merchant/platform', [MerchantController::class, 'addPlatform']);
    Route::put('merchant/platform/{platform}', [MerchantController::class, 'editPlatform']);
    Route::delete('merchant/platform', [MerchantController::class, 'deletePlatform']);

    // 店铺管理
    Route::get('store', [StoreController::class, 'index']);

    // 考核管理
    Route::get('examine', [ExamineController::class, 'index']);
    Route::post('examine/{merchant}', [ExamineController::class,'updateExamine'])->where('merchant', '[0-9]+');
});
