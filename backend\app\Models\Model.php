<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model as BaseModel;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Model extends BaseModel
{
    public function updateRelations($model, $relation, $data = [])
    {
        $relationClass = $model->$relation();
        $relationClsName = $model->$relation()->getRelated();

        if ($relationClass instanceof HasOne) {
            // 一对一关系
            $relationModel = $model->$relation;
            if ($relationModel) {
                if (sizeof($data) > 0) {
                    // 更新
                    $relationModel->fill($data);
                    $relationModel->save();
                } else {
                    // 删除
                    $relationModel->delete();
                }
            } else {
                if (sizeof($data) > 0) {
                    // 新增
                    $relationClass->create($data);
                }
            }
        } else if ($relationClass instanceof HasMany) {
            // 一对多关系
            $saveData = [];
            $updateData = [];
            $deleteData = [];

            foreach($data as $v) {
                if (isset($v['id'])) {
                    $updateData[] = $v;
                } else {
                    $saveData[] = $v;
                }
            }

            foreach($model->$relation as $v) {
                $hasCount = collect($data)->where('id', $v['id']);
                if (sizeof($hasCount) == 0) {
                    $deleteData[] = $v['id'];
                }
            }

            // 添加
            if (sizeof($saveData) > 0) {
                $model->$relation()->createMany($saveData);
            }

            // 更新
            foreach ($updateData as $v) {
                $relationModel = $model->$relation->where('id', $v['id'])->first();
                if ($relationModel) {
                    $relationModel->fill($v);
                    $relationModel->save();
                }
            }

            // 删除
            if (sizeof($deleteData) > 0) {
                $relationModel = new $relationClsName();
                $relationModel->destroy($deleteData);
            }
        }
    }
}
