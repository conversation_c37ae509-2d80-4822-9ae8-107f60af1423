<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;
use <PERSON>vel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, Notifiable;

    protected $table = 'user';

    protected $dateFormat = 'U';

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'last_login_time' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'username',
        'password',
        'name',
        'phone',
        'status',
        'api_token',
        'login_ip',
        'login_time',
        'last_login_ip',
        'last_login_time',
        'id_card',
        'is_verified',
        'outer_order_no',
        'certify_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    public $message = [
        'username.required' => '用户名不能为空',
        'username.unique' => '用户名已存在',
        'id_card.unique' => '该身份证号已存在',
        'phone.regex' => '手机号无效'
    ];

    /**
     * 获取验证规则
     */
    public function rule(User $model = null)
    {
        return [
            'username' => 'required|unique:user' . ($model ? ',username,' . $model->id : ''),
            'id_card' => 'unique:user' . ($model ? ',id_card,' . $model->id : ''),
            'phone' => 'regex:/^1[3-9]\d{9}$/'
        ];
    }

    public function getCreatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getUpdatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getLastLoginTimeAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', $value);
    }

    public function setLastLoginTimeAttribute($value)
    {
        $this->attributes['last_login_time'] = strtotime($value);
    }

    public function getLoginTimeAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', $value);
    }

    /**
     * 创建密码
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = Hash::make($value);
    }

    public function scopeOfName($query, $value)
    {
        return $query->where('name', 'like', '%' . $value . '%');
    }

    public function scopeOfPhone($query, $value)
    {
        return $query->where('phone', 'like', '%' . $value . '%');
    }
    
    public function scopeOfVerified($query, $value)
    {
        return $query->where('is_verified', $value);
    }

    /**
     * 获取负责的商户列表
     */
    public function managedMerchants()
    {
        return $this->hasMany(Merchant::class, 'market_manager_id');
    }
}
