/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : ysh_crm

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 04/04/2025 10:56:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
  `true_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '管理员姓名',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '本次登录IP',
  `login_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '本次登录时间',
  `last_login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '上次登录IP',
  `last_login_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '上次登录时间',
  `api_token` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登录Token',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态 0:禁用 1:启用',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0,
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 156 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES (1, 'shion', '$2y$10$qS0r5bvp1JHvsV1WiPr.r.kLpUVLQiuw9Dutbohwp4caPtqVtd6WS', '周正纯', '**********', 1743648840, '************', 1743508710, '$2y$10$wPJ2D0MocgEw5z.FMgv5COfGz6ev/C8ll/5wRmbBJndgkY5h.wwWG', 1, 1589010568, 1743649028);
INSERT INTO `admin` VALUES (154, 'test', '$2y$10$wzXARv7Ly4m8BK3V6jYiYuJ5vdQXM2f2EqhNsjUSK9MFPxv2.X7q6', 'test', '', 0, '', 0, '', 0, 1741710051, 1743650049);
INSERT INTO `admin` VALUES (155, 'aaabbb', '$2y$10$7kKCqDDilYzoQL0uMsI9A.8KAJBmpTg5MlyuF0IacsoDJZQI3/psy', '测试用户', '', 0, '', 0, '', 0, 1743650072, 1743650089);

-- ----------------------------
-- Table structure for admin_log
-- ----------------------------
DROP TABLE IF EXISTS `admin_log`;
CREATE TABLE `admin_log`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `action` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作',
  `data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '操作内容',
  `ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作IP',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0,
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `action`(`action`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin_log
-- ----------------------------

-- ----------------------------
-- Table structure for failed_jobs
-- ----------------------------
DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `failed_jobs_uuid_unique`(`uuid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of failed_jobs
-- ----------------------------

-- ----------------------------
-- Table structure for merchant
-- ----------------------------
DROP TABLE IF EXISTS `merchant`;
CREATE TABLE `merchant`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商户ID',
  `merchant_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商户编号',
  `merchant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商户名称',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户类型：0-加油站，1-充电桩，2-综合能源站',
  `business_license` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '营业执照号',
  `cooperation_mode` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '合作模式：0-预储值，1-t1次日结算',
  `cooperation_platform` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合作平台',
  `province_code` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '省',
  `city_code` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '市',
  `area_code` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '区',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `latitude` decimal(10, 6) UNSIGNED NOT NULL DEFAULT 0.000000 COMMENT '纬度',
  `longitude` decimal(10, 6) UNSIGNED NOT NULL DEFAULT 0.000000 COMMENT '经度',
  `store_photos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '店铺照片',
  `daily_sales_target` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '30天日均交易额目标',
  `balance` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '储值金额',
  `gift_amount` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '赠送金额',
  `platform_discount` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '平台优惠金额',
  `contact_person` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '负责人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `market_manager_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '市场负责人id',
  `is_draft` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为草稿: 0否， 1是',
  `contract` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同地址',
  `is_confirm` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否确认真实性: 0否，1是',
  `store_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联油站id',
  `progress_status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '流程状态 0:待审核 1:审核中 2:待验收 3:验收中 4:已上线',
  `audit_status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态 0:缺省 1:通过 2:不通过',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_merchant_no`(`merchant_no`) USING BTREE,
  UNIQUE INDEX `uk_business_license`(`business_license`) USING BTREE,
  INDEX `idx_merchant_type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of merchant
-- ----------------------------

-- ----------------------------
-- Table structure for merchant_acceptance
-- ----------------------------
DROP TABLE IF EXISTS `merchant_acceptance`;
CREATE TABLE `merchant_acceptance`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '验收ID',
  `merchant_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `has_group` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否建群：0-否，1-是',
  `group_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '群名称',
  `has_device` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '设备是否绑定：0-否，1-是',
  `device_binding_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '设备绑定时间',
  `device_binding_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设备绑定位置',
  `device_binding_latitude` decimal(10, 6) UNSIGNED NOT NULL DEFAULT 0.000000 COMMENT '设备绑定纬度',
  `device_binding_longitude` decimal(10, 6) UNSIGNED NOT NULL DEFAULT 0.000000 COMMENT '设备绑定经度',
  `device_photos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设备照片',
  `has_merchant_account` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家账号是否创建：0-否，1-是',
  `has_merchant_app` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '商家App是否下载：0-否，1-是',
  `has_training` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否对商家开展培训：0-否，1-是',
  `training_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '培训时间',
  `training_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '培训地点',
  `training_latitude` decimal(10, 6) UNSIGNED NOT NULL DEFAULT 0.000000 COMMENT '培训地点纬度',
  `training_longitude` decimal(10, 6) UNSIGNED NOT NULL DEFAULT 0.000000 COMMENT '培训地点经度',
  `training_videos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '培训视频',
  `training_photos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '培训照片',
  `is_draft` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为草稿: 0否，1是',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `is_confirm` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否确认：0否，1是',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_merchant_id`(`merchant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商户验收表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of merchant_acceptance
-- ----------------------------

-- ----------------------------
-- Table structure for merchant_audit
-- ----------------------------
DROP TABLE IF EXISTS `merchant_audit`;
CREATE TABLE `merchant_audit`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审核ID',
  `merchant_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核类型：0-入驻审核，1-验收审核',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核状态：0-待审核，1-已通过，2-未通过',
  `reviewer_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核人ID',
  `comment` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审核意见',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_merchant_id`(`merchant_id`) USING BTREE,
  INDEX `idx_audit_type`(`type`) USING BTREE,
  INDEX `idx_audit_status`(`status`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商户审核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of merchant_audit
-- ----------------------------

-- ----------------------------
-- Table structure for merchant_oil_price
-- ----------------------------
DROP TABLE IF EXISTS `merchant_oil_price`;
CREATE TABLE `merchant_oil_price`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `merchant_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `oil_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '油品类型：0-92号汽油, 1-95号汽油, 2-98号汽油, 3-0号柴油',
  `price` decimal(4, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_merchant_id`(`merchant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商户油价表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of merchant_oil_price
-- ----------------------------

-- ----------------------------
-- Table structure for merchant_platform
-- ----------------------------
DROP TABLE IF EXISTS `merchant_platform`;
CREATE TABLE `merchant_platform`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0,
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of merchant_platform
-- ----------------------------

-- ----------------------------
-- Table structure for merchant_qualification
-- ----------------------------
DROP TABLE IF EXISTS `merchant_qualification`;
CREATE TABLE `merchant_qualification`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `merchant_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `business_license_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '营业执照文件路径',
  `business_license_expire_date` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '营业执照有效期',
  `hazardous_license_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '危险化学品经营许可证文件路径',
  `hazardous_license_expire_date` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '危险化学品经营许可证有效期',
  `oil_retail_license_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '成品油零售经营批准证书文件路径',
  `oil_retail_license_expire_date` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '成品油零售经营批准证书有效期',
  `bank_license_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '开户许可证文件路径',
  `bank_license_expire_date` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '开户许可证有效期',
  `legal_person_id_front_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '法人身份证正面文件路径',
  `legal_person_id_back_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '法人身份证反面文件路径',
  `legal_person_id_expire_date` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '法人身份证有效期',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_merchant_id`(`merchant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商户资质表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of merchant_qualification
-- ----------------------------

-- ----------------------------
-- Table structure for merchant_settlement
-- ----------------------------
DROP TABLE IF EXISTS `merchant_settlement`;
CREATE TABLE `merchant_settlement`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `merchant_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `settlement_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '结算方式：0-对私，1-对公',
  `bank_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '开户行',
  `bank_account` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行卡号',
  `account_holder` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '持卡人姓名',
  `bank_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行预留手机号',
  `license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '开户许可证',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_merchant_id`(`merchant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商户结算信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of merchant_settlement
-- ----------------------------

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '电话号码',
  `api_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登录token',
  `id_card` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '头像地址',
  `is_verified` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否实名认证：0-未认证，1-已认证',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态：0-禁用，1-启用',
  `login_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `login_time` int UNSIGNED NOT NULL DEFAULT 0,
  `last_login_time` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '最后登录时间',
  `last_login_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '最后登录IP',
  `created_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updated_at` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username`) USING BTREE,
  UNIQUE INDEX `uk_id_card`(`id_card`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
