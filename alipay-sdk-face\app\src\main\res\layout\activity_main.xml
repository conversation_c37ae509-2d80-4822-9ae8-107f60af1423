<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="22sp"
        android:textColor="@android:color/black"
        android:text="请选择需要启动的模式" />

    <RadioGroup
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/radio_group_biz">

        <RadioButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/radio_face_app"
            android:text="FACE_APP"
            android:checked="true"
            />

        <RadioButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/radio_face_alipay_sdk"
            android:text="FACE_ALIPAY_SDK"
            />

        <RadioButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/radio_livingness_sdk"
            android:text="LIVINGNESS_SDK"
            />

    </RadioGroup>

    <EditText
        android:id="@+id/certifyId"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="dtfv_c2be75e41951efd75da8e3c405ded33d" />

    <EditText
        android:id="@+id/url"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/url_hint" />

    <Button
        android:id="@+id/btn_start_face"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/button_start_face"/>

    <Button
        android:id="@+id/btn_idcard_front"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/button_start_idcard_front"/>

    <Button
        android:id="@+id/btn_idcard_back"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/button_start_idcard_back"/>

    <Button
        android:id="@+id/btn_bankcard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/button_start_backcard"/>



</LinearLayout>