<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * 用户登录
     */
    public function login(Request $request)
    {
        $login_ip = $request->ip();

        $username = $request->input('username', '');
        $password = $request->input('password', '');

        if($username == '' || $password == '') {
            return ['status' => false, 'message' => '用户账号或密码不能为空'];
        }

        $user = User::where('username', $username)->first();
        if (!$user || !Hash::check($password, $user->password)) {
            return ['status' => false, 'message' => '账号或密码错误'];
        }

        $user->last_login_ip = $user->login_ip;
        $user->last_login_time = $user->login_time ?? 0;
        $user->login_ip = $request->ip();
        $user->login_time = time();
        if (empty($user->api_token)) {
            $user->api_token = Hash::make(time() . $request->ip() . $user->id . $user->created_at);
        }

        $user->save();

        $request->headers->set('Authorization', 'Bearer ' . $user->api_token);

        return ['status' => true, 'data' => $user, 'api_token' => $user->api_token];
    }

    /**
     * 用户注册
     */
    public function register(Request $request)
    {
        $request->validate([
            'username' => 'required|string|unique:users',
            'password' => 'required|string|min:6',
            'real_name' => 'required|string',
            'phone' => 'required|string|unique:users'
        ]);

        $user = User::create([
            'username' => $request->username,
            'password' => Hash::make($request->password),
            'real_name' => $request->real_name,
            'phone' => $request->phone,
            'role' => 1, // 默认商户角色
            'status' => 1 // 默认启用状态
        ]);

        // 创建token
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'token' => $token,
            'user' => $user
        ], 201);
    }

    /**
     * 用户退出
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => '退出成功'
        ]);
    }
} 