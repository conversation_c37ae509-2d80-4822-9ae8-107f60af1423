<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Crm\AuthController;
use App\Http\Controllers\Crm\MerchantController;
use App\Http\Controllers\Crm\HomeController;
use App\Http\Controllers\Crm\UploadController;
use App\Http\Controllers\Crm\UserController;

/*
|--------------------------------------------------------------------------
| Market Routes
|--------------------------------------------------------------------------
*/

// 公开路由
Route::post('login', [AuthController::class, 'login']);
Route::post('register', [AuthController::class, 'register']);
Route::get('test/chunk', [UploadController::class, 'uploadTestChunk']);

// 扫脸认证回调
Route::get('auth/face/callback', [UserController::class, 'authFaceCallback'])->name('face.callback');

// 需要认证的路由
Route::middleware('auth:crm')->group(function () {
    // 文件上传路由
    Route::post('/upload', [UploadController::class, 'upload']);
    // 上传合同
    Route::post('upload/contract', [UploadController::class, 'uploadContract']);

    // 新增分块上传路由
    Route::post('upload/chunk', [UploadController::class, 'uploadChunk']);
    Route::post('upload/merge', [UploadController::class, 'mergeChunks']);

    // 用户认证相关
    Route::post('logout', [AuthController::class, 'logout']);
    Route::get('profile', [UserController::class, 'profile']);
    Route::post('profile', [UserController::class, 'updateProfile']);
    Route::post('auth/user', [UserController::class, 'AuthUser']);

    // 获取验证码
    Route::post('sms/send', [UserController::class, 'sendSMS']);

    // 扫脸认证
    Route::post('auth/face/init', [UserController::class, 'initFace']);

    // 获取区域
    Route::get('area', [HomeController::class, 'getArea']);

    // 首页
    Route::get('index', [HomeController::class, 'index']);

    // 商户列表
    Route::get('merchant', [MerchantController::class, 'index']);

    // 商户详情
    Route::get('merchant/detail/{merchant}', [MerchantController::class, 'detail']);
    Route::get('merchant/statics/{merchant}', [MerchantController::class, 'statics']);

    // 更新商户
    Route::put('merchant/{merchant}', [MerchantController::class, 'edit']);

    // 创建/保存商户(保存草稿)
    Route::post('merchant/save', [MerchantController::class, 'saveMerchant']);
    // 获取单个商户
    Route::get('merchant/get', [MerchantController::class, 'getMerchant']);

    // 提交审核
    Route::post('merchant/submit/{merchant}', [MerchantController::class, 'submitMerchant']);
    // 撤销审核/验收
    Route::post('merchant/cancel/{merchant}', [MerchantController::class, 'cancelMerchant']);

    // 保存验收(保存草稿)
    Route::post('merchant/acceptance/{merchant}', [MerchantController::class, 'acceptance']);

    // OCR扫描
    Route::post('merchant/material/ocrCheck', [MerchantController::class, 'ocrCheck']);

    // 获取合作平台
    Route::get('merchant/platform/get', [MerchantController::class, 'getPlatform']);

    // 获取银行
    Route::get('bank/list', [MerchantController::class, 'getBank']);
    
});