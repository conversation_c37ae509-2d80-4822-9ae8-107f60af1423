<?php

namespace App\Service\SMS;

abstract class SmsService
{
    public static function driver($driver)
    {
        switch ($driver) {
            case 'aliyun':
                return new AliSMS();
            default:
                throw new \Exception('未知的短信驱动', 500);
        }
    }

    // 设置短信签名
    abstract public function setSignName($sign_name);
    
    // 设置短信内容模板
    abstract public function setTemplateId($template_id);

    // 设置短信内容参数
    abstract public function setParams($params);

    // 发送短信
    abstract public function send($mobile);
}
