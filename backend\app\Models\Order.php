<?php

namespace App\Models;

use Illuminate\Validation\Rule;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Order extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $connection = 'mysql_ysh';
    protected $table = 'order';


    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'amount' => 'decimal:2'
    ];

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'stor_id');
    }

    /**
     * Get the merchant through store.
     */
    public function merchant()
    {
        return $this->store->merchant();
    }

    /**
     * 获取商户最近30天的日均交易额
     */
    public static function getAverageDailyAmount($merchantId, $days = 30)
    {
        $startDate = now()->subDays($days)->startOfDay();
        
        return self::query()
            ->join('store', 'order.store_id', '=', 'store.stor_id')
            ->where('store.merchant_id', $merchantId)
            ->whereIn('order.status', ['payment', 'refunding', 'refund_failed', 'request_refund', 'refund_rejected'])
            ->where('order.created_at', '>=', $startDate)
            ->select(
                DB::raw('COUNT(DISTINCT DATE(order.created_at)) as total_days'),
                DB::raw('SUM(order.pay_money) as total_amount')
            )
            ->first();
    }

    /**
     * 计算商户的达标率
     */
    public static function calculateAchievementRate($merchantId, $dailyTarget, $days = 30)
    {
        $salesData = self::getAverageDailyAmount($merchantId, $days);
        
        if (!$salesData->total_days) {
            return 0;
        }

        $averageDailyAmount = $salesData->total_amount / $salesData->total_days;
        return ($averageDailyAmount / $dailyTarget) * 100;
    }
}