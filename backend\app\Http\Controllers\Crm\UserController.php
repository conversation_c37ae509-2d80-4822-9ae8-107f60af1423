<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Service\VerifiFace\VerifiService;
use App\Service\SMS\SmsService;
use Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * 获取用户信息
     */
    public function profile(Request $request)
    {
        $user = $request->user();
        return ['status' => true, 'data' => $user];
    }

    /**
     * 更新用户信息
     */
    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $params = $request->all();

        $model = new User();

        $validator = Validator::make($params, $model->rule($user), $model->message);
        if ($validator->fails()) {
            return ['status' => false, 'message' => $validator->errors()->all()[0]];
        }

        if ($request->has('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($params);

        return response()->json([
            'status' => true,
            'message' => '更新成功',
        ]);
    }

    // 扫脸认证
    public function verifiFace(Request $request)
    {
        $user = $request->user();
        $params = $request->all();
        
        try {
            $driver = VerfiService::driver($params['type']);
            $certResult = $driver->getInfo(array_merge($params, ['user_id' => $user->id]));

            if ($certResult['status']) {
                $user->verified_status = 1;
                $user->verified_at = now();
                $user->save();
            }

            return response()->json([
                'status' => $certResult['status'],
                'message' => $certResult['message'],
                'data' => $certResult['data'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error('扫脸认证失败：'.$e->getMessage(), ['user' => $user->id]);
            return response()->json([
                'status' => false,
                'message' => '认证服务暂时不可用'
            ], 500);
        }

    }

    public function sendSMS(Request $request)
    {
        if (!$request->filled('mobile')) {
            return ['status' => false, 'message' => '请输入手机号码'];
        }

        $user = $request->user();
        
        $code = rand(100000, 999999);

        Cache::put('user_send_code_' . $user->id, $code, 300);

        $result = SmsService::driver('aliyun')->setSignName('油实惠')->setTemplateId('SMS_232515066')->setParams(['code' => $code])->send($request->input('mobile'));

        if ($result) {
            Cache::put('user_send_code_' . $user->id, $code, 300);
            return ['status' => true, 'message' => '短信发送成功'];
        }

        return ['status' => false, 'message' => '短信发送失败', 'code' => 500];
    }

    // 实名认证
    public function AuthUser(Request $request)
    {
        $user = $request->user();

        if($user->is_verified == 1) {
            return ['status' => false, 'message' => '您已认证，无需重复认证'];
        }
        
        $params = $request->validate([
            'name' => 'required',
            'id_card' => 'required',
            'phone' => 'required',
            'verification_code' => 'required'
        ]);

        $code = Cache::get('user_send_code_' . $user->id);

        if($code !== (int)$params['verification_code']) {
            return ['status' => false, 'message' => '验证码有误'];
        }

        try {
            $identity = [
                'identity_type' => 'CERT_INFO',
                'cert_type' => 'IDENTITY_CARD',
                'cert_name' => $request->input('name'),
                'cert_no' => $request->input('id_card'),
                'phone_no' => $request->input('phone'),
            ];
            
            $outerOrderNo = Str::uuid()->toString();

            $returnUrl = route('face.callback');
            $service = VerifiService::driver('alipay');
            $res = $service->initialize($identity, $outerOrderNo, $returnUrl . '?outer_order_no=' . $outerOrderNo);
            
            if($res['code'] == '10000') {
                $user->update([
                    'name' => $request->input('name'),
                    'phone' => $request->input('phone'),
                    'id_card' => $request->input('id_card'),
                    'certify_id' => $res['certify_id'],
                    'outer_order_no' => $outerOrderNo,
                ]);
                $certify_url = $service->verify($res['certify_id']);
                return ['status' => true, 'certify_url' => $certify_url];
            } else {
                return ['status' => false, 'message' => $res['msg'] || '认证服务错误'];
            }
        } catch (\Exception $e) {
            Log::error('实名认证失败: ' . $e->getMessage());
            return ['status' => false, 'message' => '认证服务异常'];
        }
    }

    public function authFaceCallback(Request $request) {
        $data = $request->all();
        Log::info('人脸核身回调参数', ['params' => $data]);
    
        try {
    
            // 2. 查询最终结果（防止伪造回调）
            $outer_order_no = $data['outer_order_no'];
            $user = User::where('outer_order_no', $outer_order_no)->first();
            $service = VerifiService::driver('alipay');
            $queryResult = $service->query($user->certify_id);
    
            // 3. 处理结果
            if ($queryResult['code'] == '10000' && $queryResult['passed'] === 'T') {
                $user->update(['is_verified' => 1]);
                Log::info('核身成功', ['user' => $user->id]);
                //return response('success');
                return view('crm.face', ['result' => 'success']);
            }
    
            Log::warning('核身未通过', $queryResult);
            //return response('fail');
            return view('crm.face', ['result' => 'fail']);
    
        } catch (\Exception $e) {
            Log::error('回调处理异常: ' . $e->getMessage());
            //return response('fail');
            return view('crm.face', ['result' => 'fail']);
        }
    }

    /**
     * 构建支付宝核身链接
     */
    private function buildAlipayAuthUrl($certifyId) {
        $params = [
            'certifyId' => $certifyId,
            'appId' => env('ALIPAY_FACE_APP_ID')
        ];
        return 'https://openapi.alipay.com/gateway.do?' . http_build_query($params);
    }
}