<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>跳转到应用</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
      text-align: center;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #f7f9ff;
    }
    .card {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 30px;
      width: 80%;
      max-width: 400px;
      margin: 20px;
    }
    h1 {
      color: #2D5FCF;
      font-size: 20px;
      margin-bottom: 20px;
    }
    p {
      color: #666;
      font-size: 16px;
      line-height: 1.5;
      margin-bottom: 30px;
    }
    button {
      background-color: #2D5FCF;
      color: white;
      font-weight: bold;
      border: none;
      padding: 14px 20px;
      border-radius: 8px;
      font-size: 16px;
      cursor: pointer;
      width: 100%;
      margin-bottom: 15px;
    }
    button:hover {
      background-color: #244fb3;
    }
    .icon {
      width: 60px;
      height: 60px;
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: #e9f0ff;
    }
    .icon svg {
      width: 30px;
      height: 30px;
      fill: #2D5FCF;
    }
    .note {
      font-size: 14px;
      color: #999;
      margin-top: 20px;
    }
    .hidden {
      position: absolute;
      left: -9999px;
      top: -9999px;
    }
  </style>
</head>
<body>
  <div class="card">
    <div class="icon">
      @if($result == 'success')
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z" stroke="currentColor" stroke-width="2"/>
              <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
      @else
          <!-- 这里可以替换为你想要的失败图标 SVG -->
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2Z" stroke="currentColor" stroke-width="2"/>
              <path d="M15 9L9 15M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
      @endif
    </div>
    <h1>{{$result == 'success' ? '认证成功' : '认证失败'}}</h1>
    <p>您的实名认证信息已提交成功，请点击下方按钮返回应用查看认证结果。</p>
    <button id="openAppBtn">返回应用</button>
    <div class="note">如果无法自动返回，请手动打开应用</div>
  </div>

  <!-- 隐藏的链接和iframe -->
  <a id="appLink" href="ysh-crm://Profile" class="hidden"></a>
  <iframe id="appFrame" class="hidden"></iframe>

  <script>
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 获取按钮和链接元素
      var openAppBtn = document.getElementById('openAppBtn');
      var appLink = document.getElementById('appLink');
      var appFrame = document.getElementById('appFrame');
      
      // 定义打开应用的函数（结合多种方法）
      function openApp() {
        console.log('尝试打开应用...');
        
        // 方法1: 直接设置window.location
        setTimeout(function() {
          window.location.href = 'ysh-crm://Profile';
        }, 100);
        
        // 方法2: 点击隐藏的链接
        setTimeout(function() {
          appLink.click();
        }, 200);
        
        // 方法3: 使用iframe
        setTimeout(function() {
          appFrame.src = 'ysh-crm://Profile';
        }, 300);
      }
      
      // 为按钮添加点击事件
      openAppBtn.addEventListener('click', function() {
        openApp();
      });
      
      // 尝试自动打开应用
      setTimeout(openApp, 1000);
    });
  </script>
</body>
</html>