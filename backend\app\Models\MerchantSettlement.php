<?php

namespace App\Models;

class MerchantSettlement extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'merchant_settlement';

    protected $dateFormat = 'U';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'settlement_type',
        'bank_account',
        'account_holder',
        'bank_phone',
        'license',
        'bank_image',
        'bank_id'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [];

    public function getCreatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getUpdatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    /**
     * 获取商户信息
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    public function bank()
    {
        return $this->belongsTo(BankList::class, 'bank_id');
    }
} 