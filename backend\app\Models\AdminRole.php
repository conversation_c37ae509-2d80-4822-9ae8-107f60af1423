<?php

namespace App\Models;

class AdminRole extends Model
{
    protected $table = 'admin_role';

    protected $guarded = [];

    public $timestamps = false;

    protected $fillable = [
        'name',
        'permission',
    ];

    public $message = [
        'name.required' => '角色名称不能为空',
        'permission.required' => '角色权限不能为空',
    ];

    public function rule(AdminRole $model = null)
    {
        return [
            'name' => 'required|unique:admin_role' . ($model ? ',name,' . $model->id : ''),
            'permission' => 'required',
        ];
    }

    public function setPermissionAttribute($value)
    {
        $this->attributes['permission'] = is_array($value) ? json_encode($value) : json_encode([]);
    }

    public function getPermissionAttribute($value)
    {
        return json_decode($value) ?? $value;
    }

    public function admin()
    {
        return $this->hasMany('App\Models\Admin', 'role_id', 'id');
    }
}
