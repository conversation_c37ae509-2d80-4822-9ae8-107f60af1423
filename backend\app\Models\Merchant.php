<?php

namespace App\Models;

use Illuminate\Validation\Rule;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Merchant extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'merchant';

    protected $dateFormat = 'U';
    
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'online_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_no',
        'merchant_name',
        'type',
        'business_license',
        'cooperation_mode',
        'cooperation_platform',
        'address',
        'latitude',
        'longitude',
        'store_photos',
        'daily_sales_target',
        'balance',
        'gift_amount',
        'platform_discount',
        'contact_person',
        'contact_phone',
        'status',
        'user_id',
        'market_manager_id',
        'is_draft',
        'contract',
        'is_confirm',
        'store_id',
        'province_code',
        'city_code',
        'area_code',
        'store_photos_lat',
        'store_photos_lng',
        'recharge_policy',
        'progress_status',
        'audit_status',
        'online_at'
    ];

    public $message = [
        'merchant_name.required' => '商户名称不能为空',
        'merchant_name.max' => '商户名称不能超过100个字符',
        'type.required_if' => '商户类型不能为空',
        'type.in' => '商户类型不正确',
        'business_license.required_if' => '营业执照号不能为空',
        'business_license.unique' => '营业执照号已存在',
        'business_license.max' => '营业执照号不能超过50个字符',
        'cooperation_mode.required_if' => '合作模式不能为空',
        'cooperation_mode.in' => '合作模式不正确',
        'address.required_if' => '详细地址不能为空',
        'address.max' => '详细地址不能超过255个字符',
        'latitude.numeric' => '纬度必须是数字',
        'longitude.numeric' => '经度必须是数字',
        'store_photos.required_if' => '店铺照片不能为空',
        'contact_person.required_if' => '联系人不能为空',
        'contact_person.max' => '联系人不能超过50个字符',
        'contact_phone.required_if' => '联系电话不能为空',
        'contact_phone.max' => '联系电话不能超过20个字符',
        'province_code.required_if' => '省份不能为空',
        'city_code.required_if' => '城市不能为空',
        'area_code.required_if' => '区域不能为空',
        'recharge_policy.required_if' => '充值政策不能为空',
    ];

    /**
     * 获取验证规则
     */
    public function rule(Merchant $model = null)
    {
        $rules = [
            'merchant_name' => 'required|string|max:100',
            'type' => 'required_if:is_draft,0|integer|in:0,1,2',
            'business_license' => 'required_if:is_draft,0|string|max:50|unique:merchant' . ($model ? ',business_license,' . $model->id : ''),
            'cooperation_mode' => 'required_if:is_draft,0|in:0,1',
            'address' => 'required_if:is_draft,0|string|max:255',
            'latitude' => 'required_if:is_draft,0|numeric',
            'longitude' => 'required_if:is_draft,0|numeric',
            'store_photos' => 'required_if:is_draft,0',
            'contact_person' => 'required_if:is_draft,0|string|max:50',
            'contact_phone' => 'required_if:is_draft,0|string|max:20',
            'contract' => 'required_if:is_draft,0|string|max:255',
            'province_code' => 'required_if:is_draft,0',
            'city_code' => 'required_if:is_draft,0',
            'area_code' => 'required_if:is_draft,0',
            'recharge_policy' => 'required_if:is_draft,0'
        ];

        return $rules;
    }

    public function getCreatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getUpdatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getCooperationPlatformAttribute($value)
    {
        return json_decode($value);
    }

    public function setCooperationPlatformAttribute($value)
    {
        $this->attributes['cooperation_platform'] = json_encode($value);
    }

    /**
     * 获取商户的市场负责人
     */
    public function marketManager()
    {
        return $this->belongsTo(User::class, 'market_manager_id');
    }

    /**
     * 获取商户资质信息
     */
    public function qualification(): HasOne
    {
        return $this->hasOne(MerchantQualification::class, 'merchant_id');
    }

    /**
     * 获取商户结算信息
     */
    public function settlement(): HasOne
    {
        return $this->hasOne(MerchantSettlement::class, 'merchant_id');
    }

    /**
     * 获取商户审核记录
     */
    public function audits()
    {
        return $this->hasMany(MerchantAudit::class);
    }

    /**
     * 获取商户验收信息
     */
    public function acceptance(): HasOne
    {
        return $this->hasOne(MerchantAcceptance::class, 'merchant_id');
    }

    /**
     * 获取商户的油价信息
     */
    public function oilPrices(): HasMany
    {
        return $this->hasMany(MerchantOilPrice::class, 'merchant_id');
    }

    /**
     * Get the stores for the merchant.
     */
    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id', 'stor_id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'store_id', 'store_id');
    }

    public function province()
    {
        return $this->belongsTo('App\Models\Province', 'province_code', 'pr_provinceid');
    }

    public function city()
    {
        return $this->belongsTo('App\Models\City', 'city_code', 'ci_cityid');
    }

    public function area()
    {
        return $this->belongsTo('App\Models\Area', 'area_code', 'ar_areaid');
    }

    public function scopeOfName($query, $value)
    {
        return $query->where('merchant_name', 'like', "%{$value}%");
    }

    public function scopeOfType($query, $value)
    {
        return $query->where('type', $value);
    }

    public function scopeOfMode($query, $value)
    {
        return $query->where('cooperation_mode', $value);
    }
    
    public function scopeOfAreas($query, $value)
    {
        if (isset($value[0]) && $value[0]) {
            $query = $query->where('province_code', $value[0]);
        }
        if (isset($value[1]) && $value[1]) {
            $query = $query->where('city_code', $value[1]);
        }
        if (isset($value[2]) && $value[2]) {
            $query = $query->where('area_code', $value[2]);
        }
        return $query;
    }

    public function scopeOfUser($query, $value)
    {
        return $query->where('market_manager_id', $value);
    }

    public function scopeOfProgressStatus($query, $value)
    {
        return $query->where('progress_status', $value);
    }
    
    public function scopeOfAuditStatus($query, $value)
    {
        return $query->where('audit_status', $value);
    }

    public function getTypeText()
    {
        switch ($this->type) {
            case 0:
                return '油站';
            case 1:
                return '充电站';
            case 2:
                return '综合能源站';
            default:
                return '-';
        }
    }

    public function getModeText()
    {
        switch ($this->cooperation_mode) {
            case 0:
                return '预储值';
            case 1:
                return 'T+1';
            default:
                return '-';
        }
    }

    public function getStatusText()
    {
        switch ($this->status) {
            case 0:
                return '待提交';
            case 1:
                return '审核中';
            case 2:
                return '审核通过';
            case 3:
                return '审核不通过';
            case 4:
                return '待验收';
            case 5:
                return '验收中';
            case 6:
                return '验收通过';
            case 7:
                return '验收不通过';
            case 8:
                return '已上线';
            default:
                return '-';
        }
    }

    public function checkUpdateStatus($status)
    {
        switch ($status) {
            case 0:
                if ($this->progress_status != 1) {
                    return false;
                }
                return true;
            case 1:
                if ($this->progress_status!= 2 || $this->progress_status!= 0) {
                    return false;
                }
                return true;
            case 2:
                if (!in_array($this->progress_status, [1, 3])) {
                    return false;
                }
                return true;
            case 3:
                if ($this->progress_status!= 4 || $this->progress_status!= 2) {
                    return false;
                }
                return true;
            case 4:
                if ($this->progress_status!= 3) {
                    return false;
                }
                return true;
            default:
                return false;
        }
    }

    // 获取上线后第2天到指定天数的日均交易额
    public function getDailyAvgAmount($days = 30)
    {
        $start = \Carbon\Carbon::parse($this->online_at)->addDays();
        $end = $start->copy()->addDays($days - 1);
        
        // 如果结束时间大于当前时间，则取当前时间为结束时间
        if ($end > \Carbon\Carbon::now()) {
            $end = \Carbon\Carbon::now();
            $days = $start->diffInDays($end);
        }
        
        $total = $this->orders()
            ->whereIn('status', ['payment','refunding','refund_failed','request_refund','refund_rejected'])
            ->whereBetween('created_at', [$start->format('Y-m-d H:i:s'), $end->format('Y-m-d H:i:s')])
            ->sum('original_price');

        return $days > 0 ? round($total / $days, 2) : 0;
    }

    public function scopeSalesTargetVolume($query, $value) {
        return $query->where(function($q) use ($value) {
            $q->whereNotNull('online_at')
                ->whereNotNull('daily_sales_target')
                ->where('daily_sales_target', '>', 0)
                ->whereRaw('EXISTS (
                    SELECT 1 FROM ysh_oil.order o 
                    WHERE o.store_id = merchant.store_id 
                    AND o.status IN (\'payment\', \'refunding\', \'refund_failed\', \'request_refund\', \'refund_rejected\')
                    AND o.created_at >= FROM_UNIXTIME(merchant.online_at + 86400)
                    GROUP BY o.store_id
                    HAVING CASE 
                        WHEN ? = 0 THEN (SUM(o.original_price) / GREATEST(DATEDIFF(CURDATE(), FROM_UNIXTIME(merchant.online_at + 86400)), 1) / merchant.daily_sales_target) * 100 < 50
                        WHEN ? = 1 THEN (SUM(o.original_price) / GREATEST(DATEDIFF(CURDATE(), FROM_UNIXTIME(merchant.online_at + 86400)), 1) / merchant.daily_sales_target) * 100 BETWEEN 50 AND 80
                        WHEN ? = 2 THEN (SUM(o.original_price) / GREATEST(DATEDIFF(CURDATE(), FROM_UNIXTIME(merchant.online_at + 86400)), 1) / merchant.daily_sales_target) * 100 > 80
                        ELSE true
                    END
                )', [$value, $value, $value]);
        });
    }
}
