<?php

namespace App\Models;

use Illuminate\Validation\Rule;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Store extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $connection = 'mysql_ysh';
    protected $table = 'store';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'stor_id';

    protected $fillable = [
        'stor_title',
        'stor_provin',
        'stor_city',
        'stor_area',
        'stor_address',
        'stor_lng',
        'stor_lat',
        'stor_paytype',
        'stor_pic',
        'stor_statu',
        'stor_type',
        'contacts',
        'contacts_phone',
    ];

    /**
     * Get the merchant that owns the store.
     */
    public function merchant()
    {
        return $this->hasOne(Merchant::class, 'stor_id', 'store_id');
    }

    /**
     * Get the orders for the store.
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'store_id', 'store_id');
    }

    public function shop()
    {
        return $this->hasMany(Shop::class, 'store_id', 'stor_id');
    }

    public function account()
    {
        return $this->hasOne(StoreAccount::class, 'store_id', 'stor_id');
    }

    public function payInfo()
    {
        return $this->hasOne(StorePay::class, 'stor_id', 'stor_id');
    }

    public function scopeOfProvince($query, $value)
    {
        return $query->where('stor_provin', $value);
    }

    public function scopeOfCity($query, $value)
    {
        return $query->where('stor_city', $value);
    }
}
