<?php

namespace App\Service\VerifiFace\Driver;

use Alipay\EasySDK\Kernel\Config;
use Alipay\EasySDK\Kernel\Factory;

class Alipay
{
    public function __construct() {
       // 本地初始化 SDK 配置（无需全局 Provider）
       $cfg = new Config();
       $cfg->protocol           = 'https';
       $cfg->gatewayHost        = 'openapi.alipay.com';
       $cfg->signType           = 'RSA2';
       $cfg->appId              = '2021005138689245';
       $cfg->merchantPrivateKey = env('ALIPAY_FACE_PRIVATE_KEY');
       $cfg->alipayPublicKey    = env('ALIPAY_FACE_PUBLIC_KEY');
       Factory::setOptions($cfg);
    }

    /**
     * 1. 初始化认证，获取 certify_id
     */
    public function initialize(array $identityParam, string $outerOrderNo, string $returnUrl): array
    {
        $bizParams = [
            'biz_code'        => 'FUTURE_TECH_BIZ_FACE_SDK',
            'identity_param'  => $identityParam,
            'outer_order_no'  => $outerOrderNo,
            'merchant_config' => ['return_url' => $returnUrl],
        ];

        $resp = Factory::util()->generic()
            ->execute(
                'datadigital.fincloud.generalsaas.face.certify.initialize',
                [],        // 无额外系统参数
                $bizParams // 业务参数
            );  
        // Generic 通用接口 :contentReference[oaicite:1]{index=1}

        $body = json_decode($resp->httpBody, true);
        $key  = 'datadigital_fincloud_generalsaas_face_certify_initialize_response';
        return $body[$key] ?? ($body['error_response'] ?? []);
    }

    /**
     * 2. 获取刷脸 URL
     */
    public function verify(string $certifyId): ?string
    {
        $resp = Factory::util()->generic()
            ->execute(
                'datadigital.fincloud.generalsaas.face.certify.verify',
                [],
                ['certify_id' => $certifyId]
            );  
        // Generic 通用接口 :contentReference[oaicite:2]{index=2}

        $body = json_decode($resp->httpBody, true);
        $key  = 'datadigital_fincloud_generalsaas_face_certify_verify_response';

        return $body[$key]['certify_url'] ?? null;
    }

    /**
     * 3. 查询刷脸结果
     */
    public function query(string $certifyId): array
    {
        $resp = Factory::util()->generic()
            ->execute(
                'datadigital.fincloud.generalsaas.face.certify.query',
                [],
                ['certify_id' => $certifyId]
            );  
        // Generic 通用接口 :contentReference[oaicite:3]{index=3}

        $body = json_decode($resp->httpBody, true);
        $key  = 'datadigital_fincloud_generalsaas_face_certify_query_response';

        return $body[$key] ?? [];
    }
}