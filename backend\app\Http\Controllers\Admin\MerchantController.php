<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Merchant;
use App\Models\MerchantAudit;
use App\Models\MerchantPlatform;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\BaseCollection;

class MerchantController extends Controller
{
    /**
     * 商户列表
     */
    public function index(Request $request)
    {
        $model = Merchant::with([
            'marketManager' => function ($query) {
                $query->select('id', 'name');
            },
            'province',
            'city',
            'area'
        ]);

        if ($request->has('search')) {
            $model = $this->addScope($model, $request->input('search', []));
        }

        if ($request->has('sort')) {
            $sort = $request->input('sort.order', 'desc') == 'ascend' ? 'asc' : 'desc';
            $model = $model->orderBy($request->get('sort.field', 'id'), $sort);
        }

        if ($request->input('type') == 'all') {
            return $model->where('progress_status', 4)->get();
        }

        return new BaseCollection($model->paginate($request->input('pageSize', 10)));
    }

    /**
     * 商户详情
    */
    public function detail(Merchant $merchant)
    {
        $merchant->load([
            'marketManager' => function ($query) {
                $query->select('id', 'name');
            },
            'qualification',
            'settlement' => function($q) {
                $q->with('bank');
            },
            'audits' => function ($q) {
                $q->with('admin')->orderBy('created_at', 'desc');
            },
            'acceptance',
            'oilPrices',
            'province',
            'city',
            'area'
        ]);

        if($merchant->cooperation_platform != '' && sizeof($merchant->cooperation_platform) > 0) {
            $merchant->platform = MerchantPlatform::whereIn('id', $merchant->cooperation_platform)->get();
        }

        return $merchant;
    }

    /**
     * 商户导出
     */
    public function export(Request $request)
    {
        $model = Merchant::with('marketManager', 'province', 'city', 'area');

        if ($request->has('search')) {
            $model = $this->addScope($model, $request->input('search', []));
        }

        $model = $model->orderBy('id', 'desc');

        $columns = [
            '商户名称',
            '商户类型',
            '省份',
            '城市',
            '区县',
            '详细地址',
            '营业执照号',
            '合作模式',
            '合作平台',
            '平台优惠金额',
            '储值金额',
            '赠送金额',
            '负责人',
            '联系电话',
            '市场负责人',
            '状态',
            '创建时间',
            '更新时间',
        ];

        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-Allow-Methods: GET, POST, PATCH, PUT, OPTIONS, DELETE');
        header('Access-Control-Allow-Credentials: true');
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment;filename="' . urlencode('商户导出_' . date('Y-m-d H:i:s')) . 'csv"');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');

        $output = fopen('php://output', 'a');
        fwrite($output, chr(0xEF) . chr(0xBB) . chr(0xBF));
        fputcsv($output, $columns);

        $model->chunk(1000, function ($data) use ($output) {
            foreach ($data as $v) {
                $row = [
                    $v->merchant_name,
                    $v->getTypeText(),
                    $v->province->pr_province ?? '',
                    $v->city->ci_city ?? '',
                    $v->area->ar_area ?? '',
                    $v->address,
                    $v->business_license,
                    $v->getModeText(),
                    $v->cooperation_platform,
                    $v->platform_discount,
                    $v->balance,
                    $v->gift_amount,
                    $v->contact_name,
                    $v->contact_phone,
                    $v->market_manager->name ?? '',
                    $v->getStatusText(),
                    $v->created_at,
                    $v->updated_at,
                ];
                fputcsv($output, $row);
            }
            ob_flush();
            flush();
        });

        fclose($output);
        exit;
    }

    /**
     * 更新商户状态
     */
    public function editProgressStatus(Request $request, Merchant $merchant)
    {
        if (!$request->filled('progress_status')) {
            return ['status' => false,'message' => '缺少状态参数'];
        }
        if (!$request->filled('audit_status')) {
            return ['status' => false,'message' => '缺少状态参数'];
        }

        $progress_status = $request->input('progress_status', 0);
        $audit_status = $request->input('audit_status', 0);
        $store_id = $request->input('store_id', 0);
        $comment = $request->input('comment', '');
        $audit_type = $merchant->progress_status == 1 ? 0 : 1;

        // 下一步业务状态为上线时，需要检查关联店铺信息
        if ($progress_status == 4 && $store_id == 0) {
            return ['status' => false,'message' => '缺少关联店铺信息'];
        }

        // 审核状态为不通过时，需要检查审核意见
        if ($audit_status == 2 && empty($comment)) {
            return ['status' => false,'message' => '缺少审核意见'];
        }

        if ($merchant->progress_status == $progress_status) {
            return ['status' => false,'message' => '状态未改变'];
        }

        $check_status = $merchant->checkUpdateStatus($progress_status);
        if (!$check_status) {
            return ['status' => false,'message' => '状态不正确' . $progress_status . '/' . $merchant->progress_status];
        }

        $merchant->progress_status = $progress_status;
        $merchant->audit_status = $audit_status;
        if ($progress_status == 4) {
            $merchant->store_id = $store_id;
            $merchant->online_at = time();
        }
        $merchant->save();

        $user = Auth::guard('admin')->user();

        $audit = new MerchantAudit();
        $audit->merchant_id = $merchant->id;
        $audit->type = $audit_type;
        $audit->status = $audit_status;
        $audit->reviewer_id = $user->id;
        $audit->comment = $comment;
        $audit->save();

        return ['status' => true,'message' => '状态更新成功'];
    }

    /**
     * 平台列表
     */
    public function platform(Request $request)
    {
        $model = MerchantPlatform::query();

        if ($request->has('search')) {
            $model = $this->addScope($model, $request->input('search', []));
        }

        if ($request->has('sort')) {
            $sort = $request->input('sort.order', 'desc') == 'ascend' ? 'asc' : 'desc';
            $model = $model->orderBy($request->get('sort.field', 'id'), $sort);
        }

        if ($request->input('type') == 'all') {
            return $model->where('status', 1)->get();
        }

        return new BaseCollection($model->paginate($request->input('pageSize', 10)));
    }

    /**
     * 添加平台
     */
    public function addPlatform(Request $request)
    {
        $model = new MerchantPlatform();

        $validator = Validator::make($request->all(), $model->rule(), $model->message);
        if ($validator->fails()) {
            return ['status' => false, 'message' => $validator->errors()->all()[0]];
        }

        $model = $model->create($request->all());

        event('admin.merchant.platform.add');

        return ['status' => true];
    }

    /**
     * 编辑平台
     */
    public function editPlatform(Request $request, MerchantPlatform $platform)
    {
        $validator = Validator::make($request->all(), $platform->rule($platform), $platform->message);
        if ($validator->fails()) {
            return ['status' => false, 'message' => $validator->errors()->all()[0]];
        }

        event('admin.merchant.platform.edit');

        $platform->update($request->all());

        return ['status' => true];
    }

    /**
     * 删除平台
     */
    public function deletePlatform(Request $request)
    {
        $ids = $request->input('id', []);

        if (empty($ids)) {
            return ['status' => false, 'message' => '缺少参数'];
        }

        MerchantPlatform::destroy($ids);

        event('admin.merchant.platform.delete');

        return ['status' => true];
    }

}
