<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Http\Resources\BaseCollection;

class UserController extends Controller
{
    /**
     * 用户列表
     */
    public function user(Request $request)
    {
        $model = User::select('id', 'username', 'name', 'phone', 'is_verified', 'status', 'last_login_time', 'last_login_ip', 'created_at');

        if ($request->has('search')) {
            $model = $this->addScope($model, $request->input('search', []));
        }

        if ($request->has('sort')) {
            $sort = $request->input('sort.order', 'desc') == 'ascend' ? 'asc' : 'desc';
            $model = $model->orderBy($request->get('sort.field', 'id'), $sort);
        }

        if ($request->input('type') == 'all') {
            $data = $model->where('status', 1)->get();
            return ['data' => $data];
        }

        return new BaseCollection($model->paginate($request->input('pageSize', 10)));
    }

    /**
     * 添加管用户
     */
    public function addUser(Request $request)
    {
        $model = new User();

        $validator = Validator::make($request->all(), $model->rule(), $model->message);
        if ($validator->fails()) {
            return ['status' => false, 'message' => $validator->errors()->all()[0]];
        }

        $model = $model->create($request->all());

        event('admin.user.add');

        return ['status' => true];
    }

    /**
     * 编辑管理员
     */
    public function editUser(Request $request, User $user)
    {
        $validator = Validator::make($request->all(), $user->rule($user), $user->message);
        if ($validator->fails()) {
            return ['status' => false, 'message' => $validator->errors()->all()[0]];
        }

        event('admin.user.edit');

        $user->update($request->all());

        return ['status' => true];
    }

    /**
     * 编辑管理员状态
     */
    public function editStatus(Request $request, User $user)
    {
        $user->status = $request->input('status', $user->status);
        $user->save();

        event('admin.user.edit');

        return ['status' => true];
    }
}
