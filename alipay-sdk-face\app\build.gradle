apply plugin: 'com.android.application'

android {
    compileSdk 33

    defaultConfig {
        applicationId "com.alipay.mobile.android.verifydemo"
        minSdkVersion 19
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
    }
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    useLibrary 'org.apache.http.legacy'
}

//configurations.all {
//    exclude group: 'com.mpaas.android.verify', module:
//            'ocr-sdk'
//}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation "com.mpaas.android:face-verify:3.5.0"
}
