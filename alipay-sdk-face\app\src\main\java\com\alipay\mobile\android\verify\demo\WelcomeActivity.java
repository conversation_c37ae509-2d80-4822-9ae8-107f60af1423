package com.alipay.mobile.android.verify.demo;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;

import androidx.appcompat.app.AlertDialog;

import com.alipay.mobile.android.verify.sdk.MPVerifyService;

public class WelcomeActivity extends Activity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_welcome);
        initDialog();
    }

    private void initDialog() {
        new AlertDialog.Builder(this).setTitle("隐私协议")
                .setMessage("协议内容").setNegativeButton("同意", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        MPVerifyService.markUserAgreedPrivacyPolicy(WelcomeActivity.this.getApplicationContext());
                        startActivity(new Intent(WelcomeActivity.this, MainActivity.class));
                        dialog.dismiss();
                        finish();
                    }
                }).setNeutralButton("取消", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        finish();

                    }
                }).create().show();
    }

}
