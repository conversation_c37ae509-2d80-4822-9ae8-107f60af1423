<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MerchantOilPrice extends Model
{
    protected $table = 'merchant_oil_price';

    public $timestamps = false;

    protected $fillable = [
        'merchant_id',
        'oil_type',
        'price',
    ];

    public $message = [
        'merchant_id.required' => '商户ID不能为空',
        'oil_type.required' => '油品类型不能为空',
        'oil_type.in' => '油品类型不正确',
        'price.required' => '价格不能为空',
        'price.numeric' => '价格必须是数字',
        'price.min' => '价格不能小于0',
    ];

    public function rule(MerchantOilPrice $model = null)
    {
        return [
            'merchant_id' => 'required|integer',
            'oil_type' => 'required|in:0,1,2,3',
            'price' => 'required|numeric|min:0',
            'status' => 'required|integer|in:0,1'
        ];
    }

    /**
     * 获取关联的商户
     */
    public function merchant(): BelongsTo
    {
        return $this->belongsTo(Merchant::class, 'merchant_id');
    }
} 