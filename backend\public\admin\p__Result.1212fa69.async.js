(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[17],{mFC2:function(t,e,n){"use strict";n.r(e);n("J+/v");var a=n("MoRW"),i=(n("+L6B"),n("2/Rp")),r=n("q1tI"),l=n.n(r),u=n("9kvl"),s=function(t){var e=t.location.query;return l.a.createElement(a["a"],{status:e.status,title:e.title,subTitle:e.subTitle||null,extra:[l.a.createElement(i["a"],{key:"edit",type:"primary",onClick:function(){return u["b"].go(-1)}},e.primaryBtnText),l.a.createElement(i["a"],{key:"list",onClick:function(){return u["b"].push(e.listBtnUrl)}},e.listBtnText)]})};e["default"]=s}}]);