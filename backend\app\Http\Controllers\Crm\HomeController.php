<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\Controller;
use App\Models\Merchant;
use App\Models\MerchantAudit;
use App\Models\MerchantAcceptance;
use App\Models\Province;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Resources\AreaCollection;

class HomeController extends Controller
{
    /**
     * 获取首页数据
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $today = now()->startOfDay();
        $yesterday = $today->copy()->subDay();

        // 获取核心数据
        $coreData = $this->getCoreData($user, $today, $yesterday);

        // 获取待办提醒
        $todoReminders = $this->getTodoReminders($user);

        return ['status' => true, 'core_data' => $coreData, 'todo_reminders' => $todoReminders];
    }

    /**
     * 获取核心数据
     */
    private function getCoreData($user, $today, $yesterday)
    {
        // 今日数据
        $todayData = [
            'pending_submit' => Merchant::where('progress_status', 0)->where('market_manager_id', $user->id)->count(),
            'pending_audit' => Merchant::where('progress_status', 1)->where('market_manager_id', $user->id)->count(),
            'pending_acceptance' => Merchant::where('progress_status', 2)->where('market_manager_id', $user->id)->count(),
        ];

        // 昨日数据
        $yesterdayData = [
            'pending_submit' => Merchant::where('progress_status', 0)->where('market_manager_id', $user->id)
                ->whereDate('created_at', $yesterday)
                ->count(),
            'pending_audit' => Merchant::where('progress_status', 1)->where('market_manager_id', $user->id)
                ->whereDate('created_at', $yesterday)
                ->count(),
            'pending_acceptance' => Merchant::where('progress_status', 2)->where('market_manager_id', $user->id)
                ->whereDate('created_at', $yesterday)
                ->count(),
        ];

        // 计算同比变化
        $changes = [];
        foreach ($todayData as $key => $value) {
            $yesterdayValue = $yesterdayData[$key];
            $change = $value - $yesterdayValue;
            $changes[$key] = [
                'value' => $value,
                'change' => $change,
                'direction' => $change > 0 ? 'up' : ($change < 0 ? 'down' : '')
            ];
        }

        return $changes;
    }

    /**
     * 获取待办提醒
     */
    private function getTodoReminders($user)
    {
        // 待提交资料审核商户
        $pendingSubmitMerchants = Merchant::where('progress_status', 0)->where('market_manager_id', $user->id)
            ->with(['qualification'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($merchant) {
                $missingFields = $this->getMissingFields($merchant);
                return [
                    'id' => $merchant->id,
                    'merchant_name' => $merchant->merchant_name,
                    'missingFields' => $missingFields,
                    'description' => $missingFields ? '基础信息填写中断，还差' . count($missingFields) . '项' : '等待提交审核'
                ];
            });

        // 待提交验收审核商户
        $pendingAcceptanceMerchants = Merchant::where('progress_status', 2)->where('market_manager_id', $user->id)
            ->with(['acceptance'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($merchant) {
                return [
                    'id' => $merchant->id,
                    'merchant_name' => $merchant->merchant_name,
                    'description' => $merchant->acceptance ? '待提交审核' : '验收材料未上传'
                ];
            });

        // 未达标提醒
        $underperformingMerchants = Merchant::where('progress_status', 4)->where('market_manager_id', $user->id)
            ->where('daily_sales_target', '>', 0)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($merchant) {
                // 获取上线天数
                $onlineDays = now()->diffInDays($merchant->online_at);
                
                // 计算日均交易额
                $averageDailyAmount = $merchant->getDailyAvgAmount($onlineDays);

                // 如果实际日均交易额小于目标值，则视为未达标
                if ($averageDailyAmount < $merchant->daily_sales_target) {
                    return [
                        'id' => $merchant->id,
                        'merchant_name' => $merchant->merchant_name,
                        'current_value' => $averageDailyAmount,
                        'target_value' => $merchant->daily_sales_target,
                    ];
                }
                return null;
            })
            ->filter()
            ->values();

        return [
            'pending_submit' => [
                'title' => '待提交资料审核商户',
                'count' => $pendingSubmitMerchants->count(),
                'items' => $pendingSubmitMerchants
            ],
            'pending_acceptance' => [
                'title' => '待提交验收审核商户',
                'count' => $pendingAcceptanceMerchants->count(),
                'items' => $pendingAcceptanceMerchants
            ],
            'underperforming' => [
                'title' => '未达标提醒',
                'count' => $underperformingMerchants->count(),
                'items' => $underperformingMerchants
            ]
        ];
    }

    /**
     * 获取缺失字段
     */
    private function getMissingFields($merchant)
    {
        $requiredFields = [
            'merchant_name' => '商户名称',
            'business_license' => '营业执照',
            'contact_phone' => '联系电话',
            'address' => '详细地址',
            'store_photos' => '油站图片',
            'contact_person' => '负责人',
            'contact_phone' => '负责人电话',
            'contract' => '合同',
            'address' => '详细地址',
        ];

        $missingFields = [];
        foreach ($requiredFields as $field => $label) {
            if (empty($merchant->$field)) {
                $missingFields[] = $label;
            }
        }

        return $missingFields;
    }

    public function getArea(Request $request)
    {
        $data = Province::get();
        return new AreaCollection($data);
    }
}