<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MerchantAudit extends Model
{
    protected $table = 'merchant_audit';

    protected $dateFormat = 'U';

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $fillable = [
        'merchant_id',
        'audit_type',
        'audit_status',
        'submit_time',
        'audit_time',
        'reviewer_id',
        'reviewer_name',
        'comment'
    ];

    public function getCreatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getUpdatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    /**
     * 获取商户信息
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'reviewer_id', 'id');
    }
}
