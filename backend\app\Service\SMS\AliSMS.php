<?php

namespace App\Service\SMS;

use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;
use Illuminate\Support\Facades\Log;

/**
 * 阿里云短信
 */
class AliSMS extends SmsService
{
    // 短信签名
    private $_sign_name;

    // 短信模板ID
    private $_template_id;

    // 短信内容参数
    private $_params;

    // 发送的手机号码
    private $_mobile;

    // 设置短信签名
    public function setSignName($sign_name)
    {
        $this->_sign_name = $sign_name;
        return $this;
    }

    // 设置短信模板ID
    public function setTemplateId($template_id)
    {
        $this->_template_id = $template_id;
        return $this;
    }

    // 设置短信内容参数
    public function setParams($params)
    {
        $this->_params = $params;
        return $this;
    }

    // 发送短信
    public function send($mobile)
    {
        $access_key = env('ALIYUN_SMS_ACCESS_KEY_ID');
        $access_secret = env('ALIYUN_SMS_ACCESS_SECRET');
        
        if (empty($access_key)) {
            throw new \Exception('缺少access_key_id设置', 500);
        }
        if (empty($access_secret)) {
            throw new \Exception('缺少access_secret设置', 500);
        }
        AlibabaCloud::accessKeyClient($access_key, $access_secret)->regionId('cn-hangzhou')->asDefaultClient();
        try {
            $result = AlibabaCloud::rpc()
                ->product('Dysmsapi')
                ->version('2017-05-25')
                ->action('SendSms')
                ->method('POST')
                ->host('dysmsapi.aliyuncs.com')
                ->options([
                    'query' => [
                      'PhoneNumbers' => $mobile,
                      'SignName' => $this->_sign_name,
                      'TemplateCode' => $this->_template_id,
                      'TemplateParam' => json_encode($this->_params)
                    ],
                ])
                ->request();
            $data = $result->toArray();
            
            if (isset($data['Code']) && $data['Code'] == 'OK') {
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::info('验证码发送失败: ' . $e->getMessage());
            return false;
        }
    }
}
