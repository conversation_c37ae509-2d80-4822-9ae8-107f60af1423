<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Models\Store;

class StoreController extends Controller
{
    /**
     * 店铺列表
     */
    public function index(Request $request)
    {
        $model = Store::select('stor_id', 'stor_title');

        if ($request->has('search')) {
            $model = $this->addScope($model, $request->input('search', []));
        }

        $data = $model->where('stor_statu', 1)->orderBy('stor_id', 'desc')->get();

        return ['data' => $data];
    }
}
