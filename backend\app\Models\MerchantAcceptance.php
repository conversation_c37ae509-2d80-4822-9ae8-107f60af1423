<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MerchantAcceptance extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'merchant_acceptance';

    protected $dateFormat = 'U';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'has_group',
        'group_name',
        'has_device',
        'device_binding_location',
        'device_binding_latitude',
        'device_binding_longitude',
        'device_photos',
        'has_merchant_account',
        'has_merchant_app',
        'has_training',
        'training_location',
        'training_latitude',
        'training_longitude',
        'training_videos',
        'training_photos',
        'is_draft',
        'is_confirm',
        'remark'
    ];

    public $message = [
        'has_group.required' => '请选择是否建群',
        'group_name.required_if' => '群名称不能为空',
        'has_device.required' => '请选择设备是否绑定',
        'device_binding_location.required_if' => '设备绑定位置不能为空',
        'device_binding_latitude.required_if' => '设备绑定纬度不能为空',
        'device_binding_longitude.required_if' => '设备绑定经度不能为空',
        'device_photos.required_if' => '设备照片不能为空',
        'has_merchant_account.required' => '请选择商家账号是否创建',
        'has_merchant_app.required' => '请选择商家App是否下载',
        'has_training.required' => '请选择是否对商家开展培训',
        'training_location.required_if' => '培训地点不能为空',
        'training_latitude.required_if' => '培训纬度不能为空',
        'training_longitude.required_if' => '培训经度不能为空',
        'training_videos.required_if' => '培训视频不能为空',
    ];

    public function rule()
    {
        return [
            'has_group' => 'required',
            'group_name' => 'required_if:has_group,1',
            'has_device' => 'required',
            'device_binding_location' => 'required_if:has_device,1',
            'device_binding_latitude' => 'required_if:has_device,1',
            'device_binding_longitude' => 'required_if:has_device,1',
            'device_photos' => 'required_if:has_device,1',
            'has_merchant_account' => 'required',
            'has_merchant_app' => 'required',
            'has_training' => 'required',
            'training_location' => 'required_if:has_training,1',
            'training_latitude' => 'required_if:has_training,1',
            'training_longitude' => 'required_if:has_training,1',
            'training_videos' => 'required_if:has_training,1',
        ];
    }

    public function getCreatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getUpdatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getDeviceBindingTimeAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function setDeviceBindingTimeAttribute($value)
    {
        $this->attributes['device_binding_time'] = strtotime($value);
    }

    public function getTrainingTimeAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function setTrainingTimeAttribute($value)
    {
        $this->attributes['training_time'] = strtotime($value);
    }

    /**
     * 获取商户信息
     */
    public function merchant(): BelongsTo
    {
        return $this->belongsTo(Merchant::class, 'merchant_id');
    }

    /**
     * 获取验收人信息
     */
    public function inspector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }
} 