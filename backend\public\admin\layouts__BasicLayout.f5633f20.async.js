(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[4],{"2Fcx":function(e,a,t){e.exports={container:"container___1Rq3A"}},bsDN:function(e,a,t){e.exports={menu:"menu___3fMWW",right:"right___2CMz5",action:"action___3ut1O",search:"search___3FPts",account:"account___1r_Ku",avatar:"avatar___1Rx79",dark:"dark___1zu9O",name:"name___2eduw"}},eFNv:function(e,a,t){"use strict";var n=t("q1tI"),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"}}]},name:"setting",theme:"outlined"},r=c,l=t("6VBw"),o=function(e,a){return n["createElement"](l["a"],Object.assign({},e,{ref:a,icon:r}))};o.displayName="SettingOutlined";a["a"]=n["forwardRef"](o)},maEh:function(e,a,t){"use strict";t.r(a);var n=t("0Owb"),c=t("oBTY"),r=t("k1fw"),l=(t("J+/v"),t("MoRW")),o=(t("+L6B"),t("2/Rp")),u=t("Hx5s"),i=t("q1tI"),s=t.n(i),m=t("QttV"),d=t("9kvl"),h=t("HZnN"),f=(t("T2oS"),t("W9HT")),p=(t("Telt"),t("Tckk")),v=(t("lUTK"),t("BvKs")),b=t("fWQN"),g=t("mtLc"),y=t("yKVA"),E=t("879j"),_=t("eFNv"),O={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 732h-70.3c-4.8 0-9.3 2.1-12.3 5.8-7 8.5-14.5 16.7-22.4 24.5a353.84 353.84 0 01-112.7 75.9A352.8 352.8 0 01512.4 866c-47.9 0-94.3-9.4-137.9-27.8a353.84 353.84 0 01-112.7-75.9 353.28 353.28 0 01-76-112.5C167.3 606.2 158 559.9 158 512s9.4-94.2 27.8-137.8c17.8-42.1 43.4-80 76-112.5s70.5-58.1 112.7-75.9c43.6-18.4 90-27.8 137.9-27.8 47.9 0 94.3 9.3 137.9 27.8 42.2 17.8 80.1 43.4 112.7 75.9 7.9 7.9 15.3 16.1 22.4 24.5 3 3.7 7.6 5.8 12.3 5.8H868c6.3 0 10.2-7 6.7-12.3C798 160.5 663.8 81.6 511.3 82 271.7 82.6 79.6 277.1 82 516.4 84.4 751.9 276.2 942 512.4 942c152.1 0 285.7-78.8 362.3-197.7 3.4-5.3-.4-12.3-6.7-12.3zm88.9-226.3L815 393.7c-5.3-4.2-13-.4-13 6.3v76H488c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h314v76c0 6.7 7.8 10.5 13 6.3l141.9-112a8 8 0 000-12.6z"}}]},name:"logout",theme:"outlined"},N=O,j=t("6VBw"),C=function(e,a){return i["createElement"](j["a"],Object.assign({},e,{ref:a,icon:N}))};C.displayName="LogoutOutlined";var k=i["forwardRef"](C),w=(t("qVdP"),t("jsC+")),R=t("PpiC"),z=t("TSYQ"),x=t.n(z),M=t("2Fcx"),L=t.n(M),T=["overlayClassName"],A=function(e){var a=e.overlayClassName,t=Object(R["a"])(e,T);return s.a.createElement(w["a"],Object(n["a"])({overlayClassName:x()(L.a.container,a)},t))},B=A,H=t("bsDN"),F=t.n(H),I=function(e){Object(y["a"])(t,e);var a=Object(E["a"])(t);function t(){var e;Object(b["a"])(this,t);for(var n=arguments.length,c=new Array(n),r=0;r<n;r++)c[r]=arguments[r];return e=a.call.apply(a,[this].concat(c)),e.onMenuClick=function(a){var t=a.key;if("logout"!==t)"profile"==t&&d["b"].push("/".concat(t));else{var n=e.props.dispatch;n&&n({type:"login/logout"})}},e}return Object(g["a"])(t,[{key:"render",value:function(){var e=this.props,a=e.currentUser,t=void 0===a?{avatar:"",name:""}:a,n=e.menu,c=s.a.createElement(v["a"],{className:F.a.menu,selectedKeys:[],onClick:this.onMenuClick},n&&s.a.createElement(v["a"].Item,{key:"profile"},s.a.createElement(_["a"],null),"\u8d26\u53f7\u8bbe\u7f6e"),s.a.createElement(v["a"].Divider,null),s.a.createElement(v["a"].Item,{key:"logout"},s.a.createElement(k,null),"\u9000\u51fa\u767b\u5f55"));return t&&t.name?s.a.createElement(B,{overlay:c},s.a.createElement("span",{className:"".concat(F.a.action," ").concat(F.a.account)},s.a.createElement(p["a"],{size:"small",className:F.a.avatar,src:"user.png",alt:"avatar"}),s.a.createElement("span",{className:F.a.name},t.name))):s.a.createElement("span",{className:"".concat(F.a.action," ").concat(F.a.account)},s.a.createElement(f["a"],{size:"small",style:{marginLeft:8,marginRight:8}}))}}]),t}(s.a.Component),K=Object(d["a"])((function(e){var a=e.auth;return{currentUser:a.currentUser}}))(I),U=function(e){var a=e.theme,t=e.layout,n=F.a.right;return"dark"===a&&"topmenu"===t&&(n="".concat(F.a.right,"  ").concat(F.a.dark)),s.a.createElement("div",{className:n},s.a.createElement(K,{menu:!0}))},V=Object(d["a"])((function(e){var a=e.settings;return{theme:a.navTheme,layout:a.layout}}))(U),W=t("+n12"),q=(s.a.Component,s.a.createElement(l["a"],{status:403,title:"403",subTitle:"\u5bf9\u4e0d\u8d77\uff0c\u60a8\u6ca1\u6709\u8be5\u9875\u9762\u7684\u8bbf\u95ee\u6743\u9650",extra:s.a.createElement(o["a"],{type:"primary"},s.a.createElement(m["a"],{to:"/"},"\u8fd4\u56de\u9996\u9875"))})),D=function e(a){return a.map((function(a){var t=Object(r["a"])(Object(r["a"])({},a),{},{children:a.children?e(a.children):[]});return h["a"].check(a.authority,t,null)}))},J=function(e){var a=e.dispatch,t=e.children,r=e.settings,l=e.location,o=void 0===l?{pathname:"/"}:l;Object(i["useEffect"])((function(){a&&a({type:"auth/fetchCurrent"})}),[]);var d=function(e){a&&a({type:"global/changeLayoutCollapsed",payload:e})},f=Object(W["a"])(e.route.routes,o.pathname||"/")||{authority:void 0};return s.a.createElement(u["b"],Object(n["a"])({logo:null,locale:"zh-CN",menuHeaderRender:function(e,a){return s.a.createElement(m["a"],{to:"/"},e,a)},onCollapse:d,menuItemRender:function(e,a){return e.isUrl||e.children||!e.path?a:s.a.createElement(m["a"],{to:e.path},a)},breadcrumbRender:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[{path:"/",breadcrumbName:"\u9996\u9875"}].concat(Object(c["a"])(e))},itemRender:function(e,a,t,n){var c=0===t.indexOf(e);return c?s.a.createElement(m["a"],{to:n.join("/")},e.breadcrumbName):s.a.createElement("span",null,e.breadcrumbName)},menuDataRender:D,rightContentRender:function(){return s.a.createElement(V,null)}},e,r),s.a.createElement(h["a"],{authority:f.authority,noMatch:q},t))};a["default"]=Object(d["a"])((function(e){var a=e.global,t=e.settings;return{collapsed:a.collapsed,settings:t}}))(J)}}]);