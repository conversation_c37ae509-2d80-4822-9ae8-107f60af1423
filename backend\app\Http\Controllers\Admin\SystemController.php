<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Admin;
use App\Models\AdminLog;
use App\Http\Resources\BaseCollection;

class SystemController extends Controller
{
    /**
     * 管理员列表
     */
    public function user(Request $request)
    {
        $model = Admin::select('id', 'name', 'true_name', 'login_ip', 'login_time', 'last_login_ip', 'last_login_time', 'status', 'created_at');

        if ($request->has('search')) {
            $model = $this->addScope($model, $request->input('search', []));
        }

        if ($request->has('sort')) {
            $sort = $request->input('sort.order', 'desc') == 'ascend' ? 'asc' : 'desc';
            $model = $model->orderBy($request->get('sort.field', 'id'), $sort);
        }

        if ($request->input('type') == 'all') {
            return $model->where('status', 1)->get();
        }

        return new BaseCollection($model->paginate($request->input('pageSize', 10)));
    }

    /**
     * 添加管理员
     */
    public function addUser(Request $request)
    {
        $model = new Admin();

        $validator = Validator::make($request->all(), $model->rule(), $model->message);
        if ($validator->fails()) {
            return ['status' => false, 'message' => $validator->errors()->all()[0]];
        }

        $model = $model->create($request->all());

        event('admin.system.user.add');

        return ['status' => true];
    }

    /**
     * 编辑管理员
     */
    public function editUser(Request $request, Admin $admin)
    {
        $validator = Validator::make($request->all(), $admin->rule($admin), $admin->message);
        if ($validator->fails()) {
            return ['status' => false, 'message' => $validator->errors()->all()[0]];
        }

        event('admin.system.user.edit');

        $admin->update($request->all());

        return ['status' => true];
    }

    /**
     * 编辑管理员状态
     */
    public function editUserStatus(Request $request, Admin $admin)
    {
        $admin->status = $request->input('status', $admin->status);
        $admin->save();

        event('admin.system.user.edit');

        return ['status' => true];
    }

    /**
     * 删除管理员
     */
    public function deleteUser(Request $request)
    {
        $ids = $request->input('id', []);

        if (empty($ids)) {
            return ['status' => false, 'message' => '缺少参数'];
        }

        Admin::destroy($ids);

        event('admin.system.user.delete');

        return ['status' => true];
    }

    /**
     * 管理员日志
     */
    public function log(Request $request)
    {
        $model = AdminLog::with('admin');

        if ($request->has('search')) {
            $model = $this->addScope($model, $request->input('search', []));
        }

        if ($request->has('sort')) {
            $sort = $request->input('sort.order', 'desc') == 'ascend' ? 'asc' : 'desc';
            $model = $model->orderBy($request->get('sort.field', 'id'), $sort);
        } else {
            $model = $model->orderBy('created_at', 'desc');
        }

        return new BaseCollection($model->paginate($request->input('pageSize', 10)));
    }
}
