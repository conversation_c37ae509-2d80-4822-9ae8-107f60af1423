<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MerchantQualification extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'merchant_qualification';

    protected $dateFormat = 'U';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'business_license_file',
        'hazardous_license_file',
        'oil_retail_license_file',
        'legal_person_id_front_file',
        'legal_person_id_back_file',
    ];

    public $message = [
        'business_license_file.required' => '营业执照文件不能为空',
        'hazardous_license_file.required' => '危险化学品经营许可证文件不能为空',
        'oil_retail_license_file.required' => '成品油零售经营批准证书文件不能为空',
        'legal_person_id_back_file.required' => '法人身份证反面文件不能为空',
        'legal_person_id_front_file.required' => '法人身份证正面文件不能为空',
    ];

    /**
     * 获取验证规则
     */
    public function rule(Merchant $model = null)
    {
        $rules = [
            'business_license_file' => 'required',
            'hazardous_license_file' => 'required',
            'oil_retail_license_file' => 'required',
            'legal_person_id_front_file' => 'required',
            'legal_person_id_back_file' => 'required',
        ];

        return $rules;
    }

    public function getCreatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    public function getUpdatedAtAttribute($value)
    {
        if ($value == 0) return null;
        return date('Y-m-d H:i:s', strtotime($value));
    }

    /**
     * 获取商户信息
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }
} 